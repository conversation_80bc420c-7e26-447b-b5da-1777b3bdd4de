// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.
/*
const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);
*/
/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
/*
module.exports = (async () => {
  const {
    resolver: { sourceExts, assetExts },
  } = await getDefaultConfig();
 
  return {
    transformer: {
      babelTransformerPath: require.resolve('react-native-svg-transformer'),
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: false,
          inlineRequires: false,
        },
      }),
    },
    resolver: {
      assetExts: assetExts.filter(ext => ext !== 'svg'),
      sourceExts: [...sourceExts, 'svg'],
    },
  };
 })();*/

 /**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

 const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

 const obfuscatorIoMetroPlugin = require('obfuscator-io-metro-plugin')(
  {
    // Obfuscator options (refer to javascript-obfuscator library for more options)
    compact: false,
    sourceMap: false, // Source map generation is disabled
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 1,
    numbersToExpressions: true,
    simplify: true,
    stringArrayShuffle: true,
    splitStrings: true,
    stringArrayThreshold: 1,
  },
  {
    runInDev: false, // Optional: Disable obfuscation in development mode
    logObfuscatedFiles: false, // Optional: Log obfuscated files to ./.jso
  }
 );

 const defaultConfig = getDefaultConfig(__dirname);
 
 const {
   resolver: { sourceExts, assetExts },
 } = getDefaultConfig(__dirname);
 
 const config = {
   transformer: {
     getTransformOptions: async () => ({
       transform: {
         experimentalImportSupport: false,
         inlineRequires: true,
       },
     }),
     babelTransformerPath: require.resolve('react-native-svg-transformer'),
     // Enable minification for production builds
     minifierPath: 'metro-minify-terser',
     minifierConfig: {
       // Aggressive minification for smaller bundle size
       mangle: {
         keep_fnames: true,
       },
       compress: {
         drop_console: true, // Remove console.log statements
         drop_debugger: true,
         pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn'],
       },
       output: {
         comments: false, // Remove comments
         ascii_only: true,
       },
     },
   },
   resolver: {
     assetExts: assetExts.filter(ext => ext !== 'svg'),
     sourceExts: [...sourceExts, 'svg'],
     // Enable tree shaking by resolving only used modules
     platforms: ['ios', 'android', 'native', 'web'],
   },
   serializer: {
     // Optimize bundle output
     getModulesRunBeforeMainModule: () => [],
     // Enable tree shaking
     getPolyfills: () => [],
   },
 };
 
 module.exports = mergeConfig(defaultConfig, config, obfuscatorIoMetroPlugin);