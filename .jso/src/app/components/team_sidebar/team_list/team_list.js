  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = TeamList;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _team_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  //import { TeamSelector } from '@app/screens/convert_gm_to_channel/team_selector';

  var keyExtractor = function keyExtractor(item) {
    return item.id;
  };
  function TeamList(_ref) {
    var myOrderedTeams = _ref.myOrderedTeams,
      testID = _ref.testID,
      _ref$isFromHome = _ref.isFromHome,
      isFromHome = _ref$isFromHome === undefined ? false : _ref$isFromHome,
      curentTeam = _ref.curentTeam;
    var renderTeam = function renderTeam(_ref2) {
      var t = _ref2.item;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_team_item.default, {
        myTeam: t
      });
    };
    var currentTeam = myOrderedTeams.find(function (x) {
      return x.id == curentTeam;
    });
    if (isFromHome && currentTeam !== undefined) return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_team_item.default, {
        isFromHome: true,
        myTeam: currentTeam
      })
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: (myOrderedTeams == null ? undefined : myOrderedTeams.length) > 0 && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        bounces: false,
        contentContainerStyle: styles.contentContainer,
        data: myOrderedTeams,
        fadingEdgeLength: 30,
        keyExtractor: keyExtractor,
        renderItem: renderTeam,
        showsVerticalScrollIndicator: false,
        testID: `${testID}.flat_list`
      })
    });
  }
  var styles = _reactNative.StyleSheet.create({
    container: {
      // flexShrink: 1,
    },
    contentContainer: {
      // alignItems: 'center',
      // marginVertical: 6,
      // paddingBottom: 10,
    }
  });
