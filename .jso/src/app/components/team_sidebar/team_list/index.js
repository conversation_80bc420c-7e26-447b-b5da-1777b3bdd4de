  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _team_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  /* eslint-disable max-nested-callbacks */

  var withTeams = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var database = _ref.database;
    var myTeams = (0, _$$_REQUIRE(_dependencyMap[4]).queryMyTeams)(database).observe();
    var teamIds = (0, _$$_REQUIRE(_dependencyMap[4]).queryJoinedTeams)(database).observe().pipe((0, _$$_REQUIRE(_dependencyMap[5]).map)(function (ts) {
      return ts.map(function (t) {
        return {
          id: t.id,
          displayName: t.displayName
        };
      });
    }));
    var order = (0, _$$_REQUIRE(_dependencyMap[6]).queryPreferencesByCategoryAndName)(database, _$$_REQUIRE(_dependencyMap[7]).Preferences.CATEGORIES.TEAMS_ORDER).observeWithColumns(['value']).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (p) {
      return p.length ? (0, _$$_REQUIRE(_dependencyMap[8]).of)(p[0].value.split(',')) : (0, _$$_REQUIRE(_dependencyMap[8]).of)([]);
    }));
    var myOrderedTeams = (0, _$$_REQUIRE(_dependencyMap[8]).combineLatest)([myTeams, order, teamIds]).pipe((0, _$$_REQUIRE(_dependencyMap[5]).map)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 3),
        ts = _ref3[0],
        o = _ref3[1],
        tids = _ref3[2];
      var ids = o;
      if (!o.length) {
        ids = tids.sort(function (a, b) {
          return a.displayName.toLocaleLowerCase().localeCompare(b.displayName.toLocaleLowerCase());
        }).map(function (t) {
          return t.id;
        });
      }
      var indexes = {};
      var originalIndexes = {};
      ids.forEach(function (v, i) {
        indexes[v] = i;
      });
      ts.forEach(function (t, i) {
        originalIndexes[t.id] = i;
      });
      return ts.sort(function (a, b) {
        if (indexes[a.id] != null || indexes[b.id] != null) {
          var _indexes$a$id, _indexes$b$id;
          return ((_indexes$a$id = indexes[a.id]) != null ? _indexes$a$id : tids.length) - ((_indexes$b$id = indexes[b.id]) != null ? _indexes$b$id : tids.length);
        }
        return originalIndexes[a.id] - originalIndexes[b.id];
      });
    }));
    var curentTeam = (0, _$$_REQUIRE(_dependencyMap[9]).observeCurrentTeamId)(database).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (ctid) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(ctid);
    }));
    return {
      curentTeam: curentTeam,
      myOrderedTeams: myOrderedTeams
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(withTeams(_team_list.default));
