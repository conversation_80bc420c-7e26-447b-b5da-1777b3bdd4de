  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = TeamItem;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _performance_metrics_manager = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _team_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        height: 50,
        width: 50,
        flex: 0,
        padding: 3,
        borderRadius: 10,
        marginVertical: 3,
        overflow: 'hidden'
      },
      containerFromHome: {
        height: 31,
        width: 33,
        flex: 0,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.buttonBg, 0.15)
        // padding: 3,
        //borderRadius: 10,
        //marginVertical: 3,
        //overflow: 'hidden',
      },
      containerSelected: {
        borderWidth: 3,
        borderRadius: 14,
        borderColor: theme.sidebarTextActiveBorder
      },
      containerHomeSelected: {
        borderWidth: 1,
        borderRadius: 8,
        borderColor: theme.buttonBg
      },
      unread: {
        left: 43,
        top: 3
      },
      mentionsOneDigit: {
        top: 1,
        left: 31
      },
      mentionsTwoDigits: {
        top: 1,
        left: 30
      },
      mentionsThreeDigits: {
        top: 1,
        left: 28
      }
    };
  });
  function TeamItem(_ref) {
    var team = _ref.team,
      hasUnreads = _ref.hasUnreads,
      mentionCount = _ref.mentionCount,
      selected = _ref.selected,
      _ref$isFromHome = _ref.isFromHome,
      isFromHome = _ref$isFromHome === undefined ? false : _ref$isFromHome;
    var theme = (0, _$$_REQUIRE(_dependencyMap[8]).useTheme)();
    var styles = getStyleSheet(theme);
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[9]).useServerUrl)();
    var onPress = (0, _react.useCallback)(function () {
      if (!team || selected) {
        return;
      }
      _performance_metrics_manager.default.startMetric('mobile_team_switch');
      (0, _$$_REQUIRE(_dependencyMap[10]).handleTeamChange)(serverUrl, team.id);
      (0, _$$_REQUIRE(_dependencyMap[11]).dismissBottomSheet)();
    }, [selected, team == null ? undefined : team.id, serverUrl]);
    if (!team) {
      return null;
    }
    var hasBadge = Boolean(mentionCount || hasUnreads);
    var badgeStyle = styles.unread;
    var value = mentionCount;
    if (!mentionCount && hasUnreads) {
      value = -1;
    }
    switch (true) {
      case value > 99:
        badgeStyle = styles.mentionsThreeDigits;
        break;
      case value > 9:
        badgeStyle = styles.mentionsTwoDigits;
        break;
      case value > 0:
        badgeStyle = styles.mentionsOneDigit;
        break;
    }
    var windowWidth = _reactNative.Dimensions.get('window').width;
    var teamItem = `team_sidebar.team_list.team_item.${team.id}`;
    var teamItemTestId = selected ? `${teamItem}.selected` : `${teamItem}.not_selected`;
    if (isFromHome) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.containerFromHome, styles.containerHomeSelected],
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_team_icon.default, {
          isFromHome: isFromHome,
          displayName: team.displayName,
          id: team.id,
          lastIconUpdate: team.lastTeamIconUpdatedAt,
          selected: selected,
          testID: `${teamItem}.team_icon`
        })
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
      onPress: isFromHome === false && onPress,
      type: "opacity",
      testID: teamItemTestId,
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: {
          display: 'flex',
          flexDirection: 'row',
          width: windowWidth,
          alignItems: 'center'
        },
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [styles.container, selected ? styles.containerSelected : undefined],
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_team_icon.default, {
            displayName: team.displayName,
            id: team.id,
            lastIconUpdate: team.lastTeamIconUpdatedAt,
            selected: selected,
            testID: `${teamItem}.team_icon`
          })
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[12]).typography)('Heading', 200, 'Light'), {
            marginStart: 5,
            color: theme.sidebarText
          }),
          children: team.displayName
        })]
      })
    });
  }
