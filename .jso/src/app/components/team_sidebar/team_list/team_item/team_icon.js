  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = TeamIcon;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _network_manager = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.buttonBg, 0.15),
        borderRadius: 8
      },
      containerSelected: {
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.buttonBg, 0.15),
        borderRadius: 8
      },
      text: {
        color: theme.sidebarText,
        textTransform: 'uppercase'
      },
      nameOnly: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.buttonBg, 0.15)
      }
    };
  });
  function TeamIcon(_ref) {
    var id = _ref.id,
      lastIconUpdate = _ref.lastIconUpdate,
      displayName = _ref.displayName,
      selected = _ref.selected,
      _ref$smallText = _ref.smallText,
      smallText = _ref$smallText === undefined ? false : _ref$smallText,
      textColor = _ref.textColor,
      backgroundColor = _ref.backgroundColor,
      testID = _ref.testID,
      _ref$isFromHome = _ref.isFromHome,
      isFromHome = _ref$isFromHome === undefined ? false : _ref$isFromHome;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      imageError = _useState2[0],
      setImageError = _useState2[1];
    var ref = (0, _react.useRef)(null);
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var styles = getStyleSheet(theme);
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[8]).useServerUrl)();
    var client = null;
    try {
      client = _network_manager.default.getClient(serverUrl);
    } catch (err) {
      // Do nothing
    }
    (0, _react.useEffect)(function () {
      return setImageError(false);
    }, [id, lastIconUpdate]);
    var handleImageError = (0, _react.useCallback)(function () {
      if (ref.current) {
        setImageError(true);
      }
    }, []);
    var nameOnly = imageError || !lastIconUpdate || !client;
    var containerStyle = (0, _react.useMemo)(function () {
      if (selected) {
        return backgroundColor ? [styles.containerSelected, {
          backgroundColor: backgroundColor
        }] : [styles.containerSelected, nameOnly && styles.nameOnly];
      }
      return backgroundColor ? [styles.container, {
        backgroundColor: backgroundColor
      }] : [styles.container, nameOnly && styles.nameOnly];
    }, [styles, backgroundColor, selected, nameOnly]);
    var textTypography = (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', smallText ? 100 : 200, 'SemiBold');
    textTypography.fontFamily = 'Metropolis-SemiBold';
    var teamIconContent;
    if (nameOnly) {
      var textStyle = [styles.text, textTypography, Boolean(textColor) && {
        color: textColor
      }];
      teamIconContent = /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: [textStyle, {
          fontFamily: "IBMPlexSansArabic-Light"
        }],
        testID: `${testID}.display_name_abbreviation`,
        children: displayName.substring(0, 2)
      });
    } else {
      teamIconContent = /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[10]).Image, {
        style: {
          borderRadius: 8,
          position: 'absolute',
          // top: isFromHome?0:undefined,
          //bottom:isFromHome?0:undefined,
          //left: isFromHome?0:undefined,
          // right: isFromHome?0:undefined,
          backgroundColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.buttonBg, 0.15),
          resizeMode: 'contain',
          height: isFromHome ? 20 : 30,
          width: isFromHome ? 20 : 30
        },
        source: {
          uri: (0, _$$_REQUIRE(_dependencyMap[11]).buildAbsoluteUrl)(serverUrl, (0, _$$_REQUIRE(_dependencyMap[12]).buildTeamIconUrl)(serverUrl, id, lastIconUpdate))
        },
        onError: handleImageError
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: containerStyle,
      ref: ref,
      testID: testID,
      children: teamIconContent
    });
  }
