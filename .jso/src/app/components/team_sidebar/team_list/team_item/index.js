  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _team_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhance = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)(['myTeam'], function (_ref) {
    var myTeam = _ref.myTeam,
      database = _ref.database;
    var myChannels = (0, _$$_REQUIRE(_dependencyMap[4]).queryMyChannelsByTeam)(database, myTeam.id).observeWithColumns(['mentions_count', 'is_unread']);
    var notifyProps = (0, _$$_REQUIRE(_dependencyMap[4]).observeAllMyChannelNotifyProps)(database);
    var hasUnreads = myChannels.pipe((0, _$$_REQUIRE(_dependencyMap[5]).combineLatestWith)(notifyProps),
    // eslint-disable-next-line max-nested-callbacks
    (0, _$$_REQUIRE(_dependencyMap[5]).map)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        mycs = _ref3[0],
        notify = _ref3[1];
      return mycs.reduce(function (acc, v) {
        var _notify$v$id;
        var isMuted = (notify == null ? undefined : (_notify$v$id = notify[v.id]) == null ? undefined : _notify$v$id.mark_unread) === 'mention';
        return acc || v.isUnread && !isMuted;
      }, false);
    }));
    var selected = (0, _$$_REQUIRE(_dependencyMap[6]).observeCurrentTeamId)(database).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (ctid) {
      return (0, _$$_REQUIRE(_dependencyMap[7]).of)(ctid === myTeam.id);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());
    return {
      selected: selected,
      team: (0, _$$_REQUIRE(_dependencyMap[8]).observeTeam)(database, myTeam.id),
      mentionCount: (0, _$$_REQUIRE(_dependencyMap[8]).observeMentionCount)(database, myTeam.id, false),
      hasUnreads: hasUnreads
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhance(_team_item.default));
