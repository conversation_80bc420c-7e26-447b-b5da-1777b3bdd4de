  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _users_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['userIds'], function (_ref) {
    var database = _ref.database,
      userIds = _ref.userIds;
    return {
      users: (0, _$$_REQUIRE(_dependencyMap[3]).queryUsersById)(database, userIds).observe()
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_users_list.default));
