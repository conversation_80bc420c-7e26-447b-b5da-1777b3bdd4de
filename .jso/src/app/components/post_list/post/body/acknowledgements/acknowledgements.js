  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _users_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[8]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        alignItems: 'center',
        borderRadius: 4,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.onlineIndicator, 0.12),
        flexDirection: 'row',
        height: 32,
        justifyContent: 'center',
        paddingHorizontal: 8
      },
      containerActive: {
        backgroundColor: theme.onlineIndicator
      },
      text: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 100, 'SemiBold'), {
        color: theme.onlineIndicator
      }),
      textActive: {
        color: '#fff'
      },
      icon: {
        marginRight: 4
      },
      divider: {
        width: 1,
        height: 32,
        marginHorizontal: 8,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.centerChannelColor, 0.16)
      },
      listHeaderText: Object.assign({
        marginBottom: 12,
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 600, 'SemiBold'))
    };
  });
  var Acknowledgements = function Acknowledgements(_ref) {
    var _post$metadata;
    var currentUserId = _ref.currentUserId,
      currentUserTimezone = _ref.currentUserTimezone,
      hasReactions = _ref.hasReactions,
      location = _ref.location,
      post = _ref.post,
      theme = _ref.theme,
      iconColor = _ref.iconColor,
      _ref$iconSize = _ref.iconSize,
      iconSize = _ref$iconSize === undefined ? 20 : _ref$iconSize;
    var intl = (0, _$$_REQUIRE(_dependencyMap[10]).useIntl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[11]).useIsTablet)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[12]).useServerUrl)();
    var _useWindowDimensions = (0, _reactNative.useWindowDimensions)(),
      height = _useWindowDimensions.height;
    var _useSafeAreaInsets = (0, _$$_REQUIRE(_dependencyMap[13]).useSafeAreaInsets)(),
      bottom = _useSafeAreaInsets.bottom;
    var style = getStyleSheet(theme);
    var isCurrentAuthor = post.userId === currentUserId;
    var acknowledgements = ((_post$metadata = post.metadata) == null ? undefined : _post$metadata.acknowledgements) || [];
    var acknowledgedAt = (0, _react.useMemo)(function () {
      if (acknowledgements.length > 0) {
        var ack = acknowledgements.find(function (item) {
          return item.user_id === currentUserId;
        });
        if (ack) {
          return ack.acknowledged_at;
        }
      }
      return 0;
    }, [acknowledgements]);
    var handleOnPress = (0, _react.useCallback)(function () {
      // Debug log removed to prevent React Native text rendering errors
      if (acknowledgedAt && (0, _$$_REQUIRE(_dependencyMap[14]).moreThan5minAgo)(acknowledgedAt) || isCurrentAuthor) {
        return;
      }
      if (acknowledgedAt) {
        (0, _$$_REQUIRE(_dependencyMap[15]).unacknowledgePost)(serverUrl, post.id);
      } else {
        (0, _$$_REQUIRE(_dependencyMap[15]).acknowledgePost)(serverUrl, post.id);
      }
    }, [acknowledgedAt, isCurrentAuthor, post.id, serverUrl]);
    (0, _react.useEffect)(function () {
      if (!acknowledgedAt) {
        (0, _$$_REQUIRE(_dependencyMap[15]).acknowledgePost)(serverUrl, post.id);
      }
      // handleOnPress
    }, [!acknowledgedAt]);

    // useEffect(() => {
    //     if ((acknowledgedAt && moreThan5minAgo(acknowledgedAt)) || isCurrentAuthor) {
    //         return;
    //     }
    //     if (acknowledgedAt) {
    //         unacknowledgePost(serverUrl, post.id);
    //     } else {
    //         acknowledgePost(serverUrl, post.id);
    //     }
    // }, [acknowledgedAt, isCurrentAuthor, post.id, serverUrl]);

    //     useEffect(() => {
    //  console.log(`\n\nthis the acknolowdgment request function 
    //             ${JSON.stringify(post.metadata?.acknowledgements)}
    //             ${JSON.stringify(post.message)}
    //             \n\n`);

    //     },[post.metadata?.acknowledgements])

    var handleOnLongPress = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!acknowledgements.length) {
        return;
      }
      var userAcknowledgements = {};
      var userIds = [];
      acknowledgements.forEach(function (item) {
        userAcknowledgements[item.user_id] = item.acknowledged_at;
        userIds.push(item.user_id);
      });
      try {
        (0, _$$_REQUIRE(_dependencyMap[16]).fetchMissingProfilesByIds)(serverUrl, userIds);
      } catch (e) {
        return;
      }
      var renderContent = function renderContent() {
        return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [!isTablet && /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
            id: "mobile.acknowledgements.header",
            defaultMessage: 'Acknowledgements',
            style: style.listHeaderText
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_users_list.default, {
            channelId: post.channelId,
            location: location,
            userAcknowledgements: userAcknowledgements,
            userIds: userIds,
            timezone: currentUserTimezone || undefined
          })]
        });
      };
      var snapPoint1 = (0, _$$_REQUIRE(_dependencyMap[17]).bottomSheetSnapPoint)(Math.min(userIds.length, 5), _$$_REQUIRE(_dependencyMap[18]).USER_ROW_HEIGHT, bottom) + _$$_REQUIRE(_dependencyMap[19]).TITLE_HEIGHT;
      var snapPoint2 = height * 0.8;
      var snapPoints = [1, Math.min(snapPoint1, snapPoint2)];
      if (userIds.length > 5 && snapPoint1 < snapPoint2) {
        snapPoints.push(snapPoint2);
      }
      (0, _$$_REQUIRE(_dependencyMap[20]).bottomSheet)({
        closeButtonId: 'close-ack-users-list',
        renderContent: renderContent,
        initialSnapIndex: 1,
        snapPoints: snapPoints,
        title: intl.formatMessage({
          id: 'mobile.acknowledgements.header',
          defaultMessage: 'Acknowledgements'
        }),
        theme: theme
      });
    }), [bottom, intl, isTablet, acknowledgements, theme, location, post.channelId, currentUserTimezone]);

    // Only show check marks for messages sent by the current user
    // Hide check marks for received messages (messages from other users)
    if (!isCurrentAuthor) {
      return null;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity
      // onPress={handleOnPress}
      , {
        onLongPress: handleOnLongPress
        // style={[style.container, acknowledgedAt ? style.containerActive : undefined] }
        ,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          color: iconColor,
          name: acknowledgements.length > 1 ? 'check-all' : 'check',
          size: iconSize,
          style: {}
        })
      })
    });
  };
  var _default = exports.default = Acknowledgements;
