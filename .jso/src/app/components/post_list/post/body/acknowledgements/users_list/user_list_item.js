  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.USER_ROW_HEIGHT = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _formatted_relative_time = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _user_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var USER_ROW_HEIGHT = exports.USER_ROW_HEIGHT = 60;
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        paddingLeft: 0,
        height: USER_ROW_HEIGHT
      },
      pictureContainer: {
        alignItems: 'flex-start',
        width: 40
      },
      time: Object.assign({
        position: 'absolute',
        right: -30,
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.64)
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 75))
    };
  });
  var UserListItem = function UserListItem(_ref) {
    var channelId = _ref.channelId,
      location = _ref.location,
      timezone = _ref.timezone,
      user = _ref.user,
      userAcknowledgement = _ref.userAcknowledgement;
    var intl = (0, _$$_REQUIRE(_dependencyMap[9]).useIntl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var style = getStyleSheet(theme);
    var handleUserPress = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (userProfile) {
        if (userProfile) {
          yield (0, _$$_REQUIRE(_dependencyMap[11]).dismissBottomSheet)(_$$_REQUIRE(_dependencyMap[12]).Screens.BOTTOM_SHEET);
          var screen = _$$_REQUIRE(_dependencyMap[12]).Screens.USER_PROFILE;
          var title = intl.formatMessage({
            id: 'mobile.routes.user_profile',
            defaultMessage: 'Profile'
          });
          var closeButtonId = 'close-user-profile';
          var props = {
            closeButtonId: closeButtonId,
            location: location,
            userId: userProfile.id,
            channelId: channelId
          };
          _reactNative.Keyboard.dismiss();
          (0, _$$_REQUIRE(_dependencyMap[11]).openAsBottomSheet)({
            screen: screen,
            title: title,
            theme: theme,
            closeButtonId: closeButtonId,
            props: props
          });
        }
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }(), [channelId, location]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_user_item.default, {
      FooterComponent: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_relative_time.default, {
        value: userAcknowledgement,
        timezone: timezone,
        style: style.time
      }),
      containerStyle: style.container,
      onUserPress: handleUserPress,
      size: 40,
      user: user
    });
  };
  var _default = exports.default = UserListItem;
