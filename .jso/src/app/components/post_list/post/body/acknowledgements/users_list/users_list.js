  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _user_list_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var UsersList = function UsersList(_ref) {
    var channelId = _ref.channelId,
      location = _ref.location,
      users = _ref.users,
      userAcknowledgements = _ref.userAcknowledgements,
      timezone = _ref.timezone;
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[5]).useIsTablet)();
    var listRef = (0, _react.useRef)(null);
    var renderItem = (0, _react.useCallback)(function (_ref2) {
      var item = _ref2.item;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_user_list_item.default, {
        channelId: channelId,
        location: location,
        user: item,
        userAcknowledgement: userAcknowledgements[item.id],
        timezone: timezone
      });
    }, [channelId, location, timezone]);
    if ((users == null ? undefined : users.length) <= 0) return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {});
    if (isTablet) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[6]).FlatList, {
        data: users,
        ref: listRef,
        renderItem: renderItem,
        overScrollMode: 'always'
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[7]).BottomSheetFlatList, {
      data: users,
      renderItem: renderItem,
      overScrollMode: 'always'
    });
  };
  var _default = exports.default = UsersList;
