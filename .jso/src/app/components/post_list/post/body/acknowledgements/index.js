  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _compose = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _acknowledgements = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (ownProps) {
    var database = ownProps.database;
    var currentUser = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentUser)(database);
    return {
      currentUserId: currentUser.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (c) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(c == null ? undefined : c.id);
      })),
      currentUserTimezone: currentUser.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (c) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(c == null ? undefined : c.timezone);
      }))
    };
  });
  var _default = exports.default = (0, _compose.default)(_$$_REQUIRE(_dependencyMap[3]).withDatabase, enhanced)(_acknowledgements.default);
