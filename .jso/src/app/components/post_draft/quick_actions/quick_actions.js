  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = QuickActions;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _camera_quick_action = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _file_quick_action = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _image_quick_action = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _input_quick_action = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _post_priority_action = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _location_quick_action = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var windowWidth = _reactNative.Dimensions.get('window').width;
  var style = _reactNative.StyleSheet.create({
    quickActionsContainer: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-evenly',
      width: "100%",
      height: 44,
      marginTop: 5
    }
  });
  function QuickActions(_ref) {
    var testID = _ref.testID,
      canUploadFiles = _ref.canUploadFiles,
      value = _ref.value,
      fileCount = _ref.fileCount,
      currentUserId = _ref.currentUserId,
      isPostPriorityEnabled = _ref.isPostPriorityEnabled,
      canShowPostPriority = _ref.canShowPostPriority,
      maxFileCount = _ref.maxFileCount,
      updateValue = _ref.updateValue,
      addFiles = _ref.addFiles,
      postPriority = _ref.postPriority,
      updatePostPriority = _ref.updatePostPriority,
      focus = _ref.focus,
      channelID = _ref.channelID,
      _ref$channelTyp = _ref.channelTyp,
      channelTyp = _ref$channelTyp === undefined ? undefined : _ref$channelTyp,
      _ref$groupCallsAllowe = _ref.groupCallsAllowed,
      groupCallsAllowed = _ref$groupCallsAllowe === undefined ? false : _ref$groupCallsAllowe,
      onChannelQuickAction = _ref.onChannelQuickAction;
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var atDisabled = value[value.length - 1] === '@';
    var slashDisabled = value.length > 0;
    var atInputActionTestID = `${testID}.at_input_action`;
    var locationActionTestID = `${testID}.location`;
    var slashInputActionTestID = `${testID}.slash_input_action`;
    var fileActionTestID = `${testID}.file_action`;
    var imageActionTestID = `${testID}.image_action`;
    var cameraActionTestID = `${testID}.camera_action`;
    var postPriorityActionTestID = `${testID}.post_priority_action`;
    var uploadProps = {
      disabled: !canUploadFiles,
      fileCount: fileCount,
      maxFileCount: maxFileCount,
      maxFilesReached: fileCount >= maxFileCount,
      onUploadFiles: addFiles
    };
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      testID: testID,
      style: style.quickActionsContainer,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_post_priority_action.default, {
        testID: postPriorityActionTestID,
        postPriority: postPriority,
        updatePostPriority: updatePostPriority
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_camera_quick_action.default, Object.assign({
        testID: cameraActionTestID
      }, uploadProps)), /*#__PURE__*/(0, _jsxRuntime.jsx)(_image_quick_action.default, Object.assign({
        testID: imageActionTestID
      }, uploadProps)), /*#__PURE__*/(0, _jsxRuntime.jsx)(_file_quick_action.default, Object.assign({
        testID: fileActionTestID
      }, uploadProps)), /*#__PURE__*/(0, _jsxRuntime.jsx)(_input_quick_action.default, {
        testID: slashInputActionTestID,
        disabled: slashDisabled,
        inputType: "slash",
        updateValue: updateValue,
        focus: focus
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_input_quick_action.default, {
        testID: atInputActionTestID,
        disabled: atDisabled,
        inputType: "at",
        updateValue: updateValue,
        focus: focus
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_location_quick_action.default, {
        testID: locationActionTestID,
        channelID: channelID,
        currentUserId: currentUserId
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: {
          flexGrow: 1,
          alignItems: 'center',
          justifyContent: 'center'
        },
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[11]).TouchableOpacity, {
          onPress: onChannelQuickAction,
          style: {
            paddingTop: 0
          },
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[12]).EllipsisHorizontalCircleIcon, {
            color: (0, _$$_REQUIRE(_dependencyMap[13]).changeOpacity)(theme.sidebarText, 0.65),
            size: 25
          })
        })
      })]
    });
  }
