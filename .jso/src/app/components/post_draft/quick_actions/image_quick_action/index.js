  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = ImageQuickAction;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _file_picker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _alhashediReactNativeImageEditor = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNativeFs = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  // import PhotoEditor from 'react-native-photo-editor';
  // import ImageEditor from '@thienmd/react-native-image-editor'

  // import ImageEditor from '@ezz/react-native-image-editor'

  var style = _reactNative.StyleSheet.create({
    icon: {
      alignItems: 'center',
      justifyContent: 'center',
      flexGrow: 1
    }
  });
  function ImageQuickAction(_ref) {
    var disabled = _ref.disabled,
      _ref$fileCount = _ref.fileCount,
      fileCount = _ref$fileCount === undefined ? 0 : _ref$fileCount,
      onUploadFiles = _ref.onUploadFiles,
      maxFilesReached = _ref.maxFilesReached,
      maxFileCount = _ref.maxFileCount,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? '' : _ref$testID;
    var intl = (0, _$$_REQUIRE(_dependencyMap[8]).useIntl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[9]).useTheme)();
    var _useAlert = (0, _$$_REQUIRE(_dependencyMap[10]).useAlert)(),
      showAlert = _useAlert.showAlert;
    var handleButtonPress = (0, _react.useCallback)(function () {
      if (maxFilesReached) {
        showAlert(intl.formatMessage({
          id: 'mobile.link.error.title',
          defaultMessage: 'Error'
        }), (0, _$$_REQUIRE(_dependencyMap[11]).fileMaxWarning)(intl, maxFileCount));
        return;
      }
      try {
        var picker = new _file_picker.default(intl, function (files) {
          try {
            if (files && (files == null ? undefined : files.length) > 0 && files[0].mime_type.split('/')[0] === 'image') {
              console.log('this from edite useEffect');
              var fileName = files[0].localPath;
              var file = new _file_picker.default(intl, onUploadFiles);
              _alhashediReactNativeImageEditor.default.Edit({
                path: fileName == null ? undefined : fileName.substring(7, fileName.length),
                hiddenControls: ['save'],
                onDone: function onDone(imagePath) {
                  try {
                    console.log(`\n\nthis the file path ${imagePath}\n\n`);
                    _reactNativeFs.default.exists(imagePath).then(function (exists) {
                      console.log('image path', exists);
                    });
                    var fileNameToList = imagePath.split('/');
                    console.log(fileNameToList);
                    var filepath = `file://${imagePath}`;
                    var imagePicker = {
                      uri: filepath,
                      name: fileNameToList[fileNameToList.length - 1],
                      copyError: null,
                      fileCopyUri: null,
                      type: fileNameToList[fileNameToList.length - 1].split('.')[1],
                      size: undefined
                    };
                    console.log('image path', imagePath);
                    console.log('file name', fileNameToList[fileNameToList.length - 1]);
                    file.prepareFileUploadAudio(imagePicker)
                    // .then(() => {
                    // deleteImageFile(filepath) 
                    //  })
                    .catch(function (err) {
                      return console.error('error is this ', err);
                    });
                  } catch (error) {
                    console.error('PhotoEditor Exception:', error);
                  }
                },
                onCancel: function onCancel() {
                  console.log('Editing cancelled by user.');
                },
                onError: function onError(error) {
                  console.error('PhotoEditor Error:', error);
                }
              });
              //   const file = new FilePickerUtil(intl, onUploadFiles);
              //                             const filesPath: string[] = [];

              //                             for (let index = 0; index < files.length; index++) {
              //                                 const fileName = files![index].localPath;
              //                                 if (fileName !== undefined)
              //                                     filesPath.push(fileName.substring(7, fileName.length))
              //                             }

              //                                ImageEditor.Edit({
              //                                   path: filesPath,
              //                                   hiddenControls: ['save'],
              //                                   onDone: (imagePath: string) => {
              //                                       try {
              //                                           RNFS.exists(imagePath).then((exists) => {
              //                                               console.log('image path', exists);

              //                                           })
              //                                           const fileNameToList = imagePath.split('/');
              //                                           console.log(fileNameToList);
              //                                           const filepath = `file://${imagePath}`
              //                                           const imagePicker = {

              //                                               uri: filepath,
              //                                               name: fileNameToList[fileNameToList.length - 1],
              //                                               copyError: null,
              //                                               fileCopyUri: null,
              //                                               type: fileNameToList[fileNameToList.length - 1].split('.')[1],
              //                                               size: undefined,
              //                                           } as unknown as DocumentPickerResponse;
              //                                           console.log('image path', imagePath);
              //                                           console.log('file name', fileNameToList[fileNameToList.length - 1]);

              //                                           file.prepareFileUploadAudio(imagePicker)
              //                                              // .then(() => {
              //                                                   // deleteImageFile(filepath) 
              //                                                 //  })

              //                                               .catch(
              //                                                   (err) => console.error('error is this ', err));

              //                                       } catch (error) {
              //                                           console.error('PhotoEditor Exception:', error);
              //                                       }

              //                                   },
              //                                   onCancel: () => {
              //                                       console.log('Editing cancelled by user.');
              //                                   },
              //                                   onError: (error: string) => {
              //                                       console.error('PhotoEditor Error:', error);
              //                                   },
              //                               } as any);
            } else {
              onUploadFiles(files);
            }
          } catch (error) {
            console.error('PhotoEditor Exception:', error);
          }
        });
        picker.attachImageFromPhotoGallery(maxFileCount - fileCount);
      } catch (err) {
        console.log(`\n\n\n\nthis error from attach file from photogallery${err}\n\n\n`);
      }
    }, [onUploadFiles, fileCount, maxFileCount]);
    var actionTestID = disabled ? `${testID}.disabled` : testID;
    var color = disabled ? (0, _$$_REQUIRE(_dependencyMap[12]).changeOpacity)(theme.centerChannelColor, 0.16) : (0, _$$_REQUIRE(_dependencyMap[12]).changeOpacity)(theme.centerChannelColor, 0.64);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
      testID: actionTestID,
      disabled: disabled,
      onPress: handleButtonPress,
      style: style.icon,
      type: 'opacity',
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[13]).PhotoIcon, {
        color: color,
        size: _$$_REQUIRE(_dependencyMap[14]).ICON_SIZE
      })
    });
  }
