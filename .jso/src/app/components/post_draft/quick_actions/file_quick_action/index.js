  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = FileQuickAction;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _file_picker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var style = _reactNative.StyleSheet.create({
    icon: {
      alignItems: 'center',
      justifyContent: 'center',
      flexGrow: 1
    }
  });
  function FileQuickAction(_ref) {
    var disabled = _ref.disabled,
      onUploadFiles = _ref.onUploadFiles,
      maxFilesReached = _ref.maxFilesReached,
      maxFileCount = _ref.maxFileCount,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? '' : _ref$testID;
    var intl = (0, _$$_REQUIRE(_dependencyMap[6]).useIntl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var _useAlert = (0, _$$_REQUIRE(_dependencyMap[8]).useAlert)(),
      showAlert = _useAlert.showAlert;
    var handleButtonPress = (0, _react.useCallback)(function () {
      if (maxFilesReached) {
        showAlert(intl.formatMessage({
          id: 'mobile.link.error.title',
          defaultMessage: 'Error'
        }), (0, _$$_REQUIRE(_dependencyMap[9]).fileMaxWarning)(intl, maxFileCount));
        return;
      }
      var picker = new _file_picker.default(intl, onUploadFiles);
      picker.attachFileFromFiles(undefined, true);
    }, [onUploadFiles]);
    var actionTestID = disabled ? `${testID}.disabled` : testID;
    var color = disabled ? (0, _$$_REQUIRE(_dependencyMap[10]).changeOpacity)(theme.centerChannelColor, 0.16) : (0, _$$_REQUIRE(_dependencyMap[10]).changeOpacity)(theme.centerChannelColor, 0.64);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
      testID: actionTestID,
      disabled: disabled,
      onPress: handleButtonPress,
      style: style.icon,
      type: 'opacity',
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[11]).PaperClipIcon, {
        color: color,
        size: _$$_REQUIRE(_dependencyMap[12]).ICON_SIZE
      })
    });
  }
