  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.ITEM_HEIGHT = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var ITEM_HEIGHT = exports.ITEM_HEIGHT = 48;
  var getStyle = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      title: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 600, 'SemiBold'), {
        marginBottom: 8
      }),
      container: {
        height: ITEM_HEIGHT,
        marginHorizontal: -20,
        paddingHorizontal: 20
      },
      destructive: {
        color: theme.dndIndicator
      },
      row: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center'
      },
      iconContainer: {
        height: ITEM_HEIGHT,
        justifyContent: 'center',
        marginRight: 10
      },
      noIconContainer: {
        height: ITEM_HEIGHT,
        width: 18
      },
      icon: {
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.56)
      },
      textContainer: {
        justifyContent: 'center',
        flex: 1,
        height: ITEM_HEIGHT,
        marginRight: 5
      },
      text: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 200, 'Light'))
    };
  });
  var CameraType = function CameraType(_ref) {
    var onPress = _ref.onPress;
    var theme = (0, _$$_REQUIRE(_dependencyMap[8]).useTheme)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[9]).useIsTablet)();
    var style = getStyle(theme);
    var intl = (0, _$$_REQUIRE(_dependencyMap[10]).useIntl)();
    var onPhoto = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var options = {
          quality: 0.8,
          mediaType: 'photo',
          saveToPhotos: true
        };
        yield (0, _$$_REQUIRE(_dependencyMap[11]).dismissBottomSheet)();
        onPress(options);
      });
      return function onPhoto() {
        return _ref2.apply(this, arguments);
      };
    }();
    var onVideo = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var options = {
          videoQuality: 'high',
          mediaType: 'video',
          saveToPhotos: true
        };
        yield (0, _$$_REQUIRE(_dependencyMap[11]).dismissBottomSheet)();
        onPress(options);
      });
      return function onVideo() {
        return _ref3.apply(this, arguments);
      };
    }();
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [!isTablet && /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        id: "mobile.camera_type.title",
        defaultMessage: "Camera options",
        style: style.title
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableHighlight, {
        onPress: onPhoto,
        style: style.container
        //testID={testID}
        ,
        underlayColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.buttonBg, 0.08),
        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: style.row,
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[12]).CameraIcon, {
            size: 24,
            color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.56),
            style: {
              marginEnd: 9,
              marginStart: 5
            }
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: Object.assign({
              fontFamily: "IBMPlexSansArabic-SemiBold",
              /// fontSize:14,
              color: theme.centerChannelColor
            }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 200)),
            children: intl.formatMessage({
              id: 'camera_type.photo.option',
              defaultMessage: 'Capture Photo'
            })
          })]
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableHighlight, {
        onPress: onVideo,
        style: style.container
        //testID={testID}
        ,
        underlayColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.buttonBg, 0.08),
        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: style.row,
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[12]).VideoCameraIcon, {
            size: 24,
            color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.56),
            style: {
              marginEnd: 9,
              marginStart: 5
            }
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: Object.assign({
              fontFamily: "IBMPlexSansArabic-SemiBold",
              /// fontSize:14,
              color: theme.centerChannelColor
            }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 200)),
            children: intl.formatMessage({
              id: 'camera_type.video.option',
              defaultMessage: 'Record Video'
            })
          })]
        })
      })]
    });
  };
  var _default = exports.default = CameraType;
