  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = InputQuickAction;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      disabled: {
        tintColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.16)
      },
      icon: {
        alignItems: 'center',
        justifyContent: 'center',
        flexGrow: 1
      }
    };
  });
  function InputQuickAction(_ref) {
    var testID = _ref.testID,
      disabled = _ref.disabled,
      inputType = _ref.inputType,
      updateValue = _ref.updateValue,
      focus = _ref.focus;
    var theme = (0, _$$_REQUIRE(_dependencyMap[6]).useTheme)();
    var onPress = (0, _react.useCallback)(function () {
      updateValue(function (v) {
        if (inputType === 'at') {
          return `${v}@`;
        }
        return '/';
      });
      focus();
    }, [inputType]);
    var actionTestID = disabled ? `${testID}.disabled` : testID;
    var style = getStyleSheet(theme);
    var iconName = inputType === 'at' ? inputType : 'slash-forward';
    var iconColor = disabled ? (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.16) : (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.64);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
      testID: actionTestID,
      disabled: disabled,
      onPress: onPress,
      style: style.icon,
      type: 'opacity',
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        name: iconName,
        color: iconColor,
        size: _$$_REQUIRE(_dependencyMap[7]).ICON_SIZE
      })
    });
  }
