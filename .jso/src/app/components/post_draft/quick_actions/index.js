  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _quick_actions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var database = _ref.database;
    var canUploadFiles = (0, _$$_REQUIRE(_dependencyMap[4]).observeCanUploadFiles)(database);
    var maxFileCount = (0, _$$_REQUIRE(_dependencyMap[4]).observeMaxFileCount)(database);
    return {
      currentUserId: (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentUserId)(database),
      canUploadFiles: canUploadFiles,
      isPostPriorityEnabled: (0, _$$_REQUIRE(_dependencyMap[5]).observeIsPostPriorityEnabled)(database),
      maxFileCount: maxFileCount
    };
  });
  var _default = exports.default = _react2.default.memo((0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_quick_actions.default)));
