  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = PostPriorityAction;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var style = _reactNative.StyleSheet.create({
    icon: {
      alignItems: 'center',
      justifyContent: 'center',
      flexGrow: 1
    }
  });
  var POST_PRIORITY_PICKER_BUTTON = 'close-post-priority-picker';
  function PostPriorityAction(_ref) {
    var testID = _ref.testID,
      postPriority = _ref.postPriority,
      updatePostPriority = _ref.updatePostPriority;
    var intl = (0, _$$_REQUIRE(_dependencyMap[5]).useIntl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[6]).useIsTablet)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var onPress = (0, _react.useCallback)(function () {
      _reactNative.Keyboard.dismiss();
      var title = isTablet ? intl.formatMessage({
        id: 'post_priority.picker.title',
        defaultMessage: 'Message priority'
      }) : '';
      (0, _$$_REQUIRE(_dependencyMap[8]).openAsBottomSheet)({
        closeButtonId: POST_PRIORITY_PICKER_BUTTON,
        screen: _$$_REQUIRE(_dependencyMap[9]).Screens.POST_PRIORITY_PICKER,
        theme: theme,
        title: title,
        props: {
          postPriority: postPriority,
          updatePostPriority: updatePostPriority,
          closeButtonId: POST_PRIORITY_PICKER_BUTTON
        }
      });
    }, [intl, postPriority, updatePostPriority, theme]);
    var iconName = 'alert-circle-outline';
    var iconColor = (0, _$$_REQUIRE(_dependencyMap[10]).changeOpacity)(theme.centerChannelColor, 0.64);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
      testID: testID,
      onPress: onPress,
      style: style.icon,
      type: 'opacity',
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[11]).InformationCircleIcon
      //name={iconName}
      , {
        color: iconColor,
        size: _$$_REQUIRE(_dependencyMap[12]).ICON_SIZE
      })
    });
  }
