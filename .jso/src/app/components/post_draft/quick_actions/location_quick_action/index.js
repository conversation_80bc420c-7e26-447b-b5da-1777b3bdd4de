  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var Device = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var Location = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[10]).makeStyleSheetFromTheme)(function (theme) {
    return {
      disabled: {
        tintColor: (0, _$$_REQUIRE(_dependencyMap[10]).changeOpacity)(theme.centerChannelColor, 0.16)
      },
      icon: {
        alignItems: 'center',
        justifyContent: 'center',
        flexGrow: 1
      }
    };
  });
  var LocationQuickAction = function LocationQuickAction(_ref) {
    var testID = _ref.testID,
      channelID = _ref.channelID,
      currentUserId = _ref.currentUserId;
    var theme = (0, _$$_REQUIRE(_dependencyMap[11]).useTheme)();
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      location = _useState2[0],
      setLocation = _useState2[1];
    var _useState3 = (0, _react.useState)(''),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      errorMsg = _useState4[0],
      setErrorMsg = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      loading = _useState6[0],
      setLoading = _useState6[1];
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[12]).useServerUrl)();
    var style = getStyleSheet(theme);
    var _useAlert = (0, _$$_REQUIRE(_dependencyMap[13]).useAlert)(),
      showAlert = _useAlert.showAlert;

    ////////////////////////////// !! LOCATION  /////////////////////////////////////
    var fetchLocation = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        setLoading(true);
        if (_reactNative.Platform.OS === 'android' && !Device.isDevice) {
          showAlert('خطاء ما', "عذرًا، هذا لن يعمل على محاكي أندرويد. جرّب تشغيله على جهازك!");
          setLoading(false);
          return false;
        }
        var _yield$Location$reque = yield Location.requestForegroundPermissionsAsync(),
          status = _yield$Location$reque.status;
        if (status !== 'granted') {
          showAlert('خطأ', 'تم رفض الإذن للوصول إلى الموقع');
          setLoading(false);
          return false;
        }
        try {
          // Create a timeout promise that rejects in 10 seconds
          var timeoutPromise = new Promise(function (_, reject) {
            return setTimeout(function () {
              return reject(new Error('Timeout fetching location'));
            }, 10000);
          });

          // Race the location request against the timeout
          var loc = yield Promise.race([Location.getCurrentPositionAsync({}), timeoutPromise]);
          setLocation(loc);
          return true;
        } catch (error) {
          console.log(`\n\n\n\n this the error ${error} \n\n\n\n`);
          showAlert('خطأ', error.message === 'Timeout fetching location' ? 'انتهى الوقت أثناء جلب الموقع' : 'فشل في جلب الموقع');
        } finally {
          setLoading(false);
        }
        return false;
      });
      return function fetchLocation() {
        return _ref2.apply(this, arguments);
      };
    }();
    var getLocationLink = function getLocationLink() {
      if (location) {
        var _location$coords = location.coords,
          latitude = _location$coords.latitude,
          longitude = _location$coords.longitude;
        return `العنوان  https://www.google.com/maps?q=${latitude},${longitude}`;
      }
      return null;
    };
    var openLocationInBrowser = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var isLocationAvailable = yield fetchLocation();
        if (isLocationAvailable == false) return;
        var post = {
          user_id: currentUserId,
          channel_id: channelID,
          root_id: '',
          message: getLocationLink()
        };
        var locationLink = getLocationLink();
        if (locationLink) {
          post.metadata = {
            priority: {
              "priority": "",
              "requested_ack": true,
              "persistent_notifications": false
            }
          };
          (0, _$$_REQUIRE(_dependencyMap[14]).createPost)(serverUrl, post, []);
        } else if (!loading && errorMsg) {
          showAlert('خطاء ما', errorMsg);
        }
      });
      return function openLocationInBrowser() {
        return _ref3.apply(this, arguments);
      };
    }();
    ////////////////////////////// !! LOCATION  /////////////////////////////////////

    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
      disabled: loading,
      onPress: openLocationInBrowser,
      style: style.icon,
      type: 'opacity',
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        children: loading ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
          size: "small",
          color: (0, _$$_REQUIRE(_dependencyMap[10]).changeOpacity)(theme.centerChannelColor, 0.64)
        }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          name: 'map-marker-outline',
          color: (0, _$$_REQUIRE(_dependencyMap[10]).changeOpacity)(theme.centerChannelColor, 0.64),
          size: _$$_REQUIRE(_dependencyMap[15]).ICON_SIZE
        })
      })
    });
  };
  var _default = exports.default = LocationQuickAction;
