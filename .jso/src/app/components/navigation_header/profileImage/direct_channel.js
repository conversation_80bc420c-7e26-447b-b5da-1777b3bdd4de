  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _member = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      botContainer: {
        alignSelf: 'flex-end',
        bottom: 7.5,
        height: 20,
        marginBottom: 0,
        marginLeft: 4,
        paddingVertical: 0
      },
      botText: {
        fontSize: 14,
        lineHeight: 20
      },
      container: {
        alignItems: 'center',
        marginHorizontal: 20
      },
      message: Object.assign({
        color: theme.centerChannelColor,
        marginTop: 8,
        textAlign: 'center'
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 200, 'Light')),
      boldText: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 200, 'SemiBold')),
      profilesContainer: {
        height: 20,
        width: 25,
        justifyContent: 'center',
        alignItems: 'center'
      },
      title: Object.assign({
        color: theme.centerChannelColor,
        marginTop: 4,
        textAlign: 'center'
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 700, 'SemiBold')),
      titleGroup: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 600, 'SemiBold'))
    };
  });
  var gmIntroMessages = (0, _$$_REQUIRE(_dependencyMap[9]).defineMessages)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({
    muted: {
      id: 'intro.group_message.muted',
      defaultMessage: 'This group message is currently <b>muted</b>, so you will not be notified.'
    }
  }, _$$_REQUIRE(_dependencyMap[10]).NotificationLevel.ALL, {
    id: 'intro.group_message.all',
    defaultMessage: 'You\'ll be notified <b>for all activity</b> in this group message.'
  }), _$$_REQUIRE(_dependencyMap[10]).NotificationLevel.DEFAULT, {
    id: 'intro.group_message.all',
    defaultMessage: 'You\'ll be notified <b>for all activity</b> in this group message.'
  }), _$$_REQUIRE(_dependencyMap[10]).NotificationLevel.MENTION, {
    id: 'intro.group_message.mention',
    defaultMessage: 'You have selected to be notified <b>only when mentioned</b> in this group message.'
  }), _$$_REQUIRE(_dependencyMap[10]).NotificationLevel.NONE, {
    id: 'intro.group_message.none',
    defaultMessage: 'You have selected to <b>never</b> be notified in this group message.'
  }));
  var DirectChannel = function DirectChannel(_ref) {
    var channel = _ref.channel,
      isBot = _ref.isBot,
      currentUserId = _ref.currentUserId,
      members = _ref.members,
      theme = _ref.theme,
      hasGMasDMFeature = _ref.hasGMasDMFeature,
      channelNotifyProps = _ref.channelNotifyProps,
      userNotifyProps = _ref.userNotifyProps,
      _ref$isOnHeader = _ref.isOnHeader,
      isOnHeader = _ref$isOnHeader === undefined ? false : _ref$isOnHeader,
      chageUserState = _ref.chageUserState,
      getLastSeam = _ref.getLastSeam;
    var profiles = (0, _react.useMemo)(function () {
      if (channel.type === _$$_REQUIRE(_dependencyMap[10]).General.DM_CHANNEL) {
        var teammateId = (0, _$$_REQUIRE(_dependencyMap[11]).getUserIdFromChannelName)(currentUserId, channel.name);
        var teammate = members == null ? undefined : members.find(function (m) {
          return m.userId === teammateId;
        });
        if (!teammate) {
          return null;
        }
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_member.default, {
          chageUserState: chageUserState,
          getLastSeam: getLastSeam,
          channelId: channel.id,
          containerStyle: {
            height: 20
          },
          member: teammate,
          size: 30,
          theme: theme
        });
      }
    }, [members, theme]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      children: profiles
    });
  };
  var _default = exports.default = DirectChannel;
