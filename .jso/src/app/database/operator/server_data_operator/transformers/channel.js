  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformMyChannelSettingsRecord = exports.transformMyChannelRecord = exports.transformChannelRecord = exports.transformChannelMembershipRecord = exports.transformChannelInfoRecord = exports.transformChannelBookmarkRecord = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[2]).MM_TABLES.SERVER,
    CHANNEL = _MM_TABLES$SERVER.CHANNEL,
    CHANNEL_BOOKMARK = _MM_TABLES$SERVER.CHANNEL_BOOKMARK,
    CHANNEL_INFO = _MM_TABLES$SERVER.CHANNEL_INFO,
    CHANNEL_MEMBERSHIP = _MM_TABLES$SERVER.CHANNEL_MEMBERSHIP,
    MY_CHANNEL = _MM_TABLES$SERVER.MY_CHANNEL,
    MY_CHANNEL_SETTINGS = _MM_TABLES$SERVER.MY_CHANNEL_SETTINGS;

  /**
   * transformChannelRecord: Prepares a record of the SERVER database 'Channel' table for update or create actions.
   * @param {DataFactory} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<ChannelModel>}
   */
  var transformChannelRecord = exports.transformChannelRecord = function transformChannelRecord(_ref) {
    var action = _ref.action,
      database = _ref.database,
      value = _ref.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[2]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(channel) {
      var _raw$id;
      channel._raw.id = isCreateAction ? (_raw$id = raw == null ? undefined : raw.id) != null ? _raw$id : channel.id : record.id;
      channel.createAt = raw.create_at;
      channel.creatorId = raw.creator_id;
      channel.deleteAt = raw.delete_at;
      channel.updateAt = raw.update_at;
      channel.displayName = (0, _$$_REQUIRE(_dependencyMap[3]).extractChannelDisplayName)(raw, record);
      channel.isGroupConstrained = Boolean(raw.group_constrained);
      channel.name = raw.name;
      channel.shared = Boolean(raw.shared);
      channel.teamId = raw.team_id;
      channel.type = raw.type;
    };
    return (0, _$$_REQUIRE(_dependencyMap[4]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: CHANNEL,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformMyChannelSettingsRecord: Prepares a record of the SERVER database 'MyChannelSettings' table for update or create actions.
   * @param {DataFactory} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<MyChannelSettingsModel>}
   */
  var transformMyChannelSettingsRecord = exports.transformMyChannelSettingsRecord = function transformMyChannelSettingsRecord(_ref2) {
    var action = _ref2.action,
      database = _ref2.database,
      value = _ref2.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[2]).OperationType.CREATE;
    var fieldsMapper = function fieldsMapper(myChannelSetting) {
      myChannelSetting._raw.id = isCreateAction ? raw.channel_id || myChannelSetting.id : record.id;
      myChannelSetting.notifyProps = raw.notify_props;
    };
    return (0, _$$_REQUIRE(_dependencyMap[4]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: MY_CHANNEL_SETTINGS,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformChannelInfoRecord: Prepares a record of the SERVER database 'ChannelInfo' table for update or create actions.
   * @param {DataFactory} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<ChannelInfoModel>}
   */
  var transformChannelInfoRecord = exports.transformChannelInfoRecord = function transformChannelInfoRecord(_ref3) {
    var action = _ref3.action,
      database = _ref3.database,
      value = _ref3.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[2]).OperationType.CREATE;
    var fieldsMapper = function fieldsMapper(channelInfo) {
      var _ref4, _raw$guest_count, _ref5, _raw$header, _ref6, _raw$member_count, _ref7, _raw$pinned_post_coun, _ref8, _raw$files_count, _ref9, _raw$purpose;
      channelInfo._raw.id = isCreateAction ? raw.id || channelInfo.id : record.id;
      channelInfo.guestCount = (_ref4 = (_raw$guest_count = raw.guest_count) != null ? _raw$guest_count : channelInfo.guestCount) != null ? _ref4 : 0;
      channelInfo.header = (_ref5 = (_raw$header = raw.header) != null ? _raw$header : channelInfo.header) != null ? _ref5 : '';
      channelInfo.memberCount = (_ref6 = (_raw$member_count = raw.member_count) != null ? _raw$member_count : channelInfo.memberCount) != null ? _ref6 : 0;
      channelInfo.pinnedPostCount = (_ref7 = (_raw$pinned_post_coun = raw.pinned_post_count) != null ? _raw$pinned_post_coun : channelInfo.pinnedPostCount) != null ? _ref7 : 0;
      channelInfo.filesCount = (_ref8 = (_raw$files_count = raw.files_count) != null ? _raw$files_count : channelInfo.filesCount) != null ? _ref8 : 0;
      channelInfo.purpose = (_ref9 = (_raw$purpose = raw.purpose) != null ? _raw$purpose : channelInfo.purpose) != null ? _ref9 : '';
    };
    return (0, _$$_REQUIRE(_dependencyMap[4]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: CHANNEL_INFO,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformMyChannelRecord: Prepares a record of the SERVER database 'MyChannel' table for update or create actions.
   * @param {DataFactory} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<MyChannelModel>}
   */
  var transformMyChannelRecord = exports.transformMyChannelRecord = /*#__PURE__*/function () {
    var _ref11 = (0, _asyncToGenerator2.default)(function* (_ref10) {
      var action = _ref10.action,
        database = _ref10.database,
        value = _ref10.value;
      var raw = value.raw;
      var record = value.record;
      var isCreateAction = action === _$$_REQUIRE(_dependencyMap[2]).OperationType.CREATE;
      var fieldsMapper = function fieldsMapper(myChannel) {
        myChannel._raw.id = isCreateAction ? raw.channel_id || myChannel.id : record.id;
        myChannel.roles = raw.roles;

        // ignoring msg_count_root because msg_count, mention_count, last_post_at is already calculated in "handleMyChannel" based on CRT is enabled or not
        myChannel.messageCount = raw.msg_count;
        myChannel.mentionsCount = raw.mention_count;
        myChannel.lastPostAt = raw.last_post_at || 0;
        myChannel.isUnread = Boolean(raw.is_unread);
        myChannel.lastViewedAt = raw.last_viewed_at;
        myChannel.viewedAt = (record == null ? undefined : record.viewedAt) || 0;
        myChannel.lastFetchedAt = (record == null ? undefined : record.lastFetchedAt) || 0;
      };
      return (0, _$$_REQUIRE(_dependencyMap[4]).prepareBaseRecord)({
        action: action,
        database: database,
        tableName: MY_CHANNEL,
        value: value,
        fieldsMapper: fieldsMapper
      });
    });
    return function transformMyChannelRecord(_x) {
      return _ref11.apply(this, arguments);
    };
  }();

  /**
   * transformChannelMembershipRecord: Prepares a record of the SERVER database 'ChannelMembership' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<ChannelMembershipModel>}
   */
  var transformChannelMembershipRecord = exports.transformChannelMembershipRecord = function transformChannelMembershipRecord(_ref12) {
    var action = _ref12.action,
      database = _ref12.database,
      value = _ref12.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[2]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(channelMember) {
      var _raw$id2, _raw$scheme_admin;
      channelMember._raw.id = isCreateAction ? (_raw$id2 = raw == null ? undefined : raw.id) != null ? _raw$id2 : channelMember.id : record.id;
      channelMember.channelId = raw.channel_id;
      channelMember.userId = raw.user_id;
      channelMember.schemeAdmin = (_raw$scheme_admin = raw.scheme_admin) != null ? _raw$scheme_admin : false;
    };
    return (0, _$$_REQUIRE(_dependencyMap[4]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: CHANNEL_MEMBERSHIP,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformChannelBookmarkRecord: Prepares a record of the SERVER database 'Channel' table for update or create actions.
   * @param {DataFactory} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<ChannelBookmarkModel>}
   */
  var transformChannelBookmarkRecord = exports.transformChannelBookmarkRecord = function transformChannelBookmarkRecord(_ref13) {
    var action = _ref13.action,
      database = _ref13.database,
      value = _ref13.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[2]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(bookmark) {
      var _raw$id3;
      bookmark._raw.id = isCreateAction ? (_raw$id3 = raw == null ? undefined : raw.id) != null ? _raw$id3 : bookmark.id : record.id;
      bookmark.createAt = raw.create_at;
      bookmark.deleteAt = raw.delete_at;
      bookmark.updateAt = raw.update_at;
      bookmark.channelId = raw.channel_id;
      bookmark.ownerId = raw.owner_id;
      bookmark.fileId = raw.file_id;
      bookmark.displayName = raw.display_name;
      bookmark.sortOrder = raw.sort_order;
      bookmark.linkUrl = raw.link_url;
      bookmark.imageUrl = raw.image_url;
      bookmark.emoji = raw.emoji;
      bookmark.type = raw.type;
      bookmark.originalId = raw.original_id;
      bookmark.parentId = raw.parent_id;
    };
    return (0, _$$_REQUIRE(_dependencyMap[4]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: CHANNEL_BOOKMARK,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
