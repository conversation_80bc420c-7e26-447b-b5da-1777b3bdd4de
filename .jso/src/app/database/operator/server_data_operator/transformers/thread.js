  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformThreadRecord = exports.transformThreadParticipantRecord = exports.transformThreadInTeamRecord = exports.transformTeamThreadsSyncRecord = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER,
    THREAD = _MM_TABLES$SERVER.THREAD,
    THREAD_PARTICIPANT = _MM_TABLES$SERVER.THREAD_PARTICIPANT,
    THREADS_IN_TEAM = _MM_TABLES$SERVER.THREADS_IN_TEAM,
    TEAM_THREADS_SYNC = _MM_TABLES$SERVER.TEAM_THREADS_SYNC;

  /**
   * transformThreadRecord: Prepares a record of the SERVER database 'Thread' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<ThreadModel>}
   */
  var transformThreadRecord = exports.transformThreadRecord = function transformThreadRecord(_ref) {
    var action = _ref.action,
      database = _ref.database,
      value = _ref.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(thread) {
      var _raw$id, _ref2, _raw$last_viewed_at, _raw$is_following, _ref3, _raw$unread_replies, _ref4, _raw$unread_mentions;
      thread._raw.id = isCreateAction ? (_raw$id = raw == null ? undefined : raw.id) != null ? _raw$id : thread.id : record.id;

      // When post is individually fetched, we get last_reply_at as 0, so we use the record's value
      thread.lastReplyAt = raw.last_reply_at || (record == null ? undefined : record.lastReplyAt);
      thread.lastViewedAt = (_ref2 = (_raw$last_viewed_at = raw.last_viewed_at) != null ? _raw$last_viewed_at : record == null ? undefined : record.lastViewedAt) != null ? _ref2 : 0;
      thread.replyCount = raw.reply_count;
      thread.isFollowing = (_raw$is_following = raw.is_following) != null ? _raw$is_following : record == null ? undefined : record.isFollowing;
      thread.unreadReplies = (_ref3 = (_raw$unread_replies = raw.unread_replies) != null ? _raw$unread_replies : record == null ? undefined : record.unreadReplies) != null ? _ref3 : 0;
      thread.unreadMentions = (_ref4 = (_raw$unread_mentions = raw.unread_mentions) != null ? _raw$unread_mentions : record == null ? undefined : record.unreadMentions) != null ? _ref4 : 0;
      thread.viewedAt = (record == null ? undefined : record.viewedAt) || 0;
      thread.lastFetchedAt = Math.max((record == null ? undefined : record.lastFetchedAt) || 0, raw.lastFetchedAt || 0);
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: THREAD,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformThreadParticipantRecord: Prepares a record of the SERVER database 'ThreadParticipant' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<ThreadParticipantModel>}
   */
  var transformThreadParticipantRecord = exports.transformThreadParticipantRecord = function transformThreadParticipantRecord(_ref5) {
    var action = _ref5.action,
      database = _ref5.database,
      value = _ref5.value;
    var raw = value.raw;

    // id of participant comes from server response
    var fieldsMapper = function fieldsMapper(participant) {
      participant.threadId = raw.thread_id;
      participant.userId = raw.id;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: THREAD_PARTICIPANT,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
  var transformThreadInTeamRecord = exports.transformThreadInTeamRecord = function transformThreadInTeamRecord(_ref6) {
    var action = _ref6.action,
      database = _ref6.database,
      value = _ref6.value;
    var raw = value.raw;
    var fieldsMapper = function fieldsMapper(threadInTeam) {
      threadInTeam.threadId = raw.thread_id;
      threadInTeam.teamId = raw.team_id;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: THREADS_IN_TEAM,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
  var transformTeamThreadsSyncRecord = exports.transformTeamThreadsSyncRecord = function transformTeamThreadsSyncRecord(_ref7) {
    var action = _ref7.action,
      database = _ref7.database,
      value = _ref7.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;
    var fieldsMapper = function fieldsMapper(teamThreadsSync) {
      var _raw$id2;
      teamThreadsSync._raw.id = isCreateAction ? (_raw$id2 = raw == null ? undefined : raw.id) != null ? _raw$id2 : teamThreadsSync.id : record.id;
      teamThreadsSync.earliest = raw.earliest || (record == null ? undefined : record.earliest);
      teamThreadsSync.latest = raw.latest || (record == null ? undefined : record.latest);
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: TEAM_THREADS_SYNC,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
