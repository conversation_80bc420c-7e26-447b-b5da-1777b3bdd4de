  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformGroupTeamRecord = exports.transformGroupRecord = exports.transformGroupMembershipRecord = exports.transformGroupChannelRecord = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  // See LICENSE.txt for license information.

  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER,
    GROUP = _MM_TABLES$SERVER.GROUP,
    GROUP_CHANNEL = _MM_TABLES$SERVER.GROUP_CHANNEL,
    GROUP_MEMBERSHIP = _MM_TABLES$SERVER.GROUP_MEMBERSHIP,
    GROUP_TEAM = _MM_TABLES$SERVER.GROUP_TEAM;

  /**
    * transformGroupRecord: Prepares a record of the SERVER database 'Group' table for update or create actions.
    * @param {TransformerArgs} operator
    * @param {Database} operator.database
    * @param {RecordPair} operator.value
    * @returns {Promise<GroupModel>}
    */
  var transformGroupRecord = exports.transformGroupRecord = function transformGroupRecord(_ref) {
    var action = _ref.action,
      database = _ref.database,
      value = _ref.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // id of group comes from server response
    var fieldsMapper = function fieldsMapper(group) {
      var _raw$id;
      group._raw.id = isCreateAction ? (_raw$id = raw == null ? undefined : raw.id) != null ? _raw$id : group.id : record.id;
      group.name = raw.name;
      group.displayName = raw.display_name;
      group.source = raw.source;
      group.remoteId = raw.remote_id;
      group.memberCount = raw.member_count || 0;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: GROUP,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
     * transformGroupChannelRecord: Prepares a record of the SERVER database 'GroupChannel' table for update or create actions.
     * @param {TransformerArgs} operator
     * @param {Database} operator.database
     * @param {RecordPair} operator.value
     * @returns {Promise<GroupChannelModel>}
     */
  var transformGroupChannelRecord = exports.transformGroupChannelRecord = function transformGroupChannelRecord(_ref2) {
    var action = _ref2.action,
      database = _ref2.database,
      value = _ref2.value;
    var raw = value.raw;

    // id of group comes from server response
    var fieldsMapper = function fieldsMapper(model) {
      model._raw.id = raw.id || (0, _$$_REQUIRE(_dependencyMap[2]).generateGroupAssociationId)(raw.group_id, raw.channel_id);
      model.groupId = raw.group_id;
      model.channelId = raw.channel_id;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: GROUP_CHANNEL,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
     * transformGroupMembershipRecord: Prepares a record of the SERVER database 'GroupMembership' table for update or create actions.
     * @param {TransformerArgs} operator
     * @param {Database} operator.database
     * @param {RecordPair} operator.value
     * @returns {Promise<GroupMembershipModel>}
     */
  var transformGroupMembershipRecord = exports.transformGroupMembershipRecord = function transformGroupMembershipRecord(_ref3) {
    var action = _ref3.action,
      database = _ref3.database,
      value = _ref3.value;
    var raw = value.raw;

    // id of group comes from server response
    var fieldsMapper = function fieldsMapper(model) {
      model._raw.id = raw.id || (0, _$$_REQUIRE(_dependencyMap[2]).generateGroupAssociationId)(raw.group_id, raw.user_id);
      model.groupId = raw.group_id;
      model.userId = raw.user_id;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: GROUP_MEMBERSHIP,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
     * transformGroupTeamRecord: Prepares a record of the SERVER database 'GroupTeam' table for update or create actions.
     * @param {TransformerArgs} operator
     * @param {Database} operator.database
     * @param {RecordPair} operator.value
     * @returns {Promise<GroupTeamModel>}
     */
  var transformGroupTeamRecord = exports.transformGroupTeamRecord = function transformGroupTeamRecord(_ref4) {
    var action = _ref4.action,
      database = _ref4.database,
      value = _ref4.value;
    var raw = value.raw;

    // id of group comes from server response
    var fieldsMapper = function fieldsMapper(model) {
      model._raw.id = raw.id || (0, _$$_REQUIRE(_dependencyMap[2]).generateGroupAssociationId)(raw.group_id, raw.team_id);
      model.groupId = raw.group_id;
      model.teamId = raw.team_id;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: GROUP_TEAM,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
