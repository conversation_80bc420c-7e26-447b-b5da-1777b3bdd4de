  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformTeamSearchHistoryRecord = exports.transformTeamRecord = exports.transformTeamMembershipRecord = exports.transformTeamChannelHistoryRecord = exports.transformMyTeamRecord = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER,
    MY_TEAM = _MM_TABLES$SERVER.MY_TEAM,
    TEAM = _MM_TABLES$SERVER.TEAM,
    TEAM_CHANNEL_HISTORY = _MM_TABLES$SERVER.TEAM_CHANNEL_HISTORY,
    TEAM_MEMBERSHIP = _MM_TABLES$SERVER.TEAM_MEMBERSHIP,
    TEAM_SEARCH_HISTORY = _MM_TABLES$SERVER.TEAM_SEARCH_HISTORY;

  /**
   * transformTeamMembershipRecord: Prepares a record of the SERVER database 'TeamMembership' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<TeamMembershipModel>}
   */
  var transformTeamMembershipRecord = exports.transformTeamMembershipRecord = function transformTeamMembershipRecord(_ref) {
    var action = _ref.action,
      database = _ref.database,
      value = _ref.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(teamMembership) {
      var _raw$id;
      teamMembership._raw.id = isCreateAction ? (_raw$id = raw == null ? undefined : raw.id) != null ? _raw$id : teamMembership.id : record.id;
      teamMembership.teamId = raw.team_id;
      teamMembership.userId = raw.user_id;
      teamMembership.schemeAdmin = raw.scheme_admin;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: TEAM_MEMBERSHIP,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformTeamRecord: Prepares a record of the SERVER database 'Team' table for update or create actions.
   * @param {DataFactory} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<TeamModel>}
   */
  var transformTeamRecord = exports.transformTeamRecord = function transformTeamRecord(_ref2) {
    var action = _ref2.action,
      database = _ref2.database,
      value = _ref2.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(team) {
      var _raw$id2;
      team._raw.id = isCreateAction ? (_raw$id2 = raw == null ? undefined : raw.id) != null ? _raw$id2 : team.id : record.id;
      team.isAllowOpenInvite = raw.allow_open_invite;
      team.description = raw.description;
      team.displayName = raw.display_name;
      team.name = raw.name;
      team.updateAt = raw.update_at;
      team.type = raw.type;
      team.allowedDomains = raw.allowed_domains;
      team.isGroupConstrained = Boolean(raw.group_constrained);
      team.lastTeamIconUpdatedAt = raw.last_team_icon_update;
      team.inviteId = raw.invite_id;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: TEAM,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformTeamChannelHistoryRecord: Prepares a record of the SERVER database 'TeamChannelHistory' table for update or create actions.
   * @param {DataFactory} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<TeamChannelHistoryModel>}
   */
  var transformTeamChannelHistoryRecord = exports.transformTeamChannelHistoryRecord = function transformTeamChannelHistoryRecord(_ref3) {
    var action = _ref3.action,
      database = _ref3.database,
      value = _ref3.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;
    var fieldsMapper = function fieldsMapper(teamChannelHistory) {
      teamChannelHistory._raw.id = isCreateAction ? raw.id || teamChannelHistory.id : record.id;
      teamChannelHistory.channelIds = raw.channel_ids;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: TEAM_CHANNEL_HISTORY,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformTeamSearchHistoryRecord: Prepares a record of the SERVER database 'TeamSearchHistory' table for update or create actions.
   * @param {DataFactory} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<TeamSearchHistoryModel>}
   */
  var transformTeamSearchHistoryRecord = exports.transformTeamSearchHistoryRecord = function transformTeamSearchHistoryRecord(_ref4) {
    var action = _ref4.action,
      database = _ref4.database,
      value = _ref4.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;
    var fieldsMapper = function fieldsMapper(teamSearchHistory) {
      teamSearchHistory._raw.id = isCreateAction ? teamSearchHistory.id : record.id;
      teamSearchHistory.createdAt = raw.created_at;
      teamSearchHistory.displayTerm = raw.display_term;
      teamSearchHistory.term = raw.term;
      teamSearchHistory.teamId = raw.team_id;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: TEAM_SEARCH_HISTORY,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformMyTeamRecord: Prepares a record of the SERVER database 'MyTeam' table for update or create actions.
   * @param {DataFactory} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<MyTeamModel>}
   */
  var transformMyTeamRecord = exports.transformMyTeamRecord = function transformMyTeamRecord(_ref5) {
    var action = _ref5.action,
      database = _ref5.database,
      value = _ref5.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;
    var fieldsMapper = function fieldsMapper(myTeam) {
      myTeam._raw.id = isCreateAction ? raw.id || myTeam.id : record.id;
      myTeam.roles = raw.roles;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: MY_TEAM,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
