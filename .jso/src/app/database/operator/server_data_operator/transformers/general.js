  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformSystemRecord = exports.transformRoleRecord = exports.transformFileRecord = exports.transformCustomEmojiRecord = exports.transformConfigRecord = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER,
    CUSTOM_EMOJI = _MM_TABLES$SERVER.CUSTOM_EMOJI,
    FILE = _MM_TABLES$SERVER.FILE,
    ROLE = _MM_TABLES$SERVER.ROLE,
    SYSTEM = _MM_TABLES$SERVER.SYSTEM,
    CONFIG = _MM_TABLES$SERVER.CONFIG;

  /**
   * transformCustomEmojiRecord: Prepares a record of the SERVER database 'CustomEmoji' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<CustomEmojiModel>}
   */
  var transformCustomEmojiRecord = exports.transformCustomEmojiRecord = function transformCustomEmojiRecord(_ref) {
    var action = _ref.action,
      database = _ref.database,
      value = _ref.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(emoji) {
      var _raw$id;
      emoji._raw.id = isCreateAction ? (_raw$id = raw == null ? undefined : raw.id) != null ? _raw$id : emoji.id : record.id;
      emoji.name = raw.name;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: CUSTOM_EMOJI,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformRoleRecord: Prepares a record of the SERVER database 'Role' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<RoleModel>}
   */
  var transformRoleRecord = exports.transformRoleRecord = function transformRoleRecord(_ref2) {
    var action = _ref2.action,
      database = _ref2.database,
      value = _ref2.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(role) {
      var _raw$id2;
      role._raw.id = isCreateAction ? (_raw$id2 = raw == null ? undefined : raw.id) != null ? _raw$id2 : role.id : record.id;
      role.name = raw == null ? undefined : raw.name;
      role.permissions = raw == null ? undefined : raw.permissions;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: ROLE,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformSystemRecord: Prepares a record of the SERVER database 'System' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<SystemModel>}
   */
  var transformSystemRecord = exports.transformSystemRecord = function transformSystemRecord(_ref3) {
    var action = _ref3.action,
      database = _ref3.database,
      value = _ref3.value;
    var raw = value.raw;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(system) {
      system._raw.id = raw == null ? undefined : raw.id;
      system.value = raw == null ? undefined : raw.value;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: SYSTEM,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformConfigRecord: Prepares a record of the SERVER database 'Config' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<ConfigModel>}
   */
  var transformConfigRecord = exports.transformConfigRecord = function transformConfigRecord(_ref4) {
    var action = _ref4.action,
      database = _ref4.database,
      value = _ref4.value;
    var raw = value.raw;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(config) {
      config._raw.id = raw == null ? undefined : raw.id;
      config.value = raw == null ? undefined : raw.value;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: CONFIG,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformFileRecord: Prepares a record of the SERVER database 'Files' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<FileModel>}
   */
  var transformFileRecord = exports.transformFileRecord = function transformFileRecord(_ref5) {
    var action = _ref5.action,
      database = _ref5.database,
      value = _ref5.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(file) {
      var _raw$mime_type;
      file._raw.id = isCreateAction ? raw.id || file.id : record.id;
      file.postId = raw.post_id || '';
      file.name = raw.name;
      file.extension = raw.extension;
      file.size = raw.size;
      file.mimeType = (_raw$mime_type = raw == null ? undefined : raw.mime_type) != null ? _raw$mime_type : '';
      file.width = (raw == null ? undefined : raw.width) || (record == null ? undefined : record.width) || 0;
      file.height = (raw == null ? undefined : raw.height) || (record == null ? undefined : record.height) || 0;
      file.imageThumbnail = (raw == null ? undefined : raw.mini_preview) || (record == null ? undefined : record.imageThumbnail) || '';
      file.localPath = (raw == null ? undefined : raw.localPath) || (record == null ? undefined : record.localPath) || null;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: FILE,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
