  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformCategoryRecord = exports.transformCategoryChannelRecord = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER,
    CATEGORY = _MM_TABLES$SERVER.CATEGORY,
    CATEGORY_CHANNEL = _MM_TABLES$SERVER.CATEGORY_CHANNEL;

  /**
   * transformCategoryRecord: Prepares a record of the SERVER database 'Category' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<CategoryModel>}
   */
  var transformCategoryRecord = exports.transformCategoryRecord = function transformCategoryRecord(_ref) {
    var action = _ref.action,
      database = _ref.database,
      value = _ref.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // id of category comes from server response
    var fieldsMapper = function fieldsMapper(category) {
      var _raw$id, _raw$muted;
      category._raw.id = isCreateAction ? (_raw$id = raw == null ? undefined : raw.id) != null ? _raw$id : category.id : record.id;
      category.displayName = raw.display_name;
      category.sorting = raw.sorting || 'recent';
      category.sortOrder = raw.sort_order === 0 ? 0 : raw.sort_order / 10; // Sort order from server is in multiples of 10
      category.muted = (_raw$muted = raw.muted) != null ? _raw$muted : false;
      category.collapsed = isCreateAction ? false : record.collapsed;
      category.type = raw.type;
      category.teamId = raw.team_id;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: CATEGORY,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformCategoryChannelRecord: Prepares a record of the SERVER database 'CategoryChannel' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<CategoryChannelModel>}
   */
  var transformCategoryChannelRecord = exports.transformCategoryChannelRecord = function transformCategoryChannelRecord(_ref2) {
    var action = _ref2.action,
      database = _ref2.database,
      value = _ref2.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(categoryChannel) {
      var _raw$id2;
      categoryChannel._raw.id = isCreateAction ? (_raw$id2 = raw == null ? undefined : raw.id) != null ? _raw$id2 : categoryChannel.id : record.id;
      categoryChannel.channelId = raw.channel_id;
      categoryChannel.categoryId = raw.category_id;
      categoryChannel.sortOrder = raw.sort_order;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: CATEGORY_CHANNEL,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
