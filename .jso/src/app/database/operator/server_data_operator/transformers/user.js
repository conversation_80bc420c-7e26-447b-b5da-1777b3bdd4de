  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformUserRecord = exports.transformPreferenceRecord = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER,
    PREFERENCE = _MM_TABLES$SERVER.PREFERENCE,
    USER = _MM_TABLES$SERVER.USER;

  /**
   * transformUserRecord: Prepares a record of the SERVER database 'User' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<UserModel>}
   */
  var transformUserRecord = exports.transformUserRecord = function transformUserRecord(_ref) {
    var action = _ref.action,
      database = _ref.database,
      value = _ref.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // id of user comes from server response
    var fieldsMapper = function fieldsMapper(user) {
      var _raw$id, _raw$position, _raw$is_bot, _raw$remote_id, _raw$terms_of_service, _raw$terms_of_service2;
      user._raw.id = isCreateAction ? (_raw$id = raw == null ? undefined : raw.id) != null ? _raw$id : user.id : record.id;
      user.authService = raw.auth_service;
      user.deleteAt = raw.delete_at;
      user.updateAt = raw.update_at;
      user.email = raw.email;
      user.firstName = raw.first_name;
      user.isGuest = raw.roles.includes('system_guest');
      user.lastName = raw.last_name;
      user.lastPictureUpdate = raw.last_picture_update || 0;
      user.locale = raw.locale;
      user.nickname = raw.nickname;
      user.position = (_raw$position = raw == null ? undefined : raw.position) != null ? _raw$position : '';
      user.roles = raw.roles;
      user.username = raw.username;
      user.notifyProps = raw.notify_props;
      user.timezone = raw.timezone || null;
      user.isBot = (_raw$is_bot = raw.is_bot) != null ? _raw$is_bot : false;
      user.remoteId = (_raw$remote_id = raw == null ? undefined : raw.remote_id) != null ? _raw$remote_id : null;
      user.termsOfServiceId = (_raw$terms_of_service = raw == null ? undefined : raw.terms_of_service_id) != null ? _raw$terms_of_service : (record == null ? undefined : record.termsOfServiceId) || '';
      user.termsOfServiceCreateAt = (_raw$terms_of_service2 = raw == null ? undefined : raw.terms_of_service_create_at) != null ? _raw$terms_of_service2 : (record == null ? undefined : record.termsOfServiceCreateAt) || 0;
      if (raw.status) {
        user.status = raw.status;
      }
      if (raw.bot_description) {
        raw.props = Object.assign({}, raw.props, {
          bot_description: raw.bot_description
        });
      }
      if (raw.bot_last_icon_update) {
        raw.props = Object.assign({}, raw.props, {
          bot_last_icon_update: raw.bot_last_icon_update
        });
      }
      user.props = raw.props || null;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: USER,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformPreferenceRecord: Prepares a record of the SERVER database 'Preference' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<PreferenceModel>}
   */
  var transformPreferenceRecord = exports.transformPreferenceRecord = function transformPreferenceRecord(_ref2) {
    var action = _ref2.action,
      database = _ref2.database,
      value = _ref2.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // id of preference comes from server response
    var fieldsMapper = function fieldsMapper(preference) {
      preference._raw.id = isCreateAction ? preference.id : record.id;
      preference.category = raw.category;
      preference.name = raw.name;
      preference.userId = raw.user_id;
      preference.value = raw.value;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: PREFERENCE,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
