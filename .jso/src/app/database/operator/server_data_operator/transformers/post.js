  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformPostsInChannelRecord = exports.transformPostRecord = exports.transformPostInThreadRecord = exports.transformDraftRecord = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER,
    DRAFT = _MM_TABLES$SERVER.DRAFT,
    POST = _MM_TABLES$SERVER.POST,
    POSTS_IN_CHANNEL = _MM_TABLES$SERVER.POSTS_IN_CHANNEL,
    POSTS_IN_THREAD = _MM_TABLES$SERVER.POSTS_IN_THREAD;

  /**
   * transformPostRecord: Prepares a record of the SERVER database 'Post' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<PostModel>}
   */
  var transformPostRecord = exports.transformPostRecord = function transformPostRecord(_ref) {
    var action = _ref.action,
      database = _ref.database,
      value = _ref.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // If isCreateAction is true, we will use the id (API response) from the RAW, else we shall use the existing record id from the database
    var fieldsMapper = function fieldsMapper(post) {
      var _raw$id, _raw$metadata, _raw$prev_post_id, _raw$type, _raw$props;
      post._raw.id = isCreateAction ? (_raw$id = raw == null ? undefined : raw.id) != null ? _raw$id : post.id : record.id;
      post.channelId = raw.channel_id;
      post.createAt = raw.create_at;
      post.deleteAt = raw.delete_at || raw.delete_at === 0 ? raw == null ? undefined : raw.delete_at : 0;
      post.editAt = raw.edit_at;
      post.updateAt = raw.update_at;
      post.isPinned = Boolean(raw.is_pinned);
      post.message = raw.message;
      post.messageSource = raw.message_source || '';

      // When we extract the posts from the threads, we don't get the metadata
      // So, it might not be present in the raw post, so we use the one from the record
      var metadata = (_raw$metadata = raw.metadata) != null ? _raw$metadata : post.metadata;
      post.metadata = metadata && Object.keys(metadata).length ? metadata : null;
      post.userId = raw.user_id;
      post.originalId = raw.original_id;
      post.pendingPostId = raw.pending_post_id;
      post.previousPostId = (_raw$prev_post_id = raw.prev_post_id) != null ? _raw$prev_post_id : '';
      post.rootId = raw.root_id;
      post.type = (_raw$type = raw.type) != null ? _raw$type : '';
      post.props = (_raw$props = raw.props) != null ? _raw$props : {};
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: POST,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformPostInThreadRecord: Prepares a record of the SERVER database 'PostsInThread' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<PostsInThreadModel>}
   */
  var transformPostInThreadRecord = exports.transformPostInThreadRecord = function transformPostInThreadRecord(_ref2) {
    var action = _ref2.action,
      database = _ref2.database,
      value = _ref2.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;
    var fieldsMapper = function fieldsMapper(postsInThread) {
      postsInThread._raw.id = isCreateAction ? raw.id || postsInThread.id : record.id;
      postsInThread.rootId = raw.root_id;
      postsInThread.earliest = raw.earliest;
      postsInThread.latest = raw.latest;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: POSTS_IN_THREAD,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformDraftRecord: Prepares a record of the SERVER database 'Draft' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<DraftModel>}
   */
  var transformDraftRecord = exports.transformDraftRecord = function transformDraftRecord(_ref3) {
    var action = _ref3.action,
      database = _ref3.database,
      value = _ref3.value;
    var emptyFileInfo = [];
    var emptyPostMetadata = {};
    var raw = value.raw;

    // We use the raw id as  Draft is client side only and  we would only be creating/deleting drafts
    var fieldsMapper = function fieldsMapper(draft) {
      var _raw$root_id, _raw$message, _raw$channel_id, _raw$files, _raw$metadata2;
      draft._raw.id = draft.id;
      draft.rootId = (_raw$root_id = raw == null ? undefined : raw.root_id) != null ? _raw$root_id : '';
      draft.message = (_raw$message = raw == null ? undefined : raw.message) != null ? _raw$message : '';
      draft.channelId = (_raw$channel_id = raw == null ? undefined : raw.channel_id) != null ? _raw$channel_id : '';
      draft.files = (_raw$files = raw == null ? undefined : raw.files) != null ? _raw$files : emptyFileInfo;
      draft.metadata = (_raw$metadata2 = raw == null ? undefined : raw.metadata) != null ? _raw$metadata2 : emptyPostMetadata;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: DRAFT,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };

  /**
   * transformPostsInChannelRecord: Prepares a record of the SERVER database 'PostsInChannel' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<PostsInChannelModel>}
   */
  var transformPostsInChannelRecord = exports.transformPostsInChannelRecord = function transformPostsInChannelRecord(_ref4) {
    var action = _ref4.action,
      database = _ref4.database,
      value = _ref4.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;
    var fieldsMapper = function fieldsMapper(postsInChannel) {
      postsInChannel._raw.id = isCreateAction ? raw.id || postsInChannel.id : record.id;
      postsInChannel.channelId = raw.channel_id;
      postsInChannel.earliest = raw.earliest;
      postsInChannel.latest = raw.latest;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: POSTS_IN_CHANNEL,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
