  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformReactionRecord = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var REACTION = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER.REACTION;

  /**
   * transformReactionRecord: Prepares a record of the SERVER database 'Reaction' table for update or create actions.
   * @param {TransformerArgs} operator
   * @param {Database} operator.database
   * @param {RecordPair} operator.value
   * @returns {Promise<ReactionModel>}
   */
  var transformReactionRecord = exports.transformReactionRecord = function transformReactionRecord(_ref) {
    var action = _ref.action,
      database = _ref.database,
      value = _ref.value;
    var raw = value.raw;
    var record = value.record;
    var isCreateAction = action === _$$_REQUIRE(_dependencyMap[0]).OperationType.CREATE;

    // id of reaction comes from server response
    var fieldsMapper = function fieldsMapper(reaction) {
      var _raw$id;
      reaction._raw.id = isCreateAction ? (_raw$id = raw == null ? undefined : raw.id) != null ? _raw$id : reaction.id : record.id;
      reaction.userId = raw.user_id;
      reaction.postId = raw.post_id;
      reaction.emojiName = raw.emoji_name;
      reaction.createAt = raw.create_at;
    };
    return (0, _$$_REQUIRE(_dependencyMap[1]).prepareBaseRecord)({
      action: action,
      database: database,
      tableName: REACTION,
      value: value,
      fieldsMapper: fieldsMapper
    });
  };
