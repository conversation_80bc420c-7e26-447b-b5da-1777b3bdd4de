  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.prepareBaseRecord = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  /**
   * prepareBaseRecord:  This is the last step for each operator and depending on the 'action', it will either prepare an
   * existing record for UPDATE or prepare a collection for CREATE
   *
   * @param {TransformerArgs} operatorBase
   * @param {Database} operatorBase.database
   * @param {string} operatorBase.tableName
   * @param {RecordPair} operatorBase.value
   * @param {((PrepareBaseRecordArgs) => void)} operatorBase.generator
   * @returns {Promise<Model>}
   */
  var prepareBaseRecord = exports.prepareBaseRecord = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* (_ref) {
      var action = _ref.action,
        database = _ref.database,
        tableName = _ref.tableName,
        value = _ref.value,
        fieldsMapper = _ref.fieldsMapper;
      if (action === _$$_REQUIRE(_dependencyMap[2]).OperationType.UPDATE) {
        var record = value.record;
        return record.prepareUpdate(function () {
          return fieldsMapper(record);
        });
      }
      return database.collections.get(tableName).prepareCreate(fieldsMapper);
    });
    return function prepareBaseRecord(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
