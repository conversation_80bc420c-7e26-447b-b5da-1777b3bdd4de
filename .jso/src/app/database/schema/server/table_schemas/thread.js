  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.tableSchemaSpec = exports.default = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var THREAD = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER.THREAD;
  var tableSchemaSpec = exports.tableSchemaSpec = {
    name: THREAD,
    columns: [{
      name: 'is_following',
      type: 'boolean'
    }, {
      name: 'last_reply_at',
      type: 'number'
    }, {
      name: 'last_viewed_at',
      type: 'number'
    }, {
      name: 'reply_count',
      type: 'number'
    }, {
      name: 'unread_mentions',
      type: 'number'
    }, {
      name: 'unread_replies',
      type: 'number'
    }, {
      name: 'viewed_at',
      type: 'number'
    }, {
      name: 'last_fetched_at',
      type: 'number',
      isIndexed: true
    }]
  };
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[1]).tableSchema)(tableSchemaSpec);
