  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var MY_CHANNEL = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER.MY_CHANNEL;
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[1]).tableSchema)({
    name: MY_CHANNEL,
    columns: [{
      name: 'is_unread',
      type: 'boolean'
    }, {
      name: 'last_post_at',
      type: 'number'
    }, {
      name: 'last_viewed_at',
      type: 'number'
    }, {
      name: 'manually_unread',
      type: 'boolean'
    }, {
      name: 'mentions_count',
      type: 'number'
    }, {
      name: 'message_count',
      type: 'number'
    }, {
      name: 'roles',
      type: 'string'
    }, {
      name: 'viewed_at',
      type: 'number'
    }, {
      name: 'last_fetched_at',
      type: 'number',
      isIndexed: true
    }]
  });
