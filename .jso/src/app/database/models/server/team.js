  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _Model2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _class, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _TeamModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[11]).MM_TABLES.SERVER,
    CATEGORY = _MM_TABLES$SERVER.CATEGORY,
    CHANNEL = _MM_TABLES$SERVER.CHANNEL,
    TEAM = _MM_TABLES$SERVER.TEAM,
    MY_TEAM = _MM_TABLES$SERVER.MY_TEAM,
    TEAM_CHANNEL_HISTORY = _MM_TABLES$SERVER.TEAM_CHANNEL_HISTORY,
    TEAM_MEMBERSHIP = _MM_TABLES$SERVER.TEAM_MEMBERSHIP,
    TEAM_SEARCH_HISTORY = _MM_TABLES$SERVER.TEAM_SEARCH_HISTORY,
    THREADS_IN_TEAM = _MM_TABLES$SERVER.THREADS_IN_TEAM,
    THREAD = _MM_TABLES$SERVER.THREAD;

  /**
   * A Team houses and enables communication to happen across channels and users.
   */
  var TeamModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[12]).field)('is_allow_open_invite'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('description'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('display_name'), _dec4 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('is_group_constrained'), _dec5 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('last_team_icon_updated_at'), _dec6 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('name'), _dec7 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('update_at'), _dec8 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('type'), _dec9 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('allowed_domains'), _dec10 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('invite_id'), _dec11 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(CATEGORY), _dec12 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(CHANNEL), _dec13 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(MY_TEAM, 'id'), _dec14 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(TEAM_CHANNEL_HISTORY, 'id'), _dec15 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(TEAM_MEMBERSHIP), _dec16 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(TEAM_SEARCH_HISTORY), _class = (_TeamModel = /*#__PURE__*/function (_Model) {
    function TeamModel() {
      var _this;
      (0, _classCallCheck2.default)(this, TeamModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, TeamModel, [].concat(args));
      /** is_allow_open_invite : Boolean flag indicating if this team is open to the public */
      (0, _initializerDefineProperty2.default)(_this, "isAllowOpenInvite", _descriptor, _this);
      /** description : The description for the team */
      (0, _initializerDefineProperty2.default)(_this, "description", _descriptor2, _this);
      /** display_name : The display name for the team */
      (0, _initializerDefineProperty2.default)(_this, "displayName", _descriptor3, _this);
      /** is_group_constrained : Boolean flag indicating if members are managed groups */
      (0, _initializerDefineProperty2.default)(_this, "isGroupConstrained", _descriptor4, _this);
      /** last_team_icon_updated_at : Timestamp for when this team's icon has been updated last */
      (0, _initializerDefineProperty2.default)(_this, "lastTeamIconUpdatedAt", _descriptor5, _this);
      /** name : The name for the team */
      (0, _initializerDefineProperty2.default)(_this, "name", _descriptor6, _this);
      /** update_at : The timestamp to when this team was last updated on the server */
      (0, _initializerDefineProperty2.default)(_this, "updateAt", _descriptor7, _this);
      /** type : The type of team ( e.g. open/private ) */
      (0, _initializerDefineProperty2.default)(_this, "type", _descriptor8, _this);
      /** allowed_domains : List of domains that can join this team */
      (0, _initializerDefineProperty2.default)(_this, "allowedDomains", _descriptor9, _this);
      /** invite_id : The token id to use in invites to the team */
      (0, _initializerDefineProperty2.default)(_this, "inviteId", _descriptor10, _this);
      /** categories : All the categories associated with this team */
      (0, _initializerDefineProperty2.default)(_this, "categories", _descriptor11, _this);
      /** channels : All the channels associated with this team */
      (0, _initializerDefineProperty2.default)(_this, "channels", _descriptor12, _this);
      /** myTeam : Retrieves additional information about the team that this user is possibly part of. */
      (0, _initializerDefineProperty2.default)(_this, "myTeam", _descriptor13, _this);
      /** teamChannelHistory : A history of the channels in this team that has been visited,  ordered by the most recent and capped to the last 5 */
      (0, _initializerDefineProperty2.default)(_this, "teamChannelHistory", _descriptor14, _this);
      /** members : All the users associated with this team */
      (0, _initializerDefineProperty2.default)(_this, "members", _descriptor15, _this);
      /** teamSearchHistories : All the searches performed on this team */
      (0, _initializerDefineProperty2.default)(_this, "teamSearchHistories", _descriptor16, _this);
      /** threads : Threads list belonging to a team */
      (0, _initializerDefineProperty2.default)(_this, "threadsList", _descriptor17, _this);
      /** unreadThreadsList : Unread threads list belonging to a team */
      (0, _initializerDefineProperty2.default)(_this, "unreadThreadsList", _descriptor18, _this);
      return _this;
    }
    (0, _inherits2.default)(TeamModel, _Model);
    return (0, _createClass2.default)(TeamModel);
  }(_Model2.default), _TeamModel.table = TEAM, _TeamModel.associations = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, CATEGORY, {
    type: 'has_many',
    foreignKey: 'team_id'
  }), CHANNEL, {
    type: 'has_many',
    foreignKey: 'team_id'
  }), MY_TEAM, {
    type: 'has_many',
    foreignKey: 'id'
  }), TEAM_MEMBERSHIP, {
    type: 'has_many',
    foreignKey: 'team_id'
  }), TEAM_SEARCH_HISTORY, {
    type: 'has_many',
    foreignKey: 'team_id'
  }), THREADS_IN_TEAM, {
    type: 'has_many',
    foreignKey: 'team_id'
  }), TEAM_CHANNEL_HISTORY, {
    type: 'has_many',
    foreignKey: 'id'
  }), _TeamModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "isAllowOpenInvite", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "description", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "displayName", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor4 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "isGroupConstrained", [_dec4], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor5 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "lastTeamIconUpdatedAt", [_dec5], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor6 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "name", [_dec6], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor7 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "updateAt", [_dec7], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor8 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "type", [_dec8], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor9 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "allowedDomains", [_dec9], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor10 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "inviteId", [_dec10], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor11 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "categories", [_dec11], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor12 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "channels", [_dec12], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor13 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "myTeam", [_dec13], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor14 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "teamChannelHistory", [_dec14], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor15 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "members", [_dec15], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor16 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "teamSearchHistories", [_dec16], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor17 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "threadsList", [_$$_REQUIRE(_dependencyMap[12]).lazy], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: function initializer() {
      return this.collections.get(THREAD).query(_$$_REQUIRE(_dependencyMap[13]).Q.on(THREADS_IN_TEAM, 'team_id', this.id), _$$_REQUIRE(_dependencyMap[13]).Q.and(_$$_REQUIRE(_dependencyMap[13]).Q.where('reply_count', _$$_REQUIRE(_dependencyMap[13]).Q.gt(0)), _$$_REQUIRE(_dependencyMap[13]).Q.where('is_following', true)), _$$_REQUIRE(_dependencyMap[13]).Q.sortBy('last_reply_at', _$$_REQUIRE(_dependencyMap[13]).Q.desc));
    }
  }), _descriptor18 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "unreadThreadsList", [_$$_REQUIRE(_dependencyMap[12]).lazy], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: function initializer() {
      return this.collections.get(THREAD).query(_$$_REQUIRE(_dependencyMap[13]).Q.on(THREADS_IN_TEAM, 'team_id', this.id), _$$_REQUIRE(_dependencyMap[13]).Q.and(_$$_REQUIRE(_dependencyMap[13]).Q.where('reply_count', _$$_REQUIRE(_dependencyMap[13]).Q.gt(0)), _$$_REQUIRE(_dependencyMap[13]).Q.where('is_following', true), _$$_REQUIRE(_dependencyMap[13]).Q.where('unread_replies', _$$_REQUIRE(_dependencyMap[13]).Q.gt(0))), _$$_REQUIRE(_dependencyMap[13]).Q.sortBy('last_reply_at', _$$_REQUIRE(_dependencyMap[13]).Q.desc));
    }
  }), _class);
