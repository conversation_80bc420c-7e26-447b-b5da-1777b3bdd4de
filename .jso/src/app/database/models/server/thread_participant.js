  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _Model2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dec, _dec2, _dec3, _dec4, _class, _descriptor, _descriptor2, _descriptor3, _descriptor4, _ThreadParticipantModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[11]).MM_TABLES.SERVER,
    THREAD = _MM_TABLES$SERVER.THREAD,
    THREAD_PARTICIPANT = _MM_TABLES$SERVER.THREAD_PARTICIPANT,
    USER = _MM_TABLES$SERVER.USER;

  /**
   * The Thread Participants model contains participants data of a thread.
   */
  var ThreadParticipantModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[12]).field)('thread_id'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('user_id'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(THREAD, 'thread_id'), _dec4 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(USER, 'user_id'), _class = (_ThreadParticipantModel = /*#__PURE__*/function (_Model) {
    function ThreadParticipantModel() {
      var _this;
      (0, _classCallCheck2.default)(this, ThreadParticipantModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, ThreadParticipantModel, [].concat(args));
      /** thread_id : thread id to which participant belong to. */
      (0, _initializerDefineProperty2.default)(_this, "threadId", _descriptor, _this);
      /** user_id : user id of the participant. */
      (0, _initializerDefineProperty2.default)(_this, "userId", _descriptor2, _this);
      /** thread : The related record of the Thread model */
      (0, _initializerDefineProperty2.default)(_this, "thread", _descriptor3, _this);
      /** user : The related record of the User model */
      (0, _initializerDefineProperty2.default)(_this, "user", _descriptor4, _this);
      return _this;
    }
    (0, _inherits2.default)(ThreadParticipantModel, _Model);
    return (0, _createClass2.default)(ThreadParticipantModel);
  }(_Model2.default), _ThreadParticipantModel.table = THREAD_PARTICIPANT, _ThreadParticipantModel.associations = (0, _defineProperty2.default)((0, _defineProperty2.default)({}, THREAD, {
    type: 'belongs_to',
    key: 'thread_id'
  }), USER, {
    type: 'belongs_to',
    key: 'user_id'
  }), _ThreadParticipantModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "threadId", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "userId", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "thread", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor4 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "user", [_dec4], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
