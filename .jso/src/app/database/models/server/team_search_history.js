  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _Model2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dec, _dec2, _dec3, _dec4, _dec5, _class, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _TeamSearchHistoryModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[11]).MM_TABLES.SERVER,
    TEAM = _MM_TABLES$SERVER.TEAM,
    TEAM_SEARCH_HISTORY = _MM_TABLES$SERVER.TEAM_SEARCH_HISTORY;

  /**
   * The TeamSearchHistory model holds the term searched within a team.  The searches are performed
   * at team level in the app.
   */
  var TeamSearchHistoryModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[12]).field)('created_at'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('team_id'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('display_term'), _dec4 = (0, _$$_REQUIRE(_dependencyMap[12]).text)('term'), _dec5 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(TEAM, 'team_id'), _class = (_TeamSearchHistoryModel = /*#__PURE__*/function (_Model) {
    function TeamSearchHistoryModel() {
      var _this;
      (0, _classCallCheck2.default)(this, TeamSearchHistoryModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, TeamSearchHistoryModel, [].concat(args));
      /** created_at : The timestamp at which this search was performed */
      (0, _initializerDefineProperty2.default)(_this, "createdAt", _descriptor, _this);
      /** team_id : The foreign key to the parent Team model */
      (0, _initializerDefineProperty2.default)(_this, "teamId", _descriptor2, _this);
      /** display_term : The term that we display to the user */
      (0, _initializerDefineProperty2.default)(_this, "displayTerm", _descriptor3, _this);
      /** term : The term that is sent to the server to perform the search */
      (0, _initializerDefineProperty2.default)(_this, "term", _descriptor4, _this);
      /** team : The related record to the parent team model */
      (0, _initializerDefineProperty2.default)(_this, "team", _descriptor5, _this);
      return _this;
    }
    (0, _inherits2.default)(TeamSearchHistoryModel, _Model);
    return (0, _createClass2.default)(TeamSearchHistoryModel);
  }(_Model2.default), _TeamSearchHistoryModel.table = TEAM_SEARCH_HISTORY, _TeamSearchHistoryModel.associations = (0, _defineProperty2.default)({}, TEAM, {
    type: 'belongs_to',
    key: 'team_id'
  }), _TeamSearchHistoryModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "createdAt", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "teamId", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "displayTerm", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor4 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "term", [_dec4], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor5 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "team", [_dec5], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
