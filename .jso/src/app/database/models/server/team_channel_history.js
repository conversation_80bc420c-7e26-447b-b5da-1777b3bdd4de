  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _Model2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dec, _dec2, _class, _descriptor, _descriptor2, _TeamChannelHistoryModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[11]).MM_TABLES.SERVER,
    TEAM = _MM_TABLES$SERVER.TEAM,
    TEAM_CHANNEL_HISTORY = _MM_TABLES$SERVER.TEAM_CHANNEL_HISTORY;

  /**
   * The TeamChannelHistory model helps keeping track of the last channel visited
   * by the user.
   */
  var TeamChannelHistoryModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[12]).json)('channel_ids', _$$_REQUIRE(_dependencyMap[13]).safeParseJSON), _dec2 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(TEAM, 'id'), _class = (_TeamChannelHistoryModel = /*#__PURE__*/function (_Model) {
    function TeamChannelHistoryModel() {
      var _this;
      (0, _classCallCheck2.default)(this, TeamChannelHistoryModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, TeamChannelHistoryModel, [].concat(args));
      /** channel_ids : An array containing the last 5 channels visited within this team order by recency */
      (0, _initializerDefineProperty2.default)(_this, "channelIds", _descriptor, _this);
      /** team : The related record from the parent Team model */
      (0, _initializerDefineProperty2.default)(_this, "team", _descriptor2, _this);
      return _this;
    }
    (0, _inherits2.default)(TeamChannelHistoryModel, _Model);
    return (0, _createClass2.default)(TeamChannelHistoryModel);
  }(_Model2.default), _TeamChannelHistoryModel.table = TEAM_CHANNEL_HISTORY, _TeamChannelHistoryModel.associations = (0, _defineProperty2.default)({}, TEAM, {
    type: 'belongs_to',
    key: 'id'
  }), _TeamChannelHistoryModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "channelIds", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "team", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
