  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _Model2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dec, _dec2, _dec3, _dec4, _class, _descriptor, _descriptor2, _descriptor3, _descriptor4, _ThreadInTeamModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[11]).MM_TABLES.SERVER,
    TEAM = _MM_TABLES$SERVER.TEAM,
    THREAD = _MM_TABLES$SERVER.THREAD,
    THREADS_IN_TEAM = _MM_TABLES$SERVER.THREADS_IN_TEAM;

  /**
   * ThreadInTeam model helps us to combine adjacent threads together without leaving
   * gaps in between for an efficient user reading experience for threads.
   */
  var ThreadInTeamModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[12]).field)('thread_id'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('team_id'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(THREAD, 'thread_id'), _dec4 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(TEAM, 'team_id'), _class = (_ThreadInTeamModel = /*#__PURE__*/function (_Model) {
    function ThreadInTeamModel() {
      var _this;
      (0, _classCallCheck2.default)(this, ThreadInTeamModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, ThreadInTeamModel, [].concat(args));
      /** thread_id: Associated thread identifier */
      (0, _initializerDefineProperty2.default)(_this, "threadId", _descriptor, _this);
      /** team_id: Associated team identifier */
      (0, _initializerDefineProperty2.default)(_this, "teamId", _descriptor2, _this);
      (0, _initializerDefineProperty2.default)(_this, "thread", _descriptor3, _this);
      (0, _initializerDefineProperty2.default)(_this, "team", _descriptor4, _this);
      return _this;
    }
    (0, _inherits2.default)(ThreadInTeamModel, _Model);
    return (0, _createClass2.default)(ThreadInTeamModel);
  }(_Model2.default), _ThreadInTeamModel.table = THREADS_IN_TEAM, _ThreadInTeamModel.associations = (0, _defineProperty2.default)((0, _defineProperty2.default)({}, TEAM, {
    type: 'belongs_to',
    key: 'team_id'
  }), THREAD, {
    type: 'belongs_to',
    key: 'thread_id'
  }), _ThreadInTeamModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "threadId", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "teamId", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "thread", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor4 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "team", [_dec4], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
