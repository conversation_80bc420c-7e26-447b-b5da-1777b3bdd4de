  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _Model2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _dec19, _dec20, _dec21, _dec22, _dec23, _dec24, _dec25, _dec26, _dec27, _dec28, _class, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _descriptor19, _descriptor20, _descriptor21, _descriptor22, _descriptor23, _descriptor24, _descriptor25, _descriptor26, _descriptor27, _descriptor28, _UserModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[11]).MM_TABLES.SERVER,
    CHANNEL = _MM_TABLES$SERVER.CHANNEL,
    CHANNEL_BOOKMARK = _MM_TABLES$SERVER.CHANNEL_BOOKMARK,
    CHANNEL_MEMBERSHIP = _MM_TABLES$SERVER.CHANNEL_MEMBERSHIP,
    POST = _MM_TABLES$SERVER.POST,
    PREFERENCE = _MM_TABLES$SERVER.PREFERENCE,
    REACTION = _MM_TABLES$SERVER.REACTION,
    TEAM_MEMBERSHIP = _MM_TABLES$SERVER.TEAM_MEMBERSHIP,
    THREAD_PARTICIPANT = _MM_TABLES$SERVER.THREAD_PARTICIPANT,
    USER = _MM_TABLES$SERVER.USER;

  /**
   * The User model represents the 'USER' table and its relationship to other
   * shareholders in the app.
   */
  var UserModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[12]).field)('auth_service'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('update_at'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('delete_at'), _dec4 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('email'), _dec5 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('first_name'), _dec6 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('is_bot'), _dec7 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('is_guest'), _dec8 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('last_name'), _dec9 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('last_picture_update'), _dec10 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('locale'), _dec11 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('nickname'), _dec12 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('position'), _dec13 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('roles'), _dec14 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('status'), _dec15 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('username'), _dec16 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('remote_id'), _dec17 = (0, _$$_REQUIRE(_dependencyMap[12]).json)('notify_props', _$$_REQUIRE(_dependencyMap[13]).safeParseJSON), _dec18 = (0, _$$_REQUIRE(_dependencyMap[12]).json)('props', _$$_REQUIRE(_dependencyMap[13]).safeParseJSON), _dec19 = (0, _$$_REQUIRE(_dependencyMap[12]).json)('timezone', _$$_REQUIRE(_dependencyMap[13]).safeParseJSON), _dec20 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('terms_of_service_id'), _dec21 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('terms_of_service_create_at'), _dec22 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(CHANNEL), _dec23 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(CHANNEL_MEMBERSHIP), _dec24 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(POST), _dec25 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(PREFERENCE), _dec26 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(REACTION), _dec27 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(TEAM_MEMBERSHIP), _dec28 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(THREAD_PARTICIPANT), _class = (_UserModel = /*#__PURE__*/function (_Model) {
    function UserModel() {
      var _this;
      (0, _classCallCheck2.default)(this, UserModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, UserModel, [].concat(args));
      /** auth_service : The type of authentication service registered to that user */
      (0, _initializerDefineProperty2.default)(_this, "authService", _descriptor, _this);
      /** update_at : The timestamp at which this user account has been updated */
      (0, _initializerDefineProperty2.default)(_this, "updateAt", _descriptor2, _this);
      /** delete_at : The timestamp at which this user account has been archived/deleted */
      (0, _initializerDefineProperty2.default)(_this, "deleteAt", _descriptor3, _this);
      /** email : The email address for that user  */
      (0, _initializerDefineProperty2.default)(_this, "email", _descriptor4, _this);
      /** first_name : The user's first name */
      (0, _initializerDefineProperty2.default)(_this, "firstName", _descriptor5, _this);
      /** is_bot : Boolean flag indicating if the user is a bot */
      (0, _initializerDefineProperty2.default)(_this, "isBot", _descriptor6, _this);
      /** is_guest : Boolean flag indicating if the user is a guest */
      (0, _initializerDefineProperty2.default)(_this, "isGuest", _descriptor7, _this);
      /** last_name : The user's last name */
      (0, _initializerDefineProperty2.default)(_this, "lastName", _descriptor8, _this);
      /** last_picture_update : The timestamp of the last time the profile picture has been updated */
      (0, _initializerDefineProperty2.default)(_this, "lastPictureUpdate", _descriptor9, _this);
      /** locale : The user's locale */
      (0, _initializerDefineProperty2.default)(_this, "locale", _descriptor10, _this);
      /** nickname : The user's nickname */
      (0, _initializerDefineProperty2.default)(_this, "nickname", _descriptor11, _this);
      /** position : The user's position in the company */
      (0, _initializerDefineProperty2.default)(_this, "position", _descriptor12, _this);
      /** roles : The associated roles that this user has. Multiple roles concatenated together with comma to form a single string. */
      (0, _initializerDefineProperty2.default)(_this, "roles", _descriptor13, _this);
      /** status : The presence status for the user */
      (0, _initializerDefineProperty2.default)(_this, "status", _descriptor14, _this);
      /** username : The user's username */
      (0, _initializerDefineProperty2.default)(_this, "username", _descriptor15, _this);
      /** remote_id : The ID of the remote organization that this user belongs to */
      (0, _initializerDefineProperty2.default)(_this, "remoteId", _descriptor16, _this);
      /** notify_props : Notification preferences/configurations */
      (0, _initializerDefineProperty2.default)(_this, "notifyProps", _descriptor17, _this);
      /** props : Custom objects ( e.g. custom status) can be stored in there. Its type definition is known as
       *  'excess property check' in Typescript land.  We keep using it till we build up the final shape of this object.
       */
      (0, _initializerDefineProperty2.default)(_this, "props", _descriptor18, _this);
      /** timezone : The timezone for this user */
      (0, _initializerDefineProperty2.default)(_this, "timezone", _descriptor19, _this);
      /** termsOfServiceId : The id of the last accepted terms of service */
      (0, _initializerDefineProperty2.default)(_this, "termsOfServiceId", _descriptor20, _this);
      /** termsOfServiceCreateAt : The last time the user accepted the terms of service */
      (0, _initializerDefineProperty2.default)(_this, "termsOfServiceCreateAt", _descriptor21, _this);
      /** channelsCreated : All the channels that this user created */
      (0, _initializerDefineProperty2.default)(_this, "channelsCreated", _descriptor22, _this);
      /** channels : All the channels that this user is part of  */
      (0, _initializerDefineProperty2.default)(_this, "channels", _descriptor23, _this);
      /** posts :  All the posts that this user has written*/
      (0, _initializerDefineProperty2.default)(_this, "posts", _descriptor24, _this);
      /** preferences : All user preferences */
      (0, _initializerDefineProperty2.default)(_this, "preferences", _descriptor25, _this);
      /** reactions : All the reactions to posts that this user had */
      (0, _initializerDefineProperty2.default)(_this, "reactions", _descriptor26, _this);
      /** teams : All the team that this user is part of  */
      (0, _initializerDefineProperty2.default)(_this, "teams", _descriptor27, _this);
      /** threadParticipations : All the thread participations this user is part of  */
      (0, _initializerDefineProperty2.default)(_this, "threadParticipations", _descriptor28, _this);
      _this.prepareStatus = function (status) {
        _this.prepareUpdate(function (u) {
          u.status = status;
        });
      };
      return _this;
    }
    (0, _inherits2.default)(UserModel, _Model);
    return (0, _createClass2.default)(UserModel, [{
      key: "mentionKeys",
      get: function get() {
        var keys = [];
        if (!this.notifyProps) {
          return keys;
        }
        if (this.notifyProps.mention_keys) {
          keys = keys.concat(this.notifyProps.mention_keys.split(',').map(function (key) {
            return {
              key: key
            };
          }));
        }
        if (this.notifyProps.first_name === 'true' && this.firstName) {
          keys.push({
            key: this.firstName,
            caseSensitive: true
          });
        }
        if (this.notifyProps.channel === 'true') {
          keys.push({
            key: '@channel'
          });
          keys.push({
            key: '@all'
          });
          keys.push({
            key: '@here'
          });
        }
        var usernameKey = '@' + this.username;
        if (keys.findIndex(function (key) {
          return key.key === usernameKey;
        }) === -1) {
          keys.push({
            key: usernameKey
          });
        }
        return keys;
      }
    }, {
      key: "userMentionKeys",
      get: function get() {
        var mentionKeys = this.mentionKeys;
        return mentionKeys.filter(function (m) {
          return m.key !== '@all' && m.key !== '@channel' && m.key !== '@here';
        });
      }
    }, {
      key: "highlightKeys",
      get: function get() {
        var _this$notifyProps, _this$notifyProps$hig;
        if (!this.notifyProps) {
          return [];
        }
        var highlightWithoutNotificationKeys = [];
        if ((_this$notifyProps = this.notifyProps) != null && (_this$notifyProps$hig = _this$notifyProps.highlight_keys) != null && _this$notifyProps$hig.length) {
          this.notifyProps.highlight_keys.split(',').forEach(function (key) {
            if (key.trim().length > 0) {
              highlightWithoutNotificationKeys.push({
                key: key.trim()
              });
            }
          });
        }
        return highlightWithoutNotificationKeys;
      }
    }]);
  }(_Model2.default), _UserModel.table = USER, _UserModel.associations = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, CHANNEL, {
    type: 'has_many',
    foreignKey: 'creator_id'
  }), CHANNEL_BOOKMARK, {
    type: 'has_many',
    foreignKey: 'owner_id'
  }), CHANNEL_MEMBERSHIP, {
    type: 'has_many',
    foreignKey: 'user_id'
  }), POST, {
    type: 'has_many',
    foreignKey: 'user_id'
  }), PREFERENCE, {
    type: 'has_many',
    foreignKey: 'user_id'
  }), REACTION, {
    type: 'has_many',
    foreignKey: 'user_id'
  }), TEAM_MEMBERSHIP, {
    type: 'has_many',
    foreignKey: 'user_id'
  }), THREAD_PARTICIPANT, {
    type: 'has_many',
    foreignKey: 'user_id'
  }), _UserModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "authService", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "updateAt", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "deleteAt", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor4 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "email", [_dec4], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor5 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "firstName", [_dec5], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor6 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "isBot", [_dec6], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor7 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "isGuest", [_dec7], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor8 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "lastName", [_dec8], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor9 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "lastPictureUpdate", [_dec9], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor10 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "locale", [_dec10], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor11 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "nickname", [_dec11], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor12 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "position", [_dec12], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor13 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "roles", [_dec13], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor14 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "status", [_dec14], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor15 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "username", [_dec15], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor16 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "remoteId", [_dec16], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor17 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "notifyProps", [_dec17], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor18 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "props", [_dec18], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor19 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "timezone", [_dec19], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor20 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "termsOfServiceId", [_dec20], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor21 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "termsOfServiceCreateAt", [_dec21], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor22 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "channelsCreated", [_dec22], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor23 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "channels", [_dec23], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor24 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "posts", [_dec24], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor25 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "preferences", [_dec25], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor26 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "reactions", [_dec26], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor27 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "teams", [_dec27], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor28 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "threadParticipations", [_dec28], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
