  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _Model2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _class, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _ThreadModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[11]).MM_TABLES.SERVER,
    POST = _MM_TABLES$SERVER.POST,
    THREAD = _MM_TABLES$SERVER.THREAD,
    THREAD_PARTICIPANT = _MM_TABLES$SERVER.THREAD_PARTICIPANT,
    THREADS_IN_TEAM = _MM_TABLES$SERVER.THREADS_IN_TEAM;

  /**
   * The Thread model contains thread information of a post.
   */
  var ThreadModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[12]).field)('last_reply_at'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('last_fetched_at'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('last_viewed_at'), _dec4 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('reply_count'), _dec5 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('is_following'), _dec6 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('unread_replies'), _dec7 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('unread_mentions'), _dec8 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('viewed_at'), _dec9 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(THREAD_PARTICIPANT), _dec10 = (0, _$$_REQUIRE(_dependencyMap[12]).children)(THREADS_IN_TEAM), _dec11 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(POST, 'id'), _class = (_ThreadModel = /*#__PURE__*/function (_Model) {
    function ThreadModel() {
      var _this;
      (0, _classCallCheck2.default)(this, ThreadModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, ThreadModel, [].concat(args));
      /** last_reply_at : The timestamp of when user last replied to the thread. */
      (0, _initializerDefineProperty2.default)(_this, "lastReplyAt", _descriptor, _this);
      /** last_last_fetched_at_at : The timestamp when we successfully last fetched post on this thread */
      (0, _initializerDefineProperty2.default)(_this, "lastFetchedAt", _descriptor2, _this);
      /** last_viewed_at : The timestamp of when user last viewed the thread. */
      (0, _initializerDefineProperty2.default)(_this, "lastViewedAt", _descriptor3, _this);
      /** reply_count : The total replies to the thread by all the participants. */
      (0, _initializerDefineProperty2.default)(_this, "replyCount", _descriptor4, _this);
      /** is_following: If user is following the thread or not */
      (0, _initializerDefineProperty2.default)(_this, "isFollowing", _descriptor5, _this);
      /** unread_replies : The number of replies that have not been read by the user. */
      (0, _initializerDefineProperty2.default)(_this, "unreadReplies", _descriptor6, _this);
      /** unread_mentions : The number of mentions that have not been read by the user. */
      (0, _initializerDefineProperty2.default)(_this, "unreadMentions", _descriptor7, _this);
      /** viewed_at : The timestamp showing when the user's last opened this thread (this is used for the new line message indicator) */
      (0, _initializerDefineProperty2.default)(_this, "viewedAt", _descriptor8, _this);
      /** participants : All the participants associated with this Thread */
      (0, _initializerDefineProperty2.default)(_this, "participants", _descriptor9, _this);
      /** threadsInTeam : All the threadsInTeam associated with this Thread */
      (0, _initializerDefineProperty2.default)(_this, "threadsInTeam", _descriptor10, _this);
      /** post : The root post of this thread */
      (0, _initializerDefineProperty2.default)(_this, "post", _descriptor11, _this);
      return _this;
    }
    (0, _inherits2.default)(ThreadModel, _Model);
    return (0, _createClass2.default)(ThreadModel);
  }(_Model2.default), _ThreadModel.table = THREAD, _ThreadModel.associations = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, POST, {
    type: 'belongs_to',
    key: 'id'
  }), THREAD_PARTICIPANT, {
    type: 'has_many',
    foreignKey: 'thread_id'
  }), THREADS_IN_TEAM, {
    type: 'has_many',
    foreignKey: 'thread_id'
  }), _ThreadModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "lastReplyAt", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "lastFetchedAt", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "lastViewedAt", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor4 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "replyCount", [_dec4], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor5 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "isFollowing", [_dec5], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor6 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "unreadReplies", [_dec6], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor7 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "unreadMentions", [_dec7], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor8 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "viewedAt", [_dec8], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor9 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "participants", [_dec9], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor10 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "threadsInTeam", [_dec10], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor11 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "post", [_dec11], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
