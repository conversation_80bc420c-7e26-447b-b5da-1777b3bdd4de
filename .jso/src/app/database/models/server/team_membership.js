  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _Model2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dec, _dec2, _dec3, _dec4, _dec5, _class, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _TeamMembershipModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[11]).MM_TABLES.SERVER,
    TEAM = _MM_TABLES$SERVER.TEAM,
    TEAM_MEMBERSHIP = _MM_TABLES$SERVER.TEAM_MEMBERSHIP,
    USER = _MM_TABLES$SERVER.USER;

  /**
   * The TeamMembership model represents the 'association table' where many teams have users and many users are in
   * teams (relationship type N:N)
   */
  var TeamMembershipModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[12]).field)('team_id'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('user_id'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('scheme_admin'), _dec4 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(USER, 'user_id'), _dec5 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(TEAM, 'team_id'), _class = (_TeamMembershipModel = /*#__PURE__*/function (_Model) {
    function TeamMembershipModel() {
      var _this;
      (0, _classCallCheck2.default)(this, TeamMembershipModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, TeamMembershipModel, [].concat(args));
      /** team_id : The foreign key to the related Team record */
      (0, _initializerDefineProperty2.default)(_this, "teamId", _descriptor, _this);
      /* user_id: The foreign key to the related User record*/
      (0, _initializerDefineProperty2.default)(_this, "userId", _descriptor2, _this);
      /* scheme_admin: Determines if the user is an admin of the team*/
      (0, _initializerDefineProperty2.default)(_this, "schemeAdmin", _descriptor3, _this);
      /** memberUser: The related user in the team */
      (0, _initializerDefineProperty2.default)(_this, "memberUser", _descriptor4, _this);
      /** memberTeam : The related team of users */
      (0, _initializerDefineProperty2.default)(_this, "memberTeam", _descriptor5, _this);
      /**
       * getAllTeamsForUser - Retrieves all the teams that the user is part of
       */
      (0, _initializerDefineProperty2.default)(_this, "getAllTeamsForUser", _descriptor6, _this);
      /**
       * getAllUsersInTeam - Retrieves all the users who are part of this team
       */
      (0, _initializerDefineProperty2.default)(_this, "getAllUsersInTeam", _descriptor7, _this);
      return _this;
    }
    (0, _inherits2.default)(TeamMembershipModel, _Model);
    return (0, _createClass2.default)(TeamMembershipModel);
  }(_Model2.default), _TeamMembershipModel.table = TEAM_MEMBERSHIP, _TeamMembershipModel.associations = (0, _defineProperty2.default)((0, _defineProperty2.default)({}, TEAM, {
    type: 'belongs_to',
    key: 'team_id'
  }), USER, {
    type: 'belongs_to',
    key: 'user_id'
  }), _TeamMembershipModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "teamId", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "userId", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "schemeAdmin", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor4 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "memberUser", [_dec4], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor5 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "memberTeam", [_dec5], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor6 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "getAllTeamsForUser", [_$$_REQUIRE(_dependencyMap[12]).lazy], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: function initializer() {
      return this.collections.get(TEAM).query(_$$_REQUIRE(_dependencyMap[13]).Q.on(USER, 'id', this.userId));
    }
  }), _descriptor7 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "getAllUsersInTeam", [_$$_REQUIRE(_dependencyMap[12]).lazy], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: function initializer() {
      return this.collections.get(USER).query(_$$_REQUIRE(_dependencyMap[13]).Q.on(TEAM, 'id', this.teamId));
    }
  }), _class);
