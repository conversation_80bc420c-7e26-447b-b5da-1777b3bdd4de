  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _Model2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dec, _dec2, _dec3, _class, _descriptor, _descriptor2, _descriptor3, _TeamThreadsSyncModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[11]).MM_TABLES.SERVER,
    TEAM = _MM_TABLES$SERVER.TEAM,
    TEAM_THREADS_SYNC = _MM_TABLES$SERVER.TEAM_THREADS_SYNC;

  /**
   * ThreadInTeam model helps us to sync threads without creating any gaps between the threads
   * by keeping track of the latest and earliest last_replied_at timestamps loaded for a team.
   */
  var TeamThreadsSyncModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[12]).field)('earliest'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[12]).field)('latest'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[12]).immutableRelation)(TEAM, 'id'), _class = (_TeamThreadsSyncModel = /*#__PURE__*/function (_Model) {
    function TeamThreadsSyncModel() {
      var _this;
      (0, _classCallCheck2.default)(this, TeamThreadsSyncModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, TeamThreadsSyncModel, [].concat(args));
      /** earliest: Oldest last_replied_at loaded through infinite loading */
      (0, _initializerDefineProperty2.default)(_this, "earliest", _descriptor, _this);
      /** latest: Newest last_replied_at loaded during app init / navigating to global threads / pull to refresh */
      (0, _initializerDefineProperty2.default)(_this, "latest", _descriptor2, _this);
      (0, _initializerDefineProperty2.default)(_this, "team", _descriptor3, _this);
      return _this;
    }
    (0, _inherits2.default)(TeamThreadsSyncModel, _Model);
    return (0, _createClass2.default)(TeamThreadsSyncModel);
  }(_Model2.default), _TeamThreadsSyncModel.table = TEAM_THREADS_SYNC, _TeamThreadsSyncModel.associations = (0, _defineProperty2.default)({}, TEAM, {
    type: 'belongs_to',
    key: 'id'
  }), _TeamThreadsSyncModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "earliest", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "latest", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "team", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
