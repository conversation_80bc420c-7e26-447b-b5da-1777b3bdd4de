  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _dec, _class, _descriptor, _GlobalModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var GLOBAL = _$$_REQUIRE(_dependencyMap[9]).MM_TABLES.APP.GLOBAL;

  /**
   * The Global model will act as a dictionary of name-value pairs.  The value field can be a JSON object or any other
   * data type.  It will hold information that applies to the whole app ( e.g. sidebar settings for tablets)
   */
  var GlobalModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[10]).json)('value', _$$_REQUIRE(_dependencyMap[11]).safeParseJSON), _class = (_GlobalModel = /*#__PURE__*/function (_Model) {
    function GlobalModel() {
      var _this;
      (0, _classCallCheck2.default)(this, GlobalModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, GlobalModel, [].concat(args));
      /** value : The value part of the key-value combination and whose key will be the id column  */
      (0, _initializerDefineProperty2.default)(_this, "value", _descriptor, _this);
      return _this;
    }
    (0, _inherits2.default)(GlobalModel, _Model);
    return (0, _createClass2.default)(GlobalModel);
  }(_$$_REQUIRE(_dependencyMap[12]).Model), _GlobalModel.table = GLOBAL, _GlobalModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "value", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
