  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _dec, _dec2, _dec3, _class, _descriptor, _descriptor2, _descriptor3, _InfoModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var INFO = _$$_REQUIRE(_dependencyMap[9]).MM_TABLES.APP.INFO;

  /**
   * The App model will hold information - such as the version number, build number and creation date -
   * for the Mattermost mobile app.
   */
  var InfoModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[10]).field)('build_number'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[10]).field)('created_at'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[10]).field)('version_number'), _class = (_InfoModel = /*#__PURE__*/function (_Model) {
    function InfoModel() {
      var _this;
      (0, _classCallCheck2.default)(this, InfoModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, InfoModel, [].concat(args));
      /** build_number : Build number for the app */
      (0, _initializerDefineProperty2.default)(_this, "buildNumber", _descriptor, _this);
      /** created_at : Date of creation for this version */
      (0, _initializerDefineProperty2.default)(_this, "createdAt", _descriptor2, _this);
      /** version_number : Version number for the app */
      (0, _initializerDefineProperty2.default)(_this, "versionNumber", _descriptor3, _this);
      return _this;
    }
    (0, _inherits2.default)(InfoModel, _Model);
    return (0, _createClass2.default)(InfoModel);
  }(_$$_REQUIRE(_dependencyMap[11]).Model), _InfoModel.table = INFO, _InfoModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "buildNumber", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "createdAt", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "versionNumber", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
