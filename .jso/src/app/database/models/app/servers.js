  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _initializerDefineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _applyDecoratedDescriptor2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _initializerWarningHelper2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _dec, _dec2, _dec3, _dec4, _dec5, _class, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _ServersModel; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var SERVERS = _$$_REQUIRE(_dependencyMap[9]).MM_TABLES.APP.SERVERS;

  /**
   * The Server model will help us to identify the various servers a user will log in; in the context of
   * multi-server support system.  The db_path field will hold the App-Groups file-path
   */
  var ServersModel = exports.default = (_dec = (0, _$$_REQUIRE(_dependencyMap[10]).field)('db_path'), _dec2 = (0, _$$_REQUIRE(_dependencyMap[10]).field)('display_name'), _dec3 = (0, _$$_REQUIRE(_dependencyMap[10]).field)('url'), _dec4 = (0, _$$_REQUIRE(_dependencyMap[10]).field)('last_active_at'), _dec5 = (0, _$$_REQUIRE(_dependencyMap[10]).field)('identifier'), _class = (_ServersModel = /*#__PURE__*/function (_Model) {
    function ServersModel() {
      var _this;
      (0, _classCallCheck2.default)(this, ServersModel);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, ServersModel, [].concat(args));
      /** db_path : The file path where the database is stored */
      (0, _initializerDefineProperty2.default)(_this, "dbPath", _descriptor, _this);
      /** display_name : The server display name */
      (0, _initializerDefineProperty2.default)(_this, "displayName", _descriptor2, _this);
      /** url : The online address for the Mattermost server */
      (0, _initializerDefineProperty2.default)(_this, "url", _descriptor3, _this);
      /** last_active_at: The last time this server was active */
      (0, _initializerDefineProperty2.default)(_this, "lastActiveAt", _descriptor4, _this);
      /** identifier: Determines the installation identifier of a server */
      (0, _initializerDefineProperty2.default)(_this, "identifier", _descriptor5, _this);
      return _this;
    }
    (0, _inherits2.default)(ServersModel, _Model);
    return (0, _createClass2.default)(ServersModel);
  }(_$$_REQUIRE(_dependencyMap[11]).Model), _ServersModel.table = SERVERS, _ServersModel), _descriptor = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "dbPath", [_dec], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor2 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "displayName", [_dec2], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor3 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "url", [_dec3], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor4 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "lastActiveAt", [_dec4], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _descriptor5 = (0, _applyDecoratedDescriptor2.default)(_class.prototype, "identifier", [_dec5], {
    configurable: true,
    enumerable: true,
    writable: true,
    initializer: null
  }), _class);
