  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.queryCustomEmojisByName = exports.queryAllCustomEmojis = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var queryAllCustomEmojis = exports.queryAllCustomEmojis = function queryAllCustomEmojis(database) {
    return database.get(_$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER.CUSTOM_EMOJI).query();
  };
  var queryCustomEmojisByName = exports.queryCustomEmojisByName = function queryCustomEmojisByName(database, names) {
    return database.get(_$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER.CUSTOM_EMOJI).query(_$$_REQUIRE(_dependencyMap[1]).Q.where('name', _$$_REQUIRE(_dependencyMap[1]).Q.oneOf(names)));
  };
