  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.getDraftsLenth = exports.getDraft = undefined;
  exports.observeFirstDraft = observeFirstDraft;
  exports.queryDrafts = exports.queryDraftNoBelongToChannel = exports.queryDraft = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var DRAFT = _$$_REQUIRE(_dependencyMap[2]).MM_TABLES.SERVER.DRAFT;
  var getDraft = exports.getDraft = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (database, channelId) {
      var rootId = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';
      var record = yield queryDraft(database, channelId, rootId).fetch();

      // Check done to force types
      if (record.length) {
        return record[0];
      }
      return undefined;
    });
    return function getDraft(_x, _x2) {
      return _ref.apply(this, arguments);
    };
  }();
  var getDraftsLenth = exports.getDraftsLenth = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* (database) {
      var rootId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
      var record = yield queryDrafts(database, rootId).fetch();

      // Check done to force types
      if (record.length) {
        return record.length;
      }
      return 0;
    });
    return function getDraftsLenth(_x3) {
      return _ref2.apply(this, arguments);
    };
  }();
  var queryDraft = exports.queryDraft = function queryDraft(database, channelId) {
    var rootId = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';
    return database.collections.get(DRAFT).query(_$$_REQUIRE(_dependencyMap[3]).Q.where('channel_id', channelId), _$$_REQUIRE(_dependencyMap[3]).Q.where('root_id', rootId));
  };
  var queryDraftNoBelongToChannel = exports.queryDraftNoBelongToChannel = function queryDraftNoBelongToChannel(database, channelId) {
    var rootId = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';
    return database.collections.get(DRAFT).query(_$$_REQUIRE(_dependencyMap[3]).Q.where('channel_id', _$$_REQUIRE(_dependencyMap[3]).Q.notEq(channelId)), _$$_REQUIRE(_dependencyMap[3]).Q.where('root_id', rootId));
  };
  var queryDrafts = exports.queryDrafts = function queryDrafts(database) {
    var rootId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
    return database.collections.get(DRAFT).query(_$$_REQUIRE(_dependencyMap[3]).Q.where('root_id', rootId));
  };
  function observeFirstDraft(v) {
    var _v$;
    return ((_v$ = v[0]) == null ? undefined : _v$.observe()) || (0, _$$_REQUIRE(_dependencyMap[4]).of)(undefined);
  }
