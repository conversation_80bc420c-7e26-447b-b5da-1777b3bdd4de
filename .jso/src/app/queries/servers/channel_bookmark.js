  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.queryBookmarks = exports.observeCanEditBookmarks = exports.observeCanDeleteBookmarks = exports.observeCanAddBookmarks = exports.observeBookmarks = exports.getChannelBookmarkById = exports.getBookmarksSince = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var CHANNEL_BOOKMARK = _$$_REQUIRE(_dependencyMap[3]).MM_TABLES.SERVER.CHANNEL_BOOKMARK;
  var observeHasPermissionToBookmarks = function observeHasPermissionToBookmarks(database, channelId, public_permission, private_permission) {
    var serverVersion = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigValue)(database, 'Version');
    var currentUser = (0, _$$_REQUIRE(_dependencyMap[5]).observeCurrentUser)(database);
    return (0, _$$_REQUIRE(_dependencyMap[6]).observeChannel)(database, channelId).pipe((0, _$$_REQUIRE(_dependencyMap[7]).combineLatestWith)(currentUser, serverVersion), (0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref) {
      var _ref2 = (0, _slicedToArray2.default)(_ref, 3),
        c = _ref2[0],
        user = _ref2[1],
        version = _ref2[2];
      if (!c || !user || c.deleteAt !== 0 || user != null && user.isGuest || !(0, _$$_REQUIRE(_dependencyMap[8]).isMinimumServerVersion)(version || '', 9, 4)) {
        return (0, _$$_REQUIRE(_dependencyMap[9]).of)(false);
      }
      if ((0, _$$_REQUIRE(_dependencyMap[10]).isDMorGM)(c)) {
        return (0, _$$_REQUIRE(_dependencyMap[9]).of)(true);
      }
      var permission = c.type === _$$_REQUIRE(_dependencyMap[11]).General.OPEN_CHANNEL ? public_permission : private_permission;
      return (0, _$$_REQUIRE(_dependencyMap[12]).observePermissionForChannel)(database, c, user, permission, true);
    }), (0, _$$_REQUIRE(_dependencyMap[7]).distinctUntilChanged)());
  };
  var observeCanAddBookmarks = exports.observeCanAddBookmarks = function observeCanAddBookmarks(database, channelId) {
    return observeHasPermissionToBookmarks(database, channelId, _$$_REQUIRE(_dependencyMap[11]).Permissions.ADD_BOOKMARK_PUBLIC_CHANNEL, _$$_REQUIRE(_dependencyMap[11]).Permissions.ADD_BOOKMARK_PRIVATE_CHANNEL);
  };
  var observeCanEditBookmarks = exports.observeCanEditBookmarks = function observeCanEditBookmarks(database, channelId) {
    return observeHasPermissionToBookmarks(database, channelId, _$$_REQUIRE(_dependencyMap[11]).Permissions.EDIT_BOOKMARK_PUBLIC_CHANNEL, _$$_REQUIRE(_dependencyMap[11]).Permissions.EDIT_BOOKMARK_PRIVATE_CHANNEL);
  };
  var observeCanDeleteBookmarks = exports.observeCanDeleteBookmarks = function observeCanDeleteBookmarks(database, channelId) {
    return observeHasPermissionToBookmarks(database, channelId, _$$_REQUIRE(_dependencyMap[11]).Permissions.DELETE_BOOKMARK_PUBLIC_CHANNEL, _$$_REQUIRE(_dependencyMap[11]).Permissions.DELETE_BOOKMARK_PRIVATE_CHANNEL);
  };
  var getChannelBookmarkById = exports.getChannelBookmarkById = /*#__PURE__*/function () {
    var _ref3 = (0, _asyncToGenerator2.default)(function* (database, bookmarkId) {
      try {
        var bookmark = yield database.get(CHANNEL_BOOKMARK).find(bookmarkId);
        return bookmark;
      } catch (_unused) {
        return undefined;
      }
    });
    return function getChannelBookmarkById(_x, _x2) {
      return _ref3.apply(this, arguments);
    };
  }();
  var queryBookmarks = exports.queryBookmarks = function queryBookmarks(database, channelId) {
    return database.get(CHANNEL_BOOKMARK).query(_$$_REQUIRE(_dependencyMap[13]).Q.and(_$$_REQUIRE(_dependencyMap[13]).Q.where('channel_id', channelId), _$$_REQUIRE(_dependencyMap[13]).Q.where('delete_at', _$$_REQUIRE(_dependencyMap[13]).Q.eq(0))), _$$_REQUIRE(_dependencyMap[13]).Q.sortBy('sort_order', _$$_REQUIRE(_dependencyMap[13]).Q.asc));
  };
  var getBookmarksSince = exports.getBookmarksSince = /*#__PURE__*/function () {
    var _ref4 = (0, _asyncToGenerator2.default)(function* (database, channelId) {
      try {
        var _result$0$mostRecent, _result$;
        var result = yield database.get(CHANNEL_BOOKMARK).query(_$$_REQUIRE(_dependencyMap[13]).Q.unsafeSqlQuery(`SELECT 
                    COALESCE(
                        MAX (
                            MAX(COALESCE(create_at, 0)),
                            MAX(COALESCE(update_at, 0)),
                            MAX(COALESCE(delete_at, 0))
                        ) + 1, 0) as mostRecent
            FROM ChannelBookmark
            WHERE channel_id='${channelId}'`)).unsafeFetchRaw();
        return (_result$0$mostRecent = result == null ? undefined : (_result$ = result[0]) == null ? undefined : _result$.mostRecent) != null ? _result$0$mostRecent : 0;
      } catch (_unused2) {
        return 0;
      }
    });
    return function getBookmarksSince(_x3, _x4) {
      return _ref4.apply(this, arguments);
    };
  }();
  var observeBookmarks = exports.observeBookmarks = function observeBookmarks(database, channelId) {
    return queryBookmarks(database, channelId).observeWithColumns(['file_id']);
  };
