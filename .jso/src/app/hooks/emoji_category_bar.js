  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useEmojiSkinTone = exports.useEmojiCategoryBar = exports.setEmojiSkinTone = exports.setEmojiCategoryBarSection = exports.setEmojiCategoryBarIcons = exports.selectEmojiCategoryBarSection = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _$$_REQUIRE(_dependencyMap[2]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var defaultState = {
    icons: undefined,
    currentIndex: 0,
    selectedIndex: undefined,
    skinTone: 'default'
  };
  var subject = new (_$$_REQUIRE(_dependencyMap[3]).BehaviorSubject)(defaultState);
  var getEmojiCategoryBarState = function getEmojiCategoryBarState() {
    return subject.value;
  };
  var selectEmojiCategoryBarSection = exports.selectEmojiCategoryBarSection = function selectEmojiCategoryBarSection(index) {
    var prevState = getEmojiCategoryBarState();
    subject.next(Object.assign({}, prevState, {
      selectedIndex: index
    }));
  };
  var setEmojiCategoryBarSection = exports.setEmojiCategoryBarSection = function setEmojiCategoryBarSection(index) {
    var prevState = getEmojiCategoryBarState();
    subject.next(Object.assign({}, prevState, {
      currentIndex: index
    }));
  };
  var setEmojiCategoryBarIcons = exports.setEmojiCategoryBarIcons = function setEmojiCategoryBarIcons(icons) {
    console.log('🎛️ setEmojiCategoryBarIcons called with:', icons);
    var prevState = getEmojiCategoryBarState();
    subject.next(Object.assign({}, prevState, {
      icons: icons
    }));
  };
  var setEmojiSkinTone = exports.setEmojiSkinTone = function setEmojiSkinTone(skinTone) {
    var prevState = getEmojiCategoryBarState();
    subject.next(Object.assign({}, prevState, {
      skinTone: skinTone
    }));
  };
  var useEmojiCategoryBar = exports.useEmojiCategoryBar = function useEmojiCategoryBar() {
    var _useState = (0, _react.useState)(defaultState),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      state = _useState2[0],
      setState = _useState2[1];
    (0, _react.useEffect)(function () {
      var sub = subject.subscribe(function (newState) {
        var _newState$icons;
        console.log('🎛️ useEmojiCategoryBar state updated:', {
          iconsCount: ((_newState$icons = newState.icons) == null ? undefined : _newState$icons.length) || 0,
          currentIndex: newState.currentIndex,
          hasIcons: !!newState.icons
        });
        setState(newState);
      });
      return function () {
        sub.unsubscribe();
        // DON'T reset to defaultState on unmount - preserve icons
        // subject.next(defaultState);
      };
    }, []);
    return state;
  };
  var useEmojiSkinTone = exports.useEmojiSkinTone = function useEmojiSkinTone() {
    var _useState3 = (0, _react.useState)(defaultState.skinTone),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      tone = _useState4[0],
      setTone = _useState4[1];
    (0, _react.useEffect)(function () {
      var sub = subject.subscribe(function (state) {
        setTone(state.skinTone);
      });
      return function () {
        sub.unsubscribe();
      };
    }, []);
    return tone;
  };
