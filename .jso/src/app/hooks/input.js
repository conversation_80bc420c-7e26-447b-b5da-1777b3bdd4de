  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useInputPropagation = useInputPropagation;
  var _react = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  function useInputPropagation() {
    var waitForValue = (0, _react.useRef)();
    var waitToPropagate = (0, _react.useCallback)(function (value) {
      waitForValue.current = value;
    }, []);
    var shouldProcessEvent = (0, _react.useCallback)(function (newValue) {
      if (_reactNative.Platform.OS === 'android') {
        return true;
      }
      if (waitForValue.current === undefined) {
        return true;
      }
      if (newValue === waitForValue.current) {
        waitForValue.current = undefined;
      }
      return false;
    }, []);
    return [waitToPropagate, shouldProcessEvent];
  }
