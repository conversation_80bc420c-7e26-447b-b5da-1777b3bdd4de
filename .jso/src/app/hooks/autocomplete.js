  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useAutocompleteDefaultAnimatedValues = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[0]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var useAutocompleteDefaultAnimatedValues = exports.useAutocompleteDefaultAnimatedValues = function useAutocompleteDefaultAnimatedValues(position, availableSpace) {
    var animatedPosition = (0, _$$_REQUIRE(_dependencyMap[1]).useSharedValue)(position);
    var animatedAvailableSpace = (0, _$$_REQUIRE(_dependencyMap[1]).useSharedValue)(availableSpace);
    (0, _react.useEffect)(function () {
      animatedPosition.value = position;
    }, [position]);
    (0, _react.useEffect)(function () {
      animatedAvailableSpace.value = availableSpace;
    }, [availableSpace]);
    return [animatedPosition, animatedAvailableSpace];
  };
