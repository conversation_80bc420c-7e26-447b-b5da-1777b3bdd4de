  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _autocomplete_selector = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _markdown = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _bool_setting = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _text_setting = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var TEXT_DEFAULT_MAX_LENGTH = 150;
  var TEXTAREA_DEFAULT_MAX_LENGTH = 3000;
  var dialogOptionToAppSelectOption = function dialogOptionToAppSelectOption(option) {
    return {
      label: option.text,
      value: option.value
    };
  };
  var appSelectOptionToDialogOption = function appSelectOptionToDialogOption(option) {
    return {
      text: option.label,
      value: option.value
    };
  };
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[9]).makeStyleSheetFromTheme)(function (theme) {
    return {
      markdownFieldContainer: {
        marginTop: 15,
        marginBottom: 10,
        marginLeft: 15
      },
      markdownFieldText: {
        fontSize: 14,
        color: theme.centerChannelColor
      }
    };
  });
  function selectDataSource(fieldType) {
    switch (fieldType) {
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.USER:
        return _$$_REQUIRE(_dependencyMap[11]).View.DATA_SOURCE_USERS;
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.CHANNEL:
        return _$$_REQUIRE(_dependencyMap[11]).View.DATA_SOURCE_CHANNELS;
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.DYNAMIC_SELECT:
        return _$$_REQUIRE(_dependencyMap[11]).View.DATA_SOURCE_DYNAMIC;
      default:
        return '';
    }
  }
  function AppsFormField(_ref) {
    var field = _ref.field,
      name = _ref.name,
      errorText = _ref.errorText,
      value = _ref.value,
      onChange = _ref.onChange,
      performLookup = _ref.performLookup;
    var theme = (0, _$$_REQUIRE(_dependencyMap[12]).useTheme)();
    var style = getStyleSheet(theme);
    var testID = `AppFormElement.${name}`;
    var placeholder = field.hint || '';
    var displayName = field.modal_label || field.label || '';
    var handleChange = (0, _react.useCallback)(function (newValue) {
      onChange(name, newValue);
    }, [name]);
    var handleSelect = (0, _react.useCallback)(function (newValue) {
      if (!newValue) {
        var emptyValue = field.multiselect ? [] : null;
        onChange(name, emptyValue);
        return;
      }
      if (Array.isArray(newValue)) {
        var selectedOptions = newValue.map(dialogOptionToAppSelectOption);
        onChange(name, selectedOptions);
        return;
      }
      onChange(name, dialogOptionToAppSelectOption(newValue));
    }, [onChange, field, name]);
    var getDynamicOptions = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var userInput = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
      var options = yield performLookup(field.name, userInput);
      return options.map(appSelectOptionToDialogOption);
    }), [performLookup, field]);
    var options = (0, _react.useMemo)(function () {
      if (field.type === _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.STATIC_SELECT) {
        var _field$options;
        return (_field$options = field.options) == null ? undefined : _field$options.map(appSelectOptionToDialogOption);
      }
      if (field.type === _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.DYNAMIC_SELECT) {
        if (!value) {
          return undefined;
        }
        if (Array.isArray(value)) {
          return value.map(appSelectOptionToDialogOption);
        }
        var selectedOption = value;
        return [appSelectOptionToDialogOption(selectedOption)];
      }
      return undefined;
    }, [field, value]);
    var selectedValue = (0, _react.useMemo)(function () {
      if (!value || !_$$_REQUIRE(_dependencyMap[10]).SelectableAppFieldTypes.includes(field.type)) {
        return undefined;
      }
      if (!value) {
        return undefined;
      }
      if (Array.isArray(value)) {
        return value.map(function (v) {
          return v.value;
        });
      }
      return value;
    }, [field, value]);
    switch (field.type) {
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.TEXT:
        {
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_text_setting.default, {
            label: displayName,
            maxLength: field.max_length || (field.subtype === 'textarea' ? TEXTAREA_DEFAULT_MAX_LENGTH : TEXT_DEFAULT_MAX_LENGTH),
            value: value,
            placeholder: placeholder,
            helpText: field.description,
            errorText: errorText,
            onChange: handleChange,
            optional: !field.is_required,
            multiline: field.subtype === 'textarea',
            keyboardType: (0, _$$_REQUIRE(_dependencyMap[13]).selectKeyboardType)(field.subtype),
            secureTextEntry: field.subtype === 'password',
            disabled: Boolean(field.readonly),
            testID: testID
          });
        }
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.USER:
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.CHANNEL:
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.STATIC_SELECT:
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.DYNAMIC_SELECT:
        {
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_autocomplete_selector.default, {
            label: displayName,
            dataSource: selectDataSource(field.type),
            options: options,
            optional: !field.is_required,
            onSelected: handleSelect,
            getDynamicOptions: field.type === _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.DYNAMIC_SELECT ? getDynamicOptions : undefined,
            helpText: field.description,
            errorText: errorText,
            placeholder: placeholder,
            showRequiredAsterisk: true,
            selected: selectedValue,
            roundedBorders: false,
            disabled: field.readonly,
            isMultiselect: field.multiselect,
            testID: testID
          });
        }
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.BOOL:
        {
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_bool_setting.default, {
            label: displayName,
            value: value,
            placeholder: placeholder,
            helpText: field.description,
            errorText: errorText,
            optional: !field.is_required,
            onChange: handleChange,
            disabled: field.readonly,
            testID: testID
          });
        }
      case _$$_REQUIRE(_dependencyMap[10]).AppFieldTypes.MARKDOWN:
        {
          if (!field.description) {
            return null;
          }
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: style.markdownFieldContainer,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_markdown.default, {
              value: field.description,
              mentionKeys: [],
              disableAtMentions: true,
              location: "",
              blockStyles: (0, _$$_REQUIRE(_dependencyMap[14]).getMarkdownBlockStyles)(theme),
              textStyles: (0, _$$_REQUIRE(_dependencyMap[14]).getMarkdownTextStyles)(theme),
              baseTextStyle: style.markdownFieldText,
              theme: theme
            })
          });
        }
    }
    return null;
  }
  var _default = exports.default = AppsFormField;
