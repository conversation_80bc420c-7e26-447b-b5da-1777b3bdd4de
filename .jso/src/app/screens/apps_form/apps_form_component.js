  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _markdown = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _did_update = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _dialog_introduction_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _apps_form_field = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[13]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[13]).changeOpacity)(theme.centerChannelColor, 0.03),
        height: '100%'
      },
      errorContainer: {
        marginTop: 15,
        marginLeft: 15,
        fontSize: 14,
        fontWeight: 'bold'
      },
      scrollView: {
        marginBottom: 20,
        marginTop: 10
      },
      errorLabel: {
        fontSize: 12,
        textAlign: 'left',
        color: theme.errorTextColor || '#DA4A4A'
      },
      buttonContainer: {
        paddingTop: 20,
        paddingLeft: 50,
        paddingRight: 50
      }
    };
  });
  function fieldsAsElements(fields) {
    return (fields == null ? undefined : fields.map(function (f) {
      return {
        name: f.name,
        type: f.type,
        subtype: f.subtype,
        optional: !f.is_required
      };
    })) || [];
  }
  var close = function close() {
    _reactNative.Keyboard.dismiss();
    (0, _$$_REQUIRE(_dependencyMap[14]).dismissModal)();
  };
  var makeCloseButton = function makeCloseButton(icon) {
    return (0, _$$_REQUIRE(_dependencyMap[14]).buildNavigationButton)(CLOSE_BUTTON_ID, 'close.more_direct_messages.button', icon);
  };
  var emptyErrorsState = {};
  function valuesReducer(state, action) {
    if (!('name' in action)) {
      return initValues(action.elements);
    }
    if (state[action.name] === action.value) {
      return state;
    }
    return Object.assign({}, state, (0, _defineProperty2.default)({}, action.name, action.value));
  }
  function initValues(fields) {
    var values = {};
    fields == null ? undefined : fields.forEach(function (e) {
      if (e.type === 'bool') {
        values[e.name] = e.value === true || String(e.value).toLowerCase() === 'true';
      } else if (e.value) {
        values[e.name] = e.value;
      }
    });
    return values;
  }
  var CLOSE_BUTTON_ID = 'close-app-form';
  var SUBMIT_BUTTON_ID = 'submit-app-form';
  function AppsFormComponent(_ref) {
    var _submitButtons$option;
    var form = _ref.form,
      componentId = _ref.componentId,
      refreshOnSelect = _ref.refreshOnSelect,
      submit = _ref.submit,
      performLookupCall = _ref.performLookupCall;
    var scrollView = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      submitting = _useState2[0],
      setSubmitting = _useState2[1];
    var intl = (0, _$$_REQUIRE(_dependencyMap[15]).useIntl)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[16]).useServerUrl)();
    var _useState3 = (0, _react.useState)(''),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      error = _useState4[0],
      setError = _useState4[1];
    var _useState5 = (0, _react.useState)(emptyErrorsState),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      errors = _useState6[0],
      setErrors = _useState6[1];
    var _useReducer = (0, _react.useReducer)(valuesReducer, form.fields, initValues),
      _useReducer2 = (0, _slicedToArray2.default)(_useReducer, 2),
      values = _useReducer2[0],
      dispatchValues = _useReducer2[1];
    var theme = (0, _$$_REQUIRE(_dependencyMap[17]).useTheme)();
    var style = getStyleFromTheme(theme);
    (0, _did_update.default)(function () {
      dispatchValues({
        elements: form.fields
      });
    }, [form]);
    var submitButtons = (0, _react.useMemo)(function () {
      return form.fields && form.fields.find(function (f) {
        return f.name === form.submit_buttons;
      });
    }, [form]);
    var rightButton = (0, _react.useMemo)(function () {
      if (submitButtons) {
        return undefined;
      }
      var base = (0, _$$_REQUIRE(_dependencyMap[14]).buildNavigationButton)(SUBMIT_BUTTON_ID, 'interactive_dialog.submit.button', undefined, intl.formatMessage({
        id: 'interactive_dialog.submit',
        defaultMessage: 'Submit'
      }));
      base.enabled = !submitting;
      base.showAsAction = 'always';
      base.color = theme.sidebarHeaderTextColor;
      return base;
    }, [theme.sidebarHeaderTextColor, Boolean(submitButtons), submitting, intl]);
    (0, _react.useEffect)(function () {
      (0, _$$_REQUIRE(_dependencyMap[14]).setButtons)(componentId, {
        rightButtons: rightButton ? [rightButton] : []
      });
    }, [componentId, rightButton]);
    (0, _react.useEffect)(function () {
      var icon = _compass_icon.default.getImageSourceSync('close', 24, theme.sidebarHeaderTextColor);
      (0, _$$_REQUIRE(_dependencyMap[14]).setButtons)(componentId, {
        leftButtons: [makeCloseButton(icon)]
      });
    }, [componentId, theme]);
    var updateErrors = (0, _react.useCallback)(function (elements, fieldErrors, formError) {
      var hasErrors = false;
      var hasHeaderError = false;
      if (formError) {
        hasErrors = true;
        hasHeaderError = true;
        setError(formError);
      } else {
        setError('');
      }
      if (fieldErrors && Object.keys(fieldErrors).length > 0) {
        hasErrors = true;
        if ((0, _$$_REQUIRE(_dependencyMap[18]).checkIfErrorsMatchElements)(fieldErrors, elements)) {
          setErrors(fieldErrors);
        } else if (!hasHeaderError) {
          hasHeaderError = true;
          var field = Object.keys(fieldErrors)[0];
          setError(intl.formatMessage({
            id: 'apps.error.responses.unknown_field_error',
            defaultMessage: 'Received an error for an unknown field. Field name: `{field}`. Error: `{error}`.'
          }, {
            field: field,
            error: fieldErrors[field]
          }));
        }
      }
      if (hasErrors) {
        if (hasHeaderError && scrollView.current) {
          scrollView.current.scrollTo({
            x: 0,
            y: 0
          });
        }
      }
      return hasErrors;
    }, [intl]);
    var onChange = (0, _react.useCallback)(function (name, value) {
      var _form$fields;
      var field = (_form$fields = form.fields) == null ? undefined : _form$fields.find(function (f) {
        return f.name === name;
      });
      if (!field) {
        return;
      }
      var newValues = Object.assign({}, values, (0, _defineProperty2.default)({}, name, value));
      if (field.refresh) {
        refreshOnSelect(field, newValues, value).then(function (res) {
          if (res.error) {
            var _errorResponse$data;
            var errorResponse = res.error;
            var errorMsg = errorResponse.text;
            var newErrors = (_errorResponse$data = errorResponse.data) == null ? undefined : _errorResponse$data.errors;
            var elements = fieldsAsElements(form.fields);
            updateErrors(elements, newErrors, errorMsg);
            return;
          }
          var callResponse = res.data;
          switch (callResponse.type) {
            case _$$_REQUIRE(_dependencyMap[19]).AppCallResponseTypes.FORM:
              return;
            case _$$_REQUIRE(_dependencyMap[19]).AppCallResponseTypes.OK:
            case _$$_REQUIRE(_dependencyMap[19]).AppCallResponseTypes.NAVIGATE:
              updateErrors([], undefined, intl.formatMessage({
                id: 'apps.error.responses.unexpected_type',
                defaultMessage: 'App response type was not expected. Response type: {type}.'
              }, {
                type: callResponse.type
              }));
              return;
            default:
              updateErrors([], undefined, intl.formatMessage({
                id: 'apps.error.responses.unknown_type',
                defaultMessage: 'App response type not supported. Response type: {type}.'
              }, {
                type: callResponse.type
              }));
          }
        });
      }
      dispatchValues({
        name: name,
        value: value
      });
    }, [form, values, refreshOnSelect, updateErrors, intl]);
    var handleSubmit = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (button) {
        if (submitting) {
          return;
        }
        var fields = form.fields;
        var fieldErrors = {};
        var elements = fieldsAsElements(fields);
        var hasErrors = false;
        elements == null ? undefined : elements.forEach(function (element) {
          var newError = (0, _$$_REQUIRE(_dependencyMap[18]).checkDialogElementForError)(element, element.name === form.submit_buttons ? button : values[element.name]);
          if (newError) {
            hasErrors = true;
            fieldErrors[element.name] = intl.formatMessage({
              id: newError.id,
              defaultMessage: newError.defaultMessage
            }, newError.values);
          }
        });
        if (hasErrors) {
          setErrors(fieldErrors);
          return;
        }
        var submission = Object.assign({}, values);
        if (button && form.submit_buttons) {
          submission[form.submit_buttons] = button;
        }
        setSubmitting(true);
        var res = yield submit(submission);
        if (res.error) {
          var _errorResponse$data2;
          var errorResponse = res.error;
          var errorMessage = errorResponse.text;
          hasErrors = updateErrors(elements, (_errorResponse$data2 = errorResponse.data) == null ? undefined : _errorResponse$data2.errors, errorMessage);
          if (!hasErrors) {
            close();
            return;
          }
          setSubmitting(false);
          return;
        }
        setError('');
        setErrors(emptyErrorsState);
        var callResponse = res.data;
        switch (callResponse.type) {
          case _$$_REQUIRE(_dependencyMap[19]).AppCallResponseTypes.OK:
            close();
            return;
          case _$$_REQUIRE(_dependencyMap[19]).AppCallResponseTypes.NAVIGATE:
            close();
            (0, _$$_REQUIRE(_dependencyMap[20]).handleGotoLocation)(serverUrl, intl, callResponse.navigate_to_url);
            return;
          case _$$_REQUIRE(_dependencyMap[19]).AppCallResponseTypes.FORM:
            setSubmitting(false);
            return;
          default:
            updateErrors([], undefined, intl.formatMessage({
              id: 'apps.error.responses.unknown_type',
              defaultMessage: 'App response type not supported. Response type: {type}.'
            }, {
              type: callResponse.type
            }));
            setSubmitting(false);
        }
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }(), [form, values, submit, submitting, updateErrors, serverUrl, intl]);
    var performLookup = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (name, userInput) {
        var _form$fields2;
        var field = (_form$fields2 = form.fields) == null ? undefined : _form$fields2.find(function (f) {
          return f.name === name;
        });
        if (!field) {
          return [];
        }
        var res = yield performLookupCall(field, values, userInput);
        if (res.error) {
          var errorResponse = res.error;
          var errMsg = errorResponse.text || intl.formatMessage({
            id: 'apps.error.unknown',
            defaultMessage: 'Unknown error.'
          });
          setErrors((0, _defineProperty2.default)({}, field.name, errMsg));
          return [];
        }
        var callResp = res.data;
        switch (callResp.type) {
          case _$$_REQUIRE(_dependencyMap[19]).AppCallResponseTypes.OK:
            {
              var _callResp$data;
              var items = ((_callResp$data = callResp.data) == null ? undefined : _callResp$data.items) || [];
              items = items.filter(_$$_REQUIRE(_dependencyMap[21]).filterEmptyOptions);
              return items;
            }
          case _$$_REQUIRE(_dependencyMap[19]).AppCallResponseTypes.FORM:
          case _$$_REQUIRE(_dependencyMap[19]).AppCallResponseTypes.NAVIGATE:
            {
              var _errMsg = intl.formatMessage({
                id: 'apps.error.responses.unexpected_type',
                defaultMessage: 'App response type was not expected. Response type: {type}.'
              }, {
                type: callResp.type
              });
              setErrors((0, _defineProperty2.default)({}, field.name, _errMsg));
              return [];
            }
          default:
            {
              var _errMsg2 = intl.formatMessage({
                id: 'apps.error.responses.unknown_type',
                defaultMessage: 'App response type not supported. Response type: {type}.'
              }, {
                type: callResp.type
              });
              setErrors((0, _defineProperty2.default)({}, field.name, _errMsg2));
              return [];
            }
        }
      });
      return function (_x2, _x3) {
        return _ref3.apply(this, arguments);
      };
    }(), [form, values, performLookupCall, intl]);
    (0, _navigation_button_pressed.default)(CLOSE_BUTTON_ID, componentId, close, [close]);
    (0, _navigation_button_pressed.default)(SUBMIT_BUTTON_ID, componentId, handleSubmit, [handleSubmit]);
    var submitButtonStyle = (0, _react.useMemo)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[22]).buttonBackgroundStyle)(theme, 'lg', 'primary', 'default');
    }, [theme]);
    var submitButtonTextStyle = (0, _react.useMemo)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[22]).buttonTextStyle)(theme, 'lg', 'primary', 'default');
    }, [theme]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[23]).SafeAreaView, {
      testID: "interactive_dialog.screen",
      style: style.container,
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
        ref: scrollView,
        style: style.scrollView,
        children: [error && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.errorContainer,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_markdown.default, {
            baseTextStyle: style.errorLabel,
            textStyles: (0, _$$_REQUIRE(_dependencyMap[24]).getMarkdownTextStyles)(theme),
            blockStyles: (0, _$$_REQUIRE(_dependencyMap[24]).getMarkdownBlockStyles)(theme),
            location: "",
            disableAtMentions: true,
            value: error,
            theme: theme
          })
        }), form.header && /*#__PURE__*/(0, _jsxRuntime.jsx)(_dialog_introduction_text.default, {
          value: form.header
        }), form.fields && form.fields.filter(function (f) {
          return f.name !== form.submit_buttons;
        }).map(function (field) {
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_apps_form_field.default, {
            field: field,
            name: field.name,
            errorText: errors[field.name],
            value: values[field.name],
            performLookup: performLookup,
            onChange: onChange
          }, field.name);
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: {
            marginHorizontal: 5
          },
          children: submitButtons == null ? undefined : (_submitButtons$option = submitButtons.options) == null ? undefined : _submitButtons$option.map(function (o) {
            return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: style.buttonContainer,
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[25]).Button, {
                onPress: function onPress() {
                  return handleSubmit(o.value);
                },
                buttonStyle: submitButtonStyle,
                children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                  style: submitButtonTextStyle,
                  children: o.label
                })
              })
            }, o.value);
          })
        })]
      })
    });
  }
  var _default = exports.default = AppsFormComponent;
