  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _apps_form_component = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  function AppsFormContainer(_ref) {
    var form = _ref.form,
      context = _ref.context,
      componentId = _ref.componentId;
    var intl = (0, _$$_REQUIRE(_dependencyMap[8]).useIntl)();
    var _useState = (0, _react.useState)(form),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      currentForm = _useState2[0],
      setCurrentForm = _useState2[1];
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[9]).useServerUrl)();
    var submit = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (submission) {
        var makeErrorMsg = function makeErrorMsg(msg) {
          return intl.formatMessage({
            id: 'apps.error.form.submit.pretext',
            defaultMessage: 'There has been an error submitting the modal. Contact the app developer. Details: {details}'
          }, {
            details: msg
          });
        };
        if (!currentForm) {
          return {
            error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)(makeErrorMsg(intl.formatMessage({
              id: 'apps.error.form.no_form',
              defaultMessage: '`form` is not defined'
            })))
          };
        }
        if (!currentForm.submit) {
          return {
            error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)(makeErrorMsg(intl.formatMessage({
              id: 'apps.error.form.no_submit',
              defaultMessage: '`submit` is not defined'
            })))
          };
        }
        if (!context) {
          return {
            error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)('unreachable: empty context')
          };
        }
        var creq = (0, _$$_REQUIRE(_dependencyMap[10]).createCallRequest)(currentForm.submit, context, {}, submission);
        var res = yield (0, _$$_REQUIRE(_dependencyMap[11]).doAppSubmit)(serverUrl, creq, intl);
        if ('error' in res) {
          return res;
        }
        var callResp = res.data;
        switch (callResp.type) {
          case _$$_REQUIRE(_dependencyMap[12]).AppCallResponseTypes.OK:
            if (callResp.text) {
              (0, _$$_REQUIRE(_dependencyMap[11]).postEphemeralCallResponseForContext)(serverUrl, callResp, callResp.text, creq.context);
            }
            break;
          case _$$_REQUIRE(_dependencyMap[12]).AppCallResponseTypes.FORM:
            setCurrentForm(callResp.form);
            break;
          case _$$_REQUIRE(_dependencyMap[12]).AppCallResponseTypes.NAVIGATE:
            if (callResp.navigate_to_url) {
              (0, _$$_REQUIRE(_dependencyMap[13]).handleGotoLocation)(serverUrl, intl, callResp.navigate_to_url);
            }
            break;
          default:
            return {
              error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)(makeErrorMsg(intl.formatMessage({
                id: 'apps.error.responses.unknown_type',
                defaultMessage: 'App response type not supported. Response type: {type}.'
              }, {
                type: callResp.type
              })))
            };
        }
        return res;
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }(), [currentForm, setCurrentForm, context, serverUrl, intl]);
    var refreshOnSelect = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (field, values) {
        var makeErrorMsg = function makeErrorMsg(message) {
          return intl.formatMessage({
            id: 'apps.error.form.refresh',
            defaultMessage: 'There has been an error updating the modal. Contact the app developer. Details: {details}'
          }, {
            details: message
          });
        };
        if (!currentForm) {
          return {
            error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)(makeErrorMsg(intl.formatMessage({
              id: 'apps.error.form.no_form',
              defaultMessage: '`form` is not defined.'
            })))
          };
        }
        if (!currentForm.source) {
          return {
            error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)(makeErrorMsg(intl.formatMessage({
              id: 'apps.error.form.no_source',
              defaultMessage: '`source` is not defined.'
            })))
          };
        }
        if (!field.refresh) {
          // Should never happen
          return {
            error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)(makeErrorMsg(intl.formatMessage({
              id: 'apps.error.form.refresh_no_refresh',
              defaultMessage: 'Called refresh on no refresh field.'
            })))
          };
        }
        if (!context) {
          return {
            error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)('unreachable: empty context')
          };
        }
        var creq = (0, _$$_REQUIRE(_dependencyMap[10]).createCallRequest)(currentForm.source, context, {}, values);
        creq.selected_field = field.name;
        var res = yield (0, _$$_REQUIRE(_dependencyMap[11]).doAppFetchForm)(serverUrl, creq, intl);
        if (res.error) {
          return res;
        }
        var callResp = res.data;
        switch (callResp.type) {
          case _$$_REQUIRE(_dependencyMap[12]).AppCallResponseTypes.FORM:
            setCurrentForm(callResp.form);
            break;
          case _$$_REQUIRE(_dependencyMap[12]).AppCallResponseTypes.OK:
          case _$$_REQUIRE(_dependencyMap[12]).AppCallResponseTypes.NAVIGATE:
            return {
              error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)(makeErrorMsg(intl.formatMessage({
                id: 'apps.error.responses.unexpected_type',
                defaultMessage: 'App response type was not expected. Response type: {type}.'
              }, {
                type: callResp.type
              })))
            };
          default:
            return {
              error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)(makeErrorMsg(intl.formatMessage({
                id: 'apps.error.responses.unknown_type',
                defaultMessage: 'App response type not supported. Response type: {type}.'
              }, {
                type: callResp.type
              })))
            };
        }
        return res;
      });
      return function (_x2, _x3) {
        return _ref3.apply(this, arguments);
      };
    }(), [currentForm, setCurrentForm, context, serverUrl, intl]);
    var performLookupCall = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (field, values, userInput) {
        var makeErrorMsg = function makeErrorMsg(message) {
          return intl.formatMessage({
            id: 'apps.error.form.refresh',
            defaultMessage: 'There has been an error fetching the select fields. Contact the app developer. Details: {details}'
          }, {
            details: message
          });
        };
        if (!field.lookup) {
          return {
            error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)(makeErrorMsg(intl.formatMessage({
              id: 'apps.error.form.no_lookup',
              defaultMessage: '`lookup` is not defined.'
            })))
          };
        }
        if (!context) {
          return {
            error: (0, _$$_REQUIRE(_dependencyMap[10]).makeCallErrorResponse)('unreachable: empty context')
          };
        }
        var creq = (0, _$$_REQUIRE(_dependencyMap[10]).createCallRequest)(field.lookup, context, {}, values);
        creq.selected_field = field.name;
        creq.query = userInput;
        return (0, _$$_REQUIRE(_dependencyMap[11]).doAppLookup)(serverUrl, creq, intl);
      });
      return function (_x4, _x5, _x6) {
        return _ref4.apply(this, arguments);
      };
    }(), [context, serverUrl, intl]);
    var close = (0, _react.useCallback)(function () {
      _reactNative.Keyboard.dismiss();
      (0, _$$_REQUIRE(_dependencyMap[14]).dismissModal)({
        componentId: componentId
      });
    }, [componentId]);
    (0, _android_back_handler.default)(componentId, close);
    if (!(currentForm != null && currentForm.submit) || !context) {
      return null;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_apps_form_component.default, {
      form: currentForm,
      componentId: componentId,
      performLookupCall: performLookupCall,
      refreshOnSelect: refreshOnSelect,
      submit: submit
    });
  }
  var _default = exports.default = AppsFormContainer;
