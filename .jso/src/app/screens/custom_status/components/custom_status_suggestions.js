  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _custom_status_suggestion = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      separator: {
        marginTop: 32
      },
      title: Object.assign({
        fontSize: 17,
        marginBottom: 12,
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.5),
        marginLeft: 16,
        textTransform: 'uppercase'
      }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Body', 200, 'Light')),
      block: {
        borderBottomColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.1),
        borderBottomWidth: 1,
        borderTopColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.1),
        borderTopWidth: 1
      }
    };
  });
  var defaultCustomStatusSuggestions = [{
    emoji: 'calendar',
    message: (0, _$$_REQUIRE(_dependencyMap[8]).t)('custom_status.suggestions.in_a_meeting'),
    messageDefault: 'In a meeting',
    durationDefault: 'one_hour'
  }, {
    emoji: 'hamburger',
    message: (0, _$$_REQUIRE(_dependencyMap[8]).t)('custom_status.suggestions.out_for_lunch'),
    messageDefault: 'Out for lunch',
    durationDefault: 'thirty_minutes'
  }, {
    emoji: 'sneezing_face',
    message: (0, _$$_REQUIRE(_dependencyMap[8]).t)('custom_status.suggestions.out_sick'),
    messageDefault: 'Out sick',
    durationDefault: 'today'
  }, {
    emoji: 'house',
    message: (0, _$$_REQUIRE(_dependencyMap[8]).t)('custom_status.suggestions.working_from_home'),
    messageDefault: 'Working from home',
    durationDefault: 'today'
  }, {
    emoji: 'palm_tree',
    message: (0, _$$_REQUIRE(_dependencyMap[8]).t)('custom_status.suggestions.on_a_vacation'),
    messageDefault: 'On a vacation',
    durationDefault: 'this_week'
  }];
  var CustomStatusSuggestions = function CustomStatusSuggestions(_ref) {
    var intl = _ref.intl,
      onHandleCustomStatusSuggestionClick = _ref.onHandleCustomStatusSuggestionClick,
      recentCustomStatuses = _ref.recentCustomStatuses,
      theme = _ref.theme;
    var style = getStyleSheet(theme);
    var recentCustomStatusTexts = new Set(recentCustomStatuses.map(function (status) {
      return status.text;
    }));
    var customStatusSuggestions = defaultCustomStatusSuggestions.map(function (status) {
      return {
        emoji: status.emoji,
        text: intl.formatMessage({
          id: status.message,
          defaultMessage: status.messageDefault
        }),
        duration: status.durationDefault
      };
    }).filter(function (status) {
      return !recentCustomStatusTexts.has(status.text);
    }).map(function (status, index, arr) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_suggestion.default, {
        handleSuggestionClick: onHandleCustomStatusSuggestionClick,
        emoji: status.emoji,
        text: status.text,
        theme: theme,
        separator: index !== arr.length - 1,
        duration: status.duration
      }, status.text);
    });
    if (customStatusSuggestions.length === 0) {
      return null;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.separator
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        testID: "custom_status.suggestions",
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: (0, _$$_REQUIRE(_dependencyMap[8]).t)('custom_status.suggestions.title'),
          defaultMessage: "Suggestions",
          style: style.title
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.block,
          children: customStatusSuggestions
        })]
      })]
    });
  };
  var _default = exports.default = CustomStatusSuggestions;
