  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _custom_status_suggestion = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      separator: {
        marginTop: 32
      },
      title: {
        fontSize: 17,
        marginBottom: 12,
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.5),
        marginLeft: 16,
        textTransform: 'uppercase'
      },
      block: {
        borderBottomColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.1),
        borderBottomWidth: 1,
        borderTopColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.1),
        borderTopWidth: 1
      }
    };
  });
  var RecentCustomStatuses = function RecentCustomStatuses(_ref) {
    var onHandleClear = _ref.onHandleClear,
      onHandleSuggestionClick = _ref.onHandleSuggestionClick,
      recentCustomStatuses = _ref.recentCustomStatuses,
      theme = _ref.theme;
    var style = getStyleSheet(theme);
    if (recentCustomStatuses.length === 0) {
      return null;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.separator
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        testID: "custom_status.recents",
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: (0, _$$_REQUIRE(_dependencyMap[7]).t)('custom_status.suggestions.recent_title'),
          defaultMessage: "Recent",
          style: style.title
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.block,
          children: recentCustomStatuses.map(function (status, index) {
            return /*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_suggestion.default, {
              handleSuggestionClick: onHandleSuggestionClick,
              handleClear: onHandleClear,
              emoji: status == null ? undefined : status.emoji,
              text: status == null ? undefined : status.text,
              theme: theme,
              separator: index !== recentCustomStatuses.length - 1,
              duration: status.duration,
              expires_at: status.expires_at
            }, `${status.text}-${index.toString()}`);
          })
        })]
      })]
    });
  };
  var _default = exports.default = RecentCustomStatuses;
