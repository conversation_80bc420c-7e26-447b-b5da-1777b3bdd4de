  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _emoji = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      iconContainer: {
        marginLeft: 14
      },
      icon: {
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.64)
      },
      emoji: {
        color: theme.centerChannelColor
      }
    };
  });
  var CustomStatusEmoji = function CustomStatusEmoji(_ref) {
    var emoji = _ref.emoji,
      isStatusSet = _ref.isStatusSet,
      onPress = _ref.onPress,
      theme = _ref.theme;
    var style = getStyleSheet(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      testID: `custom_status.custom_status_emoji.${isStatusSet ? emoji || 'speech_balloon' : 'default'}`,
      onPress: onPress,
      style: style.iconContainer,
      children: isStatusSet ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_emoji.default, {
        emojiName: emoji || 'speech_balloon',
        size: 20
      }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        name: "emoticon-happy-outline",
        size: 24,
        style: style.icon
      })
    });
  };
  var _default = exports.default = CustomStatusEmoji;
