  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _custom_status_expiry = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      rightIcon: {
        position: 'absolute',
        right: 18,
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.5)
      },
      expiryTimeLabel: Object.assign({
        fontSize: 17,
        paddingLeft: 16,
        marginStart: 15,
        textAlignVertical: 'center',
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)("Heading", 100, "Light")),
      inputContainer: {
        justifyContent: 'center',
        height: 48,
        backgroundColor: theme.centerChannelBg
      },
      expiryTime: Object.assign({
        position: 'absolute',
        right: 42,
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.5)
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)("Heading", 100, "Light")),
      customStatusExpiry: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.5)
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)("Heading", 100, "Light"))
    };
  });
  var ClearAfter = function ClearAfter(_ref) {
    var duration = _ref.duration,
      expiresAt = _ref.expiresAt,
      onOpenClearAfterModal = _ref.onOpenClearAfterModal,
      theme = _ref.theme;
    var intl = (0, _$$_REQUIRE(_dependencyMap[9]).useIntl)();
    var style = getStyleSheet(theme);
    var renderClearAfterTime = function renderClearAfterTime() {
      if (duration && duration === 'date_and_time') {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.expiryTime,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_expiry.default, {
            textStyles: style.customStatusExpiry,
            theme: theme,
            time: expiresAt.toDate(),
            testID: `custom_status.clear_after.custom_status_duration.${duration}.custom_status_expiry`
          })
        });
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        id: _$$_REQUIRE(_dependencyMap[10]).CST[duration].id,
        defaultMessage: _$$_REQUIRE(_dependencyMap[10]).CST[duration].defaultMessage,
        style: style.expiryTime,
        testID: `custom_status.clear_after.custom_status_duration.${duration}.custom_status_expiry`
      });
    };
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: onOpenClearAfterModal,
      testID: `custom_status.clear_after.custom_status_duration.${duration}.action`,
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: style.inputContainer,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: style.expiryTimeLabel,
          children: intl.formatMessage({
            id: 'mobile.custom_status.clear_after',
            defaultMessage: 'Clear After'
          })
        }), renderClearAfterTime(), /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          name: "chevron-right",
          size: 24,
          style: style.rightIcon
        })]
      })
    });
  };
  var _default = exports.default = ClearAfter;
