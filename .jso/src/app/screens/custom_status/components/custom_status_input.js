  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _clear_button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _custom_status_emoji = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      divider: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.2),
        height: 1,
        marginRight: 16,
        marginLeft: 52
      },
      clearButton: {
        position: 'absolute',
        top: 3,
        right: 14
      },
      input: Object.assign({
        alignSelf: 'stretch',
        color: theme.centerChannelColor,
        flex: 1,
        fontSize: 17,
        paddingHorizontal: 16,
        textAlignVertical: 'center',
        height: '100%'
      }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)("Heading", 100, "Regular")),
      inputContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        height: 48,
        backgroundColor: theme.centerChannelBg,
        flexDirection: 'row'
      }
    };
  });
  var CustomStatusInput = function CustomStatusInput(_ref) {
    var emoji = _ref.emoji,
      isStatusSet = _ref.isStatusSet,
      onChangeText = _ref.onChangeText,
      onClearHandle = _ref.onClearHandle,
      onOpenEmojiPicker = _ref.onOpenEmojiPicker,
      text = _ref.text,
      theme = _ref.theme;
    var style = getStyleSheet(theme);
    var intl = (0, _$$_REQUIRE(_dependencyMap[8]).useIntl)();
    var placeholder = intl.formatMessage({
      id: 'custom_status.set_status',
      defaultMessage: 'Set a custom status'
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: style.inputContainer,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_emoji.default, {
          emoji: emoji,
          isStatusSet: isStatusSet,
          onPress: onOpenEmojiPicker,
          theme: theme
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TextInput, {
          testID: "custom_status.status.input",
          autoCapitalize: "none",
          autoCorrect: false,
          blurOnSubmit: false,
          disableFullscreenUI: true,
          keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[6]).getKeyboardAppearanceFromTheme)(theme),
          keyboardType: "default",
          maxLength: _$$_REQUIRE(_dependencyMap[9]).CUSTOM_STATUS_TEXT_CHARACTER_LIMIT,
          onChangeText: onChangeText,
          placeholder: placeholder,
          placeholderTextColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.5),
          returnKeyType: "go",
          style: style.input,
          secureTextEntry: false,
          underlineColorAndroid: "transparent",
          selectionColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.buttonBg, 0.35),
          selectionHandleColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.buttonBg, 0.6),
          cursorColor: theme.buttonBg,
          value: text
        }), isStatusSet ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.clearButton,
          testID: "custom_status.status.input.clear.button",
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_clear_button.default, {
            handlePress: onClearHandle,
            theme: theme
          })
        }) : null]
      }), isStatusSet && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.divider
      })]
    });
  };
  var _default = exports.default = CustomStatusInput;
