  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _clear_button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _custom_status_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _emoji = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        backgroundColor: theme.centerChannelBg,
        flexDirection: 'row',
        minHeight: 50,
        alignItems: 'center'
      },
      iconContainer: {
        marginLeft: 14,
        marginRight: 10
      },
      wrapper: {
        flex: 1
      },
      textContainer: Object.assign({
        paddingTop: 14,
        paddingBottom: 14,
        justifyContent: 'center',
        width: '70%',
        flex: 1
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)("Body", 100, "Light")),
      divider: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.2),
        height: 1,
        marginRight: 16
      },
      customStatusDuration: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.6),
        fontSize: 15
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)("Body", 100, "Light")),
      customStatusText: {
        color: theme.centerChannelColor
      }
    };
  });
  var CustomStatusSuggestion = function CustomStatusSuggestion(_ref) {
    var duration = _ref.duration,
      emoji = _ref.emoji,
      expires_at = _ref.expires_at,
      handleClear = _ref.handleClear,
      handleSuggestionClick = _ref.handleSuggestionClick,
      isExpirySupported = _ref.isExpirySupported,
      separator = _ref.separator,
      text = _ref.text,
      theme = _ref.theme;
    var style = getStyleSheet(theme);
    var intl = (0, _$$_REQUIRE(_dependencyMap[9]).useIntl)();
    var handleClick = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[10]).preventDoubleTap)(function () {
      handleSuggestionClick({
        emoji: emoji,
        text: text,
        duration: duration
      });
    }), []);
    var handleSuggestionClear = (0, _react.useCallback)(function () {
      if (handleClear) {
        handleClear({
          emoji: emoji,
          text: text,
          duration: duration,
          expires_at: expires_at
        });
      }
    }, []);
    var showCustomStatus = Boolean(duration && duration !== 'date_and_time' && isExpirySupported);
    var customStatusSuggestionTestId = `custom_status.custom_status_suggestion.${text}`;
    var clearButton = handleClear && expires_at ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_clear_button.default, {
        handlePress: handleSuggestionClear,
        theme: theme,
        iconName: "close",
        size: 18,
        testID: `${customStatusSuggestionTestId}.clear.button`
      })
    }) : null;
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: handleClick,
      testID: customStatusSuggestionTestId,
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: style.container,
        children: [emoji && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.iconContainer,
          testID: `${customStatusSuggestionTestId}.custom_status_emoji.${emoji}`,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_emoji.default, {
            emojiName: emoji,
            size: 20
          })
        }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: style.wrapper,
          children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: style.textContainer,
            children: [Boolean(text) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_text.default, {
                text: text,
                theme: theme,
                textStyle: style.customStatusText,
                testID: `${customStatusSuggestionTestId}.custom_status_text`
              })
            }), showCustomStatus && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: {
                paddingTop: 5
              },
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_text.default, {
                text: intl.formatMessage(_$$_REQUIRE(_dependencyMap[11]).CST[duration]),
                theme: theme,
                textStyle: style.customStatusDuration,
                testID: `${customStatusSuggestionTestId}.custom_status_duration.${duration}`
              })
            })]
          }), separator && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: style.divider
          })]
        }), clearButton]
      })
    });
  };
  var _default = exports.default = CustomStatusSuggestion;
