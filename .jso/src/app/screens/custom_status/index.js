  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _custom_status = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhancedCSM = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)([], function (_ref) {
    var database = _ref.database;
    return {
      currentUser: (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentUser)(database),
      recentCustomStatuses: (0, _$$_REQUIRE(_dependencyMap[4]).observeRecentCustomStatus)(database),
      customStatusExpirySupported: (0, _$$_REQUIRE(_dependencyMap[4]).observeIsCustomStatusExpirySupported)(database)
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhancedCSM(_custom_status.default));
