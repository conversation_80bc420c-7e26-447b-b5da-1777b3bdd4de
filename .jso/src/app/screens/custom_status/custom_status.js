  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _tablet_title = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _clear_after = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _custom_status_input = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _custom_status_suggestions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _recent_custom_statuses = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[14]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.centerChannelColor, 0.03)
      },
      contentContainerStyle: {
        height: '99%'
      },
      scrollView: {
        flex: 1,
        paddingTop: 32
      },
      separator: {
        marginTop: 32
      },
      block: {
        borderBottomColor: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.centerChannelColor, 0.1),
        borderBottomWidth: 1,
        borderTopColor: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.centerChannelColor, 0.1),
        borderTopWidth: 1
      }
    };
  });
  var DEFAULT_DURATION = 'today';
  var BTN_UPDATE_STATUS = 'update-custom-status';
  var edges = ['bottom', 'left', 'right'];
  var calculateExpiryTime = function calculateExpiryTime(duration, currentUser, expiresAt) {
    var userTimezone = (0, _$$_REQUIRE(_dependencyMap[15]).getTimezone)(currentUser == null ? undefined : currentUser.timezone);
    var currentTime = (0, _$$_REQUIRE(_dependencyMap[16]).getCurrentMomentForTimezone)(userTimezone);
    switch (duration) {
      case 'thirty_minutes':
        return currentTime.add(30, 'minutes').seconds(0).milliseconds(0).toISOString();
      case 'one_hour':
        return currentTime.add(1, 'hour').seconds(0).milliseconds(0).toISOString();
      case 'four_hours':
        return currentTime.add(4, 'hours').seconds(0).milliseconds(0).toISOString();
      case 'today':
        return currentTime.endOf('day').toISOString();
      case 'this_week':
        return currentTime.endOf('week').toISOString();
      case 'date_and_time':
        return expiresAt.toISOString();
      case _$$_REQUIRE(_dependencyMap[17]).CustomStatusDurationEnum.DONT_CLEAR:
      default:
        return '';
    }
  };
  function reducer(state, action) {
    switch (action.type) {
      case 'clear':
        return {
          emoji: '',
          text: '',
          duration: DEFAULT_DURATION,
          expiresAt: state.expiresAt
        };
      case 'fromUserCustomStatus':
        {
          var status = action.status;
          if (status) {
            return {
              emoji: status.emoji,
              text: status.text,
              duration: status.duration,
              expiresAt: (0, _momentTimezone.default)(status.expires_at)
            };
          }
          return state;
        }
      case 'fromUserCustomStatusIgnoringExpire':
        {
          var _status = action.status;
          if (_status) {
            return {
              emoji: _status.emoji,
              text: _status.text,
              duration: _status.duration,
              expiresAt: state.expiresAt
            };
          }
          return state;
        }
      case 'text':
        return Object.assign({}, state, {
          text: action.value
        });
      case 'emoji':
        return Object.assign({}, state, {
          emoji: action.value
        });
      case 'duration':
        if (action.duration != null) {
          return Object.assign({}, state, {
            duration: action.duration,
            expiresAt: action.duration === 'date_and_time' && action.expiresAt ? (0, _momentTimezone.default)(action.expiresAt) : state.expiresAt
          });
        }
        return state;
      default:
        return state;
    }
  }
  var CustomStatus = function CustomStatus(_ref) {
    var customStatusExpirySupported = _ref.customStatusExpirySupported,
      currentUser = _ref.currentUser,
      recentCustomStatuses = _ref.recentCustomStatuses,
      componentId = _ref.componentId;
    var intl = (0, _$$_REQUIRE(_dependencyMap[18]).useIntl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[19]).useIsTablet)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[20]).useTheme)();
    var style = getStyleSheet(theme);
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[21]).useServerUrl)();
    var storedStatus = (0, _react.useMemo)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[15]).getUserCustomStatus)(currentUser);
    }, [currentUser]);
    var initialStatus = (0, _react.useMemo)(function () {
      var _storedStatus$duratio;
      var userTimezone = (0, _$$_REQUIRE(_dependencyMap[15]).getTimezone)(currentUser == null ? undefined : currentUser.timezone);

      // May be a ref
      var isCustomStatusExpired = (0, _$$_REQUIRE(_dependencyMap[15]).isCustomStatusExpired)(currentUser);
      var currentTime = (0, _$$_REQUIRE(_dependencyMap[16]).getCurrentMomentForTimezone)(userTimezone != null ? userTimezone : '');
      var initialCustomExpiryTime = (0, _$$_REQUIRE(_dependencyMap[16]).getRoundedTime)(currentTime);
      var isCurrentCustomStatusSet = !isCustomStatusExpired && ((storedStatus == null ? undefined : storedStatus.text) || (storedStatus == null ? undefined : storedStatus.emoji));
      if (isCurrentCustomStatusSet && (storedStatus == null ? undefined : storedStatus.duration) === 'date_and_time' && storedStatus != null && storedStatus.expires_at) {
        initialCustomExpiryTime = (0, _momentTimezone.default)(storedStatus == null ? undefined : storedStatus.expires_at);
      }
      return {
        duration: isCurrentCustomStatusSet ? (_storedStatus$duratio = storedStatus == null ? undefined : storedStatus.duration) != null ? _storedStatus$duratio : _$$_REQUIRE(_dependencyMap[17]).CustomStatusDurationEnum.DONT_CLEAR : DEFAULT_DURATION,
        emoji: isCurrentCustomStatusSet ? storedStatus == null ? undefined : storedStatus.emoji : '',
        expiresAt: initialCustomExpiryTime,
        text: isCurrentCustomStatusSet ? storedStatus == null ? undefined : storedStatus.text : ''
      };
    }, []);
    var _useReducer = (0, _react.useReducer)(reducer, initialStatus),
      _useReducer2 = (0, _slicedToArray2.default)(_useReducer, 2),
      newStatus = _useReducer2[0],
      dispatchStatus = _useReducer2[1];
    var isStatusSet = Boolean(newStatus.emoji || newStatus.text);
    var option = {
      topBar: {
        visible: true
      },
      layout: {
        componentBackgroundColor: theme.centerChannelBg
      },
      statusBar: {
        visible: true,
        backgroundColor: theme.sidebarBg
      }
    };
    var handleClear = (0, _react.useCallback)(function () {
      dispatchStatus({
        type: 'clear'
      });
    }, []);
    var handleTextChange = (0, _react.useCallback)(function (value) {
      dispatchStatus({
        type: 'text',
        value: value
      });
    }, []);
    var handleEmojiClick = (0, _react.useCallback)(function (value) {
      dispatchStatus({
        type: 'emoji',
        value: value
      });
    }, []);
    var handleClearAfterClick = (0, _react.useCallback)(function (duration, expiresAt) {
      dispatchStatus({
        type: 'duration',
        duration: duration,
        expiresAt: expiresAt
      });
    }, []);
    var handleRecentCustomStatusClear = (0, _react.useCallback)(function (status) {
      return (0, _$$_REQUIRE(_dependencyMap[22]).removeRecentCustomStatus)(serverUrl, status);
    }, [serverUrl]);
    var handleCustomStatusSuggestionClick = (0, _react.useCallback)(function (status) {
      if (!status.duration) {
        // This should never happen, but we add a safeguard here
        (0, _$$_REQUIRE(_dependencyMap[23]).logDebug)('clicked on a custom status with no duration');
        return;
      }
      dispatchStatus({
        type: 'fromUserCustomStatusIgnoringExpire',
        status: status
      });
    }, []);
    var openClearAfterModal = (0, _react.useCallback)(function () {
      var screen = _$$_REQUIRE(_dependencyMap[24]).Screens.CUSTOM_STATUS_CLEAR_AFTER;
      var title = intl.formatMessage({
        id: 'mobile.custom_status.clear_after.title',
        defaultMessage: 'Clear Custom Status After'
      });
      var passProps = {
        handleClearAfterClick: handleClearAfterClick,
        initialDuration: newStatus.duration,
        intl: intl,
        theme: theme
      };
      if (isTablet) {
        (0, _$$_REQUIRE(_dependencyMap[25]).showModal)(screen, title, passProps, option);
      } else {
        (0, _$$_REQUIRE(_dependencyMap[25]).goToScreen)(screen, title, passProps, option);
      }
    }, [intl, theme, isTablet, newStatus.duration, handleClearAfterClick]);
    var handleRecentCustomStatusSuggestionClick = (0, _react.useCallback)(function (status) {
      dispatchStatus({
        type: 'fromUserCustomStatusIgnoringExpire',
        status: Object.assign({}, status, {
          duration: status.duration || _$$_REQUIRE(_dependencyMap[17]).CustomStatusDurationEnum.DONT_CLEAR
        })
      });
      if (status.duration === 'date_and_time') {
        openClearAfterModal();
      }
    }, [openClearAfterModal]);
    var handleSetStatus = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!currentUser) {
        return;
      }
      if (isStatusSet) {
        var isStatusSame = (storedStatus == null ? undefined : storedStatus.emoji) === newStatus.emoji && (storedStatus == null ? undefined : storedStatus.text) === newStatus.text && (storedStatus == null ? undefined : storedStatus.duration) === newStatus.duration;
        var newExpiresAt = calculateExpiryTime(newStatus.duration, currentUser, newStatus.expiresAt);
        if (isStatusSame && newStatus.duration === 'date_and_time') {
          isStatusSame = (storedStatus == null ? undefined : storedStatus.expires_at) === newExpiresAt;
        }
        if (!isStatusSame) {
          var _newStatus$text;
          var status = {
            emoji: newStatus.emoji || 'speech_balloon',
            text: (_newStatus$text = newStatus.text) == null ? undefined : _newStatus$text.trim(),
            duration: _$$_REQUIRE(_dependencyMap[17]).CustomStatusDurationEnum.DONT_CLEAR
          };
          if (customStatusExpirySupported) {
            status.duration = newStatus.duration;
            status.expires_at = newExpiresAt;
          }
          var _yield$updateCustomSt = yield (0, _$$_REQUIRE(_dependencyMap[22]).updateCustomStatus)(serverUrl, status),
            error = _yield$updateCustomSt.error;
          if (error) {
            _reactNative.DeviceEventEmitter.emit(_$$_REQUIRE(_dependencyMap[17]).SET_CUSTOM_STATUS_FAILURE);
            return;
          }
          (0, _$$_REQUIRE(_dependencyMap[26]).updateLocalCustomStatus)(serverUrl, currentUser, status);
          dispatchStatus({
            type: 'fromUserCustomStatus',
            status: status
          });
        }
      } else if (storedStatus != null && storedStatus.emoji) {
        var _yield$unsetCustomSta = yield (0, _$$_REQUIRE(_dependencyMap[22]).unsetCustomStatus)(serverUrl),
          _error = _yield$unsetCustomSta.error;
        if (!_error) {
          (0, _$$_REQUIRE(_dependencyMap[26]).updateLocalCustomStatus)(serverUrl, currentUser, undefined);
        }
      }
      _reactNative.Keyboard.dismiss();
      if (isTablet) {
        _reactNative.DeviceEventEmitter.emit(_$$_REQUIRE(_dependencyMap[24]).Events.ACCOUNT_SELECT_TABLET_VIEW, '');
      } else {
        (0, _$$_REQUIRE(_dependencyMap[25]).dismissModal)();
      }
    }), [newStatus, isStatusSet, storedStatus, currentUser]);
    var openEmojiPicker = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[27]).preventDoubleTap)(function () {
      (0, _$$_REQUIRE(_dependencyMap[25]).openAsBottomSheet)({
        closeButtonId: 'close-emoji-picker',
        screen: _$$_REQUIRE(_dependencyMap[24]).Screens.EMOJI_PICKER,
        theme: theme,
        title: intl.formatMessage({
          id: 'mobile.custom_status.choose_emoji',
          defaultMessage: 'Choose an emoji'
        }),
        props: {
          onEmojiPress: handleEmojiClick
        }
      });
    }), [theme, intl, handleEmojiClick]);
    var handleBackButton = (0, _react.useCallback)(function () {
      if (isTablet) {
        _reactNative.DeviceEventEmitter.emit(_$$_REQUIRE(_dependencyMap[24]).Events.ACCOUNT_SELECT_TABLET_VIEW, '');
      } else {
        (0, _$$_REQUIRE(_dependencyMap[25]).dismissModal)({
          componentId: componentId
        });
      }
    }, [isTablet]);
    (0, _android_back_handler.default)(componentId, handleBackButton);
    (0, _navigation_button_pressed.default)(BTN_UPDATE_STATUS, componentId, handleSetStatus, [handleSetStatus]);
    (0, _react.useEffect)(function () {
      (0, _$$_REQUIRE(_dependencyMap[28]).mergeNavigationOptions)(componentId, {
        topBar: {
          rightButtons: [{
            enabled: true,
            id: BTN_UPDATE_STATUS,
            showAsAction: 'always',
            testID: 'custom_status.done.button',
            text: intl.formatMessage({
              id: 'mobile.custom_status.modal_confirm',
              defaultMessage: 'Done'
            }),
            color: theme.sidebarHeaderTextColor,
            fontFamily: 'IBMPlexSansArabic-Light'
          }]
        }
      });
    }, []);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [isTablet && /*#__PURE__*/(0, _jsxRuntime.jsx)(_tablet_title.default, {
        action: intl.formatMessage({
          id: 'mobile.custom_status.modal_confirm',
          defaultMessage: 'Done'
        }),
        onPress: handleSetStatus,
        testID: "custom_status",
        title: intl.formatMessage({
          id: 'mobile.routes.custom_status',
          defaultMessage: 'Set a custom status'
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[29]).SafeAreaView, {
        edges: edges,
        style: style.container,
        testID: "custom_status.screen",
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.KeyboardAvoidingView, {
          behavior: "padding",
          enabled: _reactNative.Platform.OS === 'ios',
          keyboardVerticalOffset: 100,
          contentContainerStyle: style.contentContainerStyle,
          children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
            bounces: false,
            keyboardDismissMode: "none",
            keyboardShouldPersistTaps: "always",
            testID: "custom_status.scroll_view",
            children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: style.scrollView,
              children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: style.block,
                children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_input.default, {
                  emoji: newStatus.emoji,
                  isStatusSet: isStatusSet,
                  onChangeText: handleTextChange,
                  onClearHandle: handleClear,
                  onOpenEmojiPicker: openEmojiPicker,
                  text: newStatus.text,
                  theme: theme
                }), isStatusSet && customStatusExpirySupported && /*#__PURE__*/(0, _jsxRuntime.jsx)(_clear_after.default, {
                  duration: newStatus.duration,
                  expiresAt: newStatus.expiresAt,
                  onOpenClearAfterModal: openClearAfterModal,
                  theme: theme
                })]
              }), recentCustomStatuses.length > 0 && /*#__PURE__*/(0, _jsxRuntime.jsx)(_recent_custom_statuses.default, {
                onHandleClear: handleRecentCustomStatusClear,
                onHandleSuggestionClick: handleRecentCustomStatusSuggestionClick,
                recentCustomStatuses: recentCustomStatuses,
                theme: theme
              }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_suggestions.default, {
                intl: intl,
                onHandleCustomStatusSuggestionClick: handleCustomStatusSuggestionClick,
                recentCustomStatuses: recentCustomStatuses,
                theme: theme
              })]
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: style.separator
            })]
          })
        })
      })]
    });
  };
  var _default = exports.default = CustomStatus;
