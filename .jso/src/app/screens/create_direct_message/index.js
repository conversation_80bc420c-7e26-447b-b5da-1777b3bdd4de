  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _create_direct_message = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)([], function (_ref) {
    var database = _ref.database;
    var restrictDirectMessage = (0, _$$_REQUIRE(_dependencyMap[3]).observeConfigValue)(database, 'RestrictDirectMessage').pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (v) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)(v !== _$$_REQUIRE(_dependencyMap[6]).General.RESTRICT_DIRECT_MESSAGE_ANY);
    }));
    return {
      teammateNameDisplay: (0, _$$_REQUIRE(_dependencyMap[7]).observeTeammateNameDisplay)(database),
      currentUserId: (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentUserId)(database),
      currentTeamId: (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentTeamId)(database),
      tutorialWatched: (0, _$$_REQUIRE(_dependencyMap[8]).observeTutorialWatched)(_$$_REQUIRE(_dependencyMap[6]).Tutorial.PROFILE_LONG_PRESS),
      restrictDirectMessage: restrictDirectMessage
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_create_direct_message.default));
