  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = CreateDirectMessage;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _selected_users = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _server_user_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var messages = (0, _$$_REQUIRE(_dependencyMap[14]).defineMessages)({
    dm: {
      id: 'mobile.open_dm.error',
      defaultMessage: "We couldn't open a direct message with {displayName}. Please check your connection and try again."
    },
    gm: {
      id: 'mobile.open_gm.error',
      defaultMessage: "We couldn't open a group message with those users. Please check your connection and try again."
    },
    buttonText: {
      id: 'mobile.create_direct_message.start',
      defaultMessage: 'Start Conversation'
    },
    toastMessage: {
      id: 'mobile.create_direct_message.max_limit_reached',
      defaultMessage: 'Group messages are limited to {maxCount} members'
    }
  });
  var CLOSE_BUTTON = 'close-dms';
  var close = function close() {
    _reactNative.Keyboard.dismiss();
    (0, _$$_REQUIRE(_dependencyMap[15]).dismissModal)();
  };
  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[16]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1
      },
      searchBar: {
        marginLeft: 12,
        marginRight: _reactNative.Platform.select({
          ios: 4,
          default: 12
        }),
        marginVertical: 12
      },
      loadingContainer: {
        alignItems: 'center',
        backgroundColor: theme.centerChannelBg,
        height: 70,
        justifyContent: 'center'
      },
      loadingText: {
        color: (0, _$$_REQUIRE(_dependencyMap[16]).changeOpacity)(theme.centerChannelColor, 0.6)
      },
      noResultContainer: {
        flexGrow: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center'
      },
      noResultText: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[16]).changeOpacity)(theme.centerChannelColor, 0.5)
      }, (0, _$$_REQUIRE(_dependencyMap[17]).typography)('Heading', 600, 'Light'))
    };
  });
  function removeProfileFromList(list, id) {
    var newSelectedIds = Object.assign({}, list);
    Reflect.deleteProperty(newSelectedIds, id);
    return newSelectedIds;
  }
  function CreateDirectMessage(_ref) {
    var componentId = _ref.componentId,
      currentTeamId = _ref.currentTeamId,
      currentUserId = _ref.currentUserId,
      restrictDirectMessage = _ref.restrictDirectMessage,
      teammateNameDisplay = _ref.teammateNameDisplay,
      tutorialWatched = _ref.tutorialWatched;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[18]).useServerUrl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[19]).useTheme)();
    var style = getStyleFromTheme(theme);
    var intl = (0, _$$_REQUIRE(_dependencyMap[14]).useIntl)();
    var formatMessage = intl.formatMessage;
    var mainView = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      containerHeight = _useState2[0],
      setContainerHeight = _useState2[1];
    var keyboardOverlap = (0, _$$_REQUIRE(_dependencyMap[20]).useKeyboardOverlap)(mainView, containerHeight);
    var _useState3 = (0, _react.useState)(''),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      term = _useState4[0],
      setTerm = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      startingConversation = _useState6[0],
      setStartingConversation = _useState6[1];
    var _useState7 = (0, _react.useState)({}),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      selectedIds = _useState8[0],
      setSelectedIds = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
      showToast = _useState10[0],
      setShowToast = _useState10[1];
    var selectedCount = Object.keys(selectedIds).length;
    var clearSearch = (0, _react.useCallback)(function () {
      setTerm('');
    }, []);
    var handleRemoveProfile = (0, _react.useCallback)(function (id) {
      setSelectedIds(function (current) {
        return removeProfileFromList(current, id);
      });
    }, []);
    var createDirectChannel = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (id, selectedUser) {
        var user = selectedUser || selectedIds[id];
        var displayName = (0, _$$_REQUIRE(_dependencyMap[21]).displayUsername)(user, intl.locale, teammateNameDisplay);
        var result = yield (0, _$$_REQUIRE(_dependencyMap[22]).makeDirectChannel)(serverUrl, id, displayName);
        if (result.error) {
          (0, _$$_REQUIRE(_dependencyMap[23]).alertErrorWithFallback)(intl, result.error, messages.dm);
        }
        return !result.error;
      });
      return function (_x, _x2) {
        return _ref2.apply(this, arguments);
      };
    }(), [selectedIds, intl.locale, teammateNameDisplay, serverUrl]);
    var createGroupChannel = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (ids) {
        var result = yield (0, _$$_REQUIRE(_dependencyMap[22]).makeGroupChannel)(serverUrl, ids);
        if (result.error) {
          (0, _$$_REQUIRE(_dependencyMap[23]).alertErrorWithFallback)(intl, result.error, messages.gm);
        }
        return !result.error;
      });
      return function (_x3) {
        return _ref3.apply(this, arguments);
      };
    }(), [serverUrl]);
    var startConversation = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (selectedId, selectedUser) {
        if (startingConversation) {
          return;
        }
        setStartingConversation(true);
        var idsToUse = selectedId ? Object.keys(selectedId) : Object.keys(selectedIds);
        var success;
        if (idsToUse.length === 0) {
          success = false;
        } else if (idsToUse.length > 1) {
          success = yield createGroupChannel(idsToUse);
        } else {
          success = yield createDirectChannel(idsToUse[0], selectedUser);
        }
        if (success) {
          close();
        } else {
          setStartingConversation(false);
        }
      });
      return function (_x4, _x5) {
        return _ref4.apply(this, arguments);
      };
    }(), [startingConversation, selectedIds, createGroupChannel, createDirectChannel]);
    var handleSelectProfile = (0, _react.useCallback)(function (user) {
      if (user.id === currentUserId) {
        var selectedId = (0, _defineProperty2.default)({}, currentUserId, true);
        startConversation(selectedId, user);
      } else {
        clearSearch();
        setSelectedIds(function (current) {
          if (current[user.id]) {
            return removeProfileFromList(current, user.id);
          }
          var wasSelected = current[user.id];
          if (!wasSelected && selectedCount >= _$$_REQUIRE(_dependencyMap[24]).General.MAX_USERS_IN_GM) {
            setShowToast(true);
            return current;
          }
          var newSelectedIds = Object.assign({}, current);
          if (!wasSelected) {
            newSelectedIds[user.id] = user;
          }
          return newSelectedIds;
        });
      }
    }, [currentUserId, clearSearch]);
    var onLayout = (0, _react.useCallback)(function (e) {
      setContainerHeight(e.nativeEvent.layout.height);
    }, []);
    var updateNavigationButtons = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var closeIcon = yield _compass_icon.default.getImageSource('close', 24, theme.sidebarHeaderTextColor);
      (0, _$$_REQUIRE(_dependencyMap[15]).setButtons)(componentId, {
        leftButtons: [{
          id: CLOSE_BUTTON,
          icon: closeIcon,
          testID: 'close.create_direct_message.button'
        }]
      });
    }), [intl.locale, theme]);
    var onChangeText = (0, _react.useCallback)(function (searchTerm) {
      setTerm(searchTerm);
    }, []);
    var userFetchFunction = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (page) {
        var _results$users;
        var results;
        if (restrictDirectMessage) {
          results = yield (0, _$$_REQUIRE(_dependencyMap[25]).fetchProfilesInTeam)(serverUrl, currentTeamId, page, _$$_REQUIRE(_dependencyMap[24]).General.PROFILE_CHUNK_SIZE);
        } else {
          results = yield (0, _$$_REQUIRE(_dependencyMap[25]).fetchProfiles)(serverUrl, page, _$$_REQUIRE(_dependencyMap[24]).General.PROFILE_CHUNK_SIZE);
        }
        if ((_results$users = results.users) != null && _results$users.length) {
          return results.users;
        }
        return [];
      });
      return function (_x6) {
        return _ref6.apply(this, arguments);
      };
    }(), [serverUrl, currentTeamId, restrictDirectMessage]);
    var userSearchFunction = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* (searchTerm) {
        var lowerCasedTerm = searchTerm.toLowerCase();
        var results;
        if (restrictDirectMessage) {
          results = yield (0, _$$_REQUIRE(_dependencyMap[25]).searchProfiles)(serverUrl, lowerCasedTerm, {
            team_id: currentTeamId,
            allow_inactive: true
          });
        } else {
          results = yield (0, _$$_REQUIRE(_dependencyMap[25]).searchProfiles)(serverUrl, lowerCasedTerm, {
            allow_inactive: true
          });
        }
        if (results.data) {
          return results.data;
        }
        return [];
      });
      return function (_x7) {
        return _ref7.apply(this, arguments);
      };
    }(), [serverUrl, currentTeamId, restrictDirectMessage]);
    var createUserFilter = (0, _react.useCallback)(function (exactMatches, searchTerm) {
      return function (p) {
        if (selectedCount > 0 && p.id === currentUserId) {
          return false;
        }
        if (p.username === searchTerm || p.username.startsWith(searchTerm)) {
          exactMatches.push(p);
          return false;
        }
        return true;
      };
    }, [selectedCount > 0, currentUserId]);
    (0, _navigation_button_pressed.default)(CLOSE_BUTTON, componentId, close, [close]);
    (0, _android_back_handler.default)(componentId, close);
    (0, _react.useEffect)(function () {
      updateNavigationButtons();
    }, [updateNavigationButtons]);
    (0, _react.useEffect)(function () {
      setShowToast(selectedCount >= _$$_REQUIRE(_dependencyMap[24]).General.MAX_USERS_IN_GM);
    }, [selectedCount >= _$$_REQUIRE(_dependencyMap[24]).General.MAX_USERS_IN_GM]);
    if (startingConversation) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.container,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
          color: theme.centerChannelColor
        })
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[26]).SafeAreaView, {
      style: style.container,
      testID: "create_direct_message.screen",
      onLayout: onLayout,
      ref: mainView,
      edges: ['top', 'left', 'right'],
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.searchBar,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_search.default, {
          testID: "create_direct_message.search_bar",
          placeholder: formatMessage({
            id: 'search_bar.search',
            defaultMessage: 'Search'
          }),
          cancelButtonTitle: formatMessage({
            id: 'mobile.post.cancel',
            defaultMessage: 'Cancel'
          }),
          placeholderTextColor: (0, _$$_REQUIRE(_dependencyMap[16]).changeOpacity)(theme.centerChannelColor, 0.5),
          onChangeText: onChangeText,
          onCancel: clearSearch,
          autoCapitalize: "none",
          keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[16]).getKeyboardAppearanceFromTheme)(theme),
          value: term
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_server_user_list.default, {
        currentUserId: currentUserId,
        handleSelectProfile: handleSelectProfile,
        selectedIds: selectedIds,
        term: term,
        testID: "create_direct_message.user_list",
        tutorialWatched: tutorialWatched,
        fetchFunction: userFetchFunction,
        searchFunction: userSearchFunction,
        createFilter: createUserFilter
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_selected_users.default, {
        keyboardOverlap: keyboardOverlap,
        showToast: showToast,
        setShowToast: setShowToast,
        toastIcon: 'check',
        toastMessage: formatMessage(messages.toastMessage, {
          maxCount: _$$_REQUIRE(_dependencyMap[24]).General.MAX_USERS_IN_GM
        }),
        selectedIds: selectedIds,
        onRemove: handleRemoveProfile,
        teammateNameDisplay: teammateNameDisplay,
        onPress: startConversation,
        buttonIcon: 'forum-outline',
        buttonText: formatMessage(messages.buttonText),
        testID: "create_direct_message",
        maxUsers: _$$_REQUIRE(_dependencyMap[24]).General.MAX_USERS_IN_GM
      })]
    });
  }
