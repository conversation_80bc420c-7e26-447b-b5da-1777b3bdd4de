  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = DropdownSlideup;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _slide_up_panel_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _content = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      checkIcon: {
        color: theme.buttonBg
      }
    };
  });
  function DropdownSlideup(_ref) {
    var onPress = _ref.onPress,
      canShowArchivedChannels = _ref.canShowArchivedChannels,
      selected = _ref.selected,
      sharedChannelsEnabled = _ref.sharedChannelsEnabled;
    var intl = (0, _$$_REQUIRE(_dependencyMap[6]).useIntl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var style = getStyleFromTheme(theme);
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[8]).useIsTablet)();
    var handlePublicPress = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[9]).dismissBottomSheet)();
      onPress(_$$_REQUIRE(_dependencyMap[10]).PUBLIC);
    }, [onPress]);
    var handleArchivedPress = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[9]).dismissBottomSheet)();
      onPress(_$$_REQUIRE(_dependencyMap[10]).ARCHIVED);
    }, [onPress]);
    var handleSharedPress = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[9]).dismissBottomSheet)();
      onPress(_$$_REQUIRE(_dependencyMap[10]).SHARED);
    }, [onPress]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_content.default, {
      showButton: false,
      showTitle: !isTablet,
      testID: "browse_channels.dropdown_slideup",
      title: intl.formatMessage({
        id: 'browse_channels.dropdownTitle',
        defaultMessage: 'Show'
      }),
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_slide_up_panel_item.default, {
        onPress: handlePublicPress,
        testID: "browse_channels.dropdown_slideup_item.public_channels",
        text: intl.formatMessage({
          id: 'browse_channels.publicChannels',
          defaultMessage: 'Public Channels'
        }),
        rightIcon: selected === _$$_REQUIRE(_dependencyMap[10]).PUBLIC ? 'check' : undefined,
        rightIconStyles: style.checkIcon
      }), canShowArchivedChannels && /*#__PURE__*/(0, _jsxRuntime.jsx)(_slide_up_panel_item.default, {
        onPress: handleArchivedPress,
        testID: "browse_channels.dropdown_slideup_item.archived_channels",
        text: intl.formatMessage({
          id: 'browse_channels.archivedChannels',
          defaultMessage: 'Archived Channels'
        }),
        rightIcon: selected === _$$_REQUIRE(_dependencyMap[10]).ARCHIVED ? 'check' : undefined,
        rightIconStyles: style.checkIcon
      }), sharedChannelsEnabled && /*#__PURE__*/(0, _jsxRuntime.jsx)(_slide_up_panel_item.default, {
        onPress: handleSharedPress,
        testID: "browse_channels.dropdown_slideup_item.shared_channels",
        text: intl.formatMessage({
          id: 'browse_channels.sharedChannels',
          defaultMessage: 'Shared Channels'
        }),
        rightIcon: selected === _$$_REQUIRE(_dependencyMap[10]).SHARED ? 'check' : undefined,
        rightIconStyles: style.checkIcon
      })]
    });
  }
