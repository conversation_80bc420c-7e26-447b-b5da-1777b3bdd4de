  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = ChannelDropdown;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _dropdown_slideup = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      channelDropdown: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 100, 'SemiBold'), {
        lineHeight: 20,
        color: theme.centerChannelColor,
        marginLeft: 20,
        marginTop: 12,
        marginBottom: 4
      }),
      channelDropdownIcon: {
        color: theme.centerChannelColor
      }
    };
  });
  function ChannelDropdown(_ref) {
    var typeOfChannels = _ref.typeOfChannels,
      onPress = _ref.onPress,
      canShowArchivedChannels = _ref.canShowArchivedChannels,
      sharedChannelsEnabled = _ref.sharedChannelsEnabled;
    var intl = (0, _$$_REQUIRE(_dependencyMap[8]).useIntl)();
    var _useSafeAreaInsets = (0, _$$_REQUIRE(_dependencyMap[9]).useSafeAreaInsets)(),
      bottom = _useSafeAreaInsets.bottom;
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var style = getStyleFromTheme(theme);

    // Depends on all props, so no need to use a callback.
    var handleDropdownClick = function handleDropdownClick() {
      var renderContent = function renderContent() {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_dropdown_slideup.default, {
          canShowArchivedChannels: canShowArchivedChannels,
          onPress: onPress,
          sharedChannelsEnabled: sharedChannelsEnabled,
          selected: typeOfChannels
        });
      };
      var items = 1;
      if (canShowArchivedChannels) {
        items += 1;
      }
      if (sharedChannelsEnabled) {
        items += 1;
      }
      var itemsSnap = (0, _$$_REQUIRE(_dependencyMap[11]).bottomSheetSnapPoint)(items, _$$_REQUIRE(_dependencyMap[12]).ITEM_HEIGHT, bottom) + _$$_REQUIRE(_dependencyMap[13]).TITLE_HEIGHT;
      (0, _$$_REQUIRE(_dependencyMap[14]).bottomSheet)({
        title: intl.formatMessage({
          id: 'browse_channels.dropdownTitle',
          defaultMessage: 'Show'
        }),
        renderContent: renderContent,
        snapPoints: [1, itemsSnap],
        closeButtonId: 'close',
        theme: theme
      });
    };
    var channelDropdownText = intl.formatMessage({
      id: 'browse_channels.showPublicChannels',
      defaultMessage: 'Show: Public Channels'
    });
    if (typeOfChannels === _$$_REQUIRE(_dependencyMap[15]).SHARED) {
      channelDropdownText = intl.formatMessage({
        id: 'browse_channels.showSharedChannels',
        defaultMessage: 'Show: Shared Channels'
      });
    } else if (typeOfChannels === _$$_REQUIRE(_dependencyMap[15]).ARCHIVED) {
      channelDropdownText = intl.formatMessage({
        id: 'browse_channels.showArchivedChannels',
        defaultMessage: 'Show: Archived Channels'
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      testID: "browse_channels.channel_dropdown",
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.Text, {
        accessibilityRole: 'button',
        style: style.channelDropdown,
        onPress: handleDropdownClick,
        testID: `browse_channels.channel_dropdown.text.${typeOfChannels}`,
        children: [channelDropdownText, '  ', /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          name: "menu-down",
          size: 18,
          style: style.channelDropdownIcon
        })]
      })
    });
  }
