  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _search_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)([], function (_ref) {
    var database = _ref.database;
    var sharedChannelsEnabled = (0, _$$_REQUIRE(_dependencyMap[3]).observeConfigBooleanValue)(database, 'ExperimentalSharedChannels');
    var canShowArchivedChannels = (0, _$$_REQUIRE(_dependencyMap[3]).observeConfigBooleanValue)(database, 'ExperimentalViewArchivedChannels');
    var currentTeamId = (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentTeamId)(database);
    var currentUserId = (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentUserId)(database);
    var joinedChannels = (0, _$$_REQUIRE(_dependencyMap[4]).queryAllMyChannel)(database).observe();
    var roles = currentUserId.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (id) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).observeUser)(database, id);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (u) {
      return u ? (0, _$$_REQUIRE(_dependencyMap[7]).of)(u.roles.split(' ')) : (0, _$$_REQUIRE(_dependencyMap[7]).of)([]);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (values) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).queryRolesByNames)(database, values).observeWithColumns(['permissions']);
    }));
    var canCreateChannels = roles.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (r) {
      return (0, _$$_REQUIRE(_dependencyMap[7]).of)((0, _$$_REQUIRE(_dependencyMap[9]).hasPermission)(r, _$$_REQUIRE(_dependencyMap[10]).Permissions.CREATE_PUBLIC_CHANNEL));
    }));
    return {
      canCreateChannels: canCreateChannels,
      currentTeamId: currentTeamId,
      joinedChannels: joinedChannels,
      sharedChannelsEnabled: sharedChannelsEnabled,
      canShowArchivedChannels: canShowArchivedChannels
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_search_handler.default));
