  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SHARED = exports.PUBLIC = exports.ARCHIVED = undefined;
  exports.default = BrowseChannels;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _channel_dropdown = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _channel_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var CLOSE_BUTTON_ID = 'close-browse-channels';
  var CREATE_BUTTON_ID = 'create-pub-channel';
  var PUBLIC = exports.PUBLIC = 'public';
  var SHARED = exports.SHARED = 'shared';
  var ARCHIVED = exports.ARCHIVED = 'archived';
  var makeLeftButton = function makeLeftButton(icon) {
    return {
      id: CLOSE_BUTTON_ID,
      icon: icon,
      testID: 'close.browse_channels.button'
    };
  };
  var makeRightButton = function makeRightButton(theme, formatMessage, enabled) {
    return {
      color: theme.sidebarHeaderTextColor,
      id: CREATE_BUTTON_ID,
      text: formatMessage({
        id: 'mobile.create_channel',
        defaultMessage: 'Create'
      }),
      showAsAction: 'always',
      testID: 'browse_channels.create.button',
      enabled: enabled,
      fontFamily: 'IBMPlexSansArabic-Light'
    };
  };
  var close = function close() {
    _reactNative.Keyboard.dismiss();
    (0, _$$_REQUIRE(_dependencyMap[12]).dismissModal)();
  };
  var style = _reactNative.StyleSheet.create({
    container: {
      flex: 1
    },
    searchBar: {
      marginLeft: 12,
      marginRight: _reactNative.Platform.select({
        ios: 4,
        default: 12
      }),
      marginTop: 12
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center'
    }
  });
  function BrowseChannels(props) {
    var componentId = props.componentId,
      canCreateChannels = props.canCreateChannels,
      sharedChannelsEnabled = props.sharedChannelsEnabled,
      closeButton = props.closeButton,
      currentTeamId = props.currentTeamId,
      canShowArchivedChannels = props.canShowArchivedChannels,
      typeOfChannels = props.typeOfChannels,
      changeTypeOfChannels = props.changeChannelType,
      term = props.term,
      searchChannels = props.searchChannels,
      stopSearch = props.stopSearch,
      channels = props.channels,
      loading = props.loading,
      onEndReached = props.onEndReached;
    var intl = (0, _$$_REQUIRE(_dependencyMap[13]).useIntl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[14]).useTheme)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[15]).useServerUrl)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      adding = _useState2[0],
      setAdding = _useState2[1];
    var setHeaderButtons = (0, _react.useCallback)(function (createEnabled) {
      var buttons = {
        leftButtons: [makeLeftButton(closeButton)],
        rightButtons: []
      };
      if (canCreateChannels) {
        buttons.rightButtons = [makeRightButton(theme, intl.formatMessage, createEnabled)];
      }
      (0, _$$_REQUIRE(_dependencyMap[12]).setButtons)(componentId, buttons);
    }, [closeButton, canCreateChannels, intl.locale, theme, componentId]);
    var onSelectChannel = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (channel) {
        setHeaderButtons(false);
        setAdding(true);
        var result = yield (0, _$$_REQUIRE(_dependencyMap[16]).joinChannel)(serverUrl, currentTeamId, channel.id, '', false);
        if (result.error) {
          (0, _$$_REQUIRE(_dependencyMap[17]).alertErrorWithFallback)(intl, result.error, {
            id: 'mobile.join_channel.error',
            defaultMessage: "We couldn't join the channel {displayName}."
          }, {
            displayName: channel.display_name
          });
          setHeaderButtons(true);
          setAdding(false);
        } else {
          (0, _$$_REQUIRE(_dependencyMap[16]).switchToChannelById)(serverUrl, channel.id, currentTeamId);
          close();
        }
      });
      return function (_x) {
        return _ref.apply(this, arguments);
      };
    }(), [setHeaderButtons, intl.locale]);
    var onSearch = (0, _react.useCallback)(function () {
      searchChannels(term);
    }, [term, searchChannels]);
    var handleCreate = (0, _react.useCallback)(function () {
      var screen = _$$_REQUIRE(_dependencyMap[18]).Screens.CREATE_OR_EDIT_CHANNEL;
      var title = intl.formatMessage({
        id: 'mobile.create_channel.title',
        defaultMessage: 'New channel'
      });
      (0, _$$_REQUIRE(_dependencyMap[12]).goToScreen)(screen, title);
    }, [intl.locale]);
    (0, _navigation_button_pressed.default)(CLOSE_BUTTON_ID, componentId, close, [close]);
    (0, _navigation_button_pressed.default)(CREATE_BUTTON_ID, componentId, handleCreate, [handleCreate]);
    (0, _android_back_handler.default)(componentId, close);
    (0, _react.useEffect)(function () {
      // Update header buttons in case anything related to the header changes
      setHeaderButtons(!adding);
    }, [theme, canCreateChannels, adding]);
    var content;
    if (adding) {
      content = /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
        containerStyle: style.loadingContainer,
        size: "large",
        color: theme.buttonBg
      });
    } else {
      var channelDropdown;
      if (canShowArchivedChannels || sharedChannelsEnabled) {
        channelDropdown = /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_dropdown.default, {
          onPress: changeTypeOfChannels,
          typeOfChannels: typeOfChannels,
          canShowArchivedChannels: canShowArchivedChannels,
          sharedChannelsEnabled: sharedChannelsEnabled
        });
      }
      content = /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          testID: "browse_channels.screen",
          style: style.searchBar,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_search.default, {
            testID: "browse_channels.search_bar",
            placeholder: intl.formatMessage({
              id: 'search_bar.search',
              defaultMessage: 'Search'
            }),
            cancelButtonTitle: intl.formatMessage({
              id: 'mobile.post.cancel',
              defaultMessage: 'Cancel'
            }),
            placeholderTextColor: (0, _$$_REQUIRE(_dependencyMap[19]).changeOpacity)(theme.centerChannelColor, 0.5),
            onChangeText: searchChannels,
            onSubmitEditing: onSearch,
            onCancel: stopSearch,
            autoCapitalize: "none",
            keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[19]).getKeyboardAppearanceFromTheme)(theme),
            value: term
          })
        }), channelDropdown, /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_list.default, {
          channels: channels,
          onEndReached: onEndReached,
          loading: loading,
          onSelectChannel: onSelectChannel,
          term: term
        })]
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[20]).SafeAreaView, {
      style: style.container,
      children: content
    });
  }
