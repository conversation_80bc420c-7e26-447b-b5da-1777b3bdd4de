  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = ChannelList;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _channel_list_row = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _no_results_with_term = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var channelKeyExtractor = function channelKeyExtractor(channel) {
    return channel.id;
  };
  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[8]).makeStyleSheetFromTheme)(function (theme) {
    return {
      loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
      },
      listContainer: {
        paddingHorizontal: 20,
        flexGrow: 1
      },
      noResultContainer: {
        flexGrow: 1,
        alignItems: 'center',
        justifyContent: 'center'
      },
      noResultText: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.centerChannelColor, 0.5)
      }, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 600, 'Light')),
      separator: {
        height: 1,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.centerChannelColor, 0.08),
        width: '100%'
      }
    };
  });
  function ChannelList(_ref) {
    var onEndReached = _ref.onEndReached,
      onSelectChannel = _ref.onSelectChannel,
      loading = _ref.loading,
      term = _ref.term,
      channels = _ref.channels;
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var style = getStyleFromTheme(theme);
    var keyboardHeight = (0, _$$_REQUIRE(_dependencyMap[11]).useKeyboardHeight)();
    var noResutsStyle = (0, _react.useMemo)(function () {
      return [style.noResultContainer, {
        paddingBottom: keyboardHeight
      }];
    }, [style, keyboardHeight]);
    var renderItem = (0, _react.useCallback)(function (_ref2) {
      var item = _ref2.item;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_list_row.default, {
        channel: item,
        testID: "browse_channels.custom_list.channel_item",
        onPress: onSelectChannel
      });
    }, [onSelectChannel]);
    var renderLoading = (0, _react.useCallback)(function () {
      if (!loading) {
        return null;
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
        color: theme.buttonBg,
        containerStyle: style.loadingContainer,
        size: "large"
      });

      //Style is covered by the theme
    }, [loading, theme]);
    var renderNoResults = (0, _react.useCallback)(function () {
      if (term) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: noResutsStyle,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_no_results_with_term.default, {
            term: term
          })
        });
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: noResutsStyle,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "browse_channels.noMore",
          defaultMessage: "No more channels to join",
          style: style.noResultText
        })
      });
    }, [style, term, noResutsStyle]);
    var renderSeparator = (0, _react.useCallback)(function () {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.separator
      });
    }, [theme]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
      data: channels,
      renderItem: renderItem,
      testID: "browse_channels.channel_list.flat_list",
      ListEmptyComponent: renderNoResults,
      ListFooterComponent: renderLoading,
      onEndReached: onEndReached,
      contentContainerStyle: style.listContainer,
      ItemSeparatorComponent: renderSeparator,
      keyExtractor: channelKeyExtractor
    });
  }
