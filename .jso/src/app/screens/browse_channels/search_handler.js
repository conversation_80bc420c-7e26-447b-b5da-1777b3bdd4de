  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = SearchHandler;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _did_update = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _browse_channels = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var _excluded = ["joinedChannels", "currentTeamId"]; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  var MIN_CHANNELS_LOADED = 10;
  var LOAD = 'load';
  var STOP = 'stop';
  var filterChannelsByType = function filterChannelsByType(channels, joinedChannels, channelType) {
    var ids = new Set(joinedChannels.map(function (c) {
      return c.id;
    }));
    var filter;
    switch (channelType) {
      case _browse_channels.ARCHIVED:
        filter = function filter(c) {
          return c.delete_at !== 0;
        };
        break;
      case _browse_channels.SHARED:
        filter = function filter(c) {
          return c.delete_at === 0 && c.shared && !ids.has(c.id);
        };
        break;
      case _browse_channels.PUBLIC:
      default:
        filter = function filter(c) {
          return c.delete_at === 0 && !c.shared && !ids.has(c.id);
        };
        break;
    }
    return channels.filter(filter);
  };
  var filterJoinedChannels = function filterJoinedChannels(joinedChannels, allChannels) {
    var ids = new Set(joinedChannels.map(function (c) {
      return c.id;
    }));
    return allChannels == null ? undefined : allChannels.filter(function (c) {
      return !ids.has(c.id);
    });
  };
  var LoadAction = {
    type: LOAD,
    data: []
  };
  var StopAction = {
    type: STOP,
    data: []
  };
  var addAction = function addAction(t, data) {
    return {
      type: t,
      data: data
    };
  };
  var reducer = function reducer(state, action) {
    switch (action.type) {
      case _browse_channels.PUBLIC:
        return Object.assign({}, state, {
          channels: [].concat((0, _toConsumableArray2.default)(state.channels), (0, _toConsumableArray2.default)(action.data)),
          loading: false
        });
      case _browse_channels.ARCHIVED:
        return Object.assign({}, state, {
          archivedChannels: [].concat((0, _toConsumableArray2.default)(state.archivedChannels), (0, _toConsumableArray2.default)(action.data)),
          loading: false
        });
      case _browse_channels.SHARED:
        return Object.assign({}, state, {
          sharedChannels: [].concat((0, _toConsumableArray2.default)(state.sharedChannels), (0, _toConsumableArray2.default)(action.data)),
          loading: false
        });
      case LOAD:
        if (state.loading) {
          return state;
        }
        return Object.assign({}, state, {
          loading: true
        });
      case STOP:
        if (state.loading) {
          return Object.assign({}, state, {
            loading: false
          });
        }
        return state;
      default:
        return state;
    }
  };
  var initialState = {
    channels: [],
    archivedChannels: [],
    sharedChannels: [],
    loading: false
  };
  var defaultJoinedChannels = [];
  var defaultSearchResults = [];
  function SearchHandler(props) {
    var _props$joinedChannels = props.joinedChannels,
      joinedChannels = _props$joinedChannels === undefined ? defaultJoinedChannels : _props$joinedChannels,
      currentTeamId = props.currentTeamId,
      passProps = (0, _objectWithoutProperties2.default)(props, _excluded);
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[9]).useServerUrl)();
    var _useReducer = (0, _react.useReducer)(reducer, initialState),
      _useReducer2 = (0, _slicedToArray2.default)(_useReducer, 2),
      _useReducer2$ = _useReducer2[0],
      channels = _useReducer2$.channels,
      archivedChannels = _useReducer2$.archivedChannels,
      sharedChannels = _useReducer2$.sharedChannels,
      loading = _useReducer2$.loading,
      dispatch = _useReducer2[1];
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      visibleChannels = _useState2[0],
      setVisibleChannels = _useState2[1];
    var _useState3 = (0, _react.useState)(''),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      term = _useState4[0],
      setTerm = _useState4[1];
    var _useState5 = (0, _react.useState)(_browse_channels.PUBLIC),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      typeOfChannels = _useState6[0],
      setTypeOfChannels = _useState6[1];
    var publicPage = (0, _react.useRef)(-1);
    var sharedPage = (0, _react.useRef)(-1);
    var archivedPage = (0, _react.useRef)(-1);
    var nextPublic = (0, _react.useRef)(true);
    var nextShared = (0, _react.useRef)(true);
    var nextArchived = (0, _react.useRef)(true);
    var loadedChannels = (0, _react.useRef)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {/* Do nothing */}));
    var searchTimeout = (0, _react.useRef)();
    var _useState7 = (0, _react.useState)(defaultSearchResults),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      searchResults = _useState8[0],
      setSearchResults = _useState8[1];
    var isSearch = Boolean(term);
    var doGetChannels = function doGetChannels(t) {
      var next;
      var fetch;
      var page;
      switch (t) {
        case _browse_channels.SHARED:
          next = nextShared;
          fetch = _$$_REQUIRE(_dependencyMap[10]).fetchSharedChannels;
          page = sharedPage;
          break;
        case _browse_channels.ARCHIVED:
          next = nextArchived;
          fetch = _$$_REQUIRE(_dependencyMap[10]).fetchArchivedChannels;
          page = archivedPage;
          break;
        case _browse_channels.PUBLIC:
        default:
          next = nextPublic;
          fetch = _$$_REQUIRE(_dependencyMap[10]).fetchChannels;
          page = publicPage;
      }
      if (next.current) {
        dispatch(LoadAction);
        fetch(serverUrl, currentTeamId, page.current + 1, _$$_REQUIRE(_dependencyMap[11]).General.CHANNELS_CHUNK_SIZE).then(function (_ref2) {
          var receivedChannels = _ref2.channels;
          return loadedChannels.current(receivedChannels, t);
        }).catch(function () {
          return dispatch(StopAction);
        });
      }
    };
    var onEndReached = (0, _react.useCallback)(function () {
      if (!loading && !term) {
        doGetChannels(typeOfChannels);
      }
    }, [typeOfChannels, loading, term]);
    var activeChannels;
    switch (typeOfChannels) {
      case _browse_channels.ARCHIVED:
        activeChannels = archivedChannels;
        break;
      case _browse_channels.SHARED:
        activeChannels = sharedChannels;
        break;
      default:
        activeChannels = channels;
    }
    var stopSearch = (0, _react.useCallback)(function () {
      setSearchResults(defaultSearchResults);
      setTerm('');
    }, [activeChannels]);
    var doSearchChannels = (0, _react.useCallback)(function (text) {
      if (text) {
        setSearchResults(defaultSearchResults);
        if (searchTimeout.current) {
          clearTimeout(searchTimeout.current);
        }
        searchTimeout.current = setTimeout(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
          var results = yield (0, _$$_REQUIRE(_dependencyMap[10]).searchChannels)(serverUrl, text, currentTeamId);
          if (results.channels) {
            setSearchResults(results.channels);
          }
          dispatch(StopAction);
        }), 500);
        setTerm(text);
        setVisibleChannels(searchResults);
        dispatch(LoadAction);
      } else {
        stopSearch();
      }
    }, [activeChannels, visibleChannels, joinedChannels, stopSearch]);
    var changeChannelType = (0, _react.useCallback)(function (channelType) {
      setTypeOfChannels(channelType);
    }, []);
    (0, _react.useEffect)(function () {
      loadedChannels.current = /*#__PURE__*/function () {
        var _ref4 = (0, _asyncToGenerator2.default)(function* (data, t) {
          var _filtered;
          var next;
          var page;
          var shouldFilterJoined;
          switch (t) {
            case _browse_channels.SHARED:
              page = sharedPage;
              next = nextShared;
              shouldFilterJoined = true;
              break;
            case _browse_channels.ARCHIVED:
              page = archivedPage;
              next = nextArchived;
              shouldFilterJoined = false;
              break;
            case _browse_channels.PUBLIC:
            default:
              page = publicPage;
              next = nextPublic;
              shouldFilterJoined = true;
          }
          page.current += 1;
          next.current = Boolean(data == null ? undefined : data.length);
          var filtered = data;
          if (shouldFilterJoined) {
            filtered = filterJoinedChannels(joinedChannels, data);
          }
          if ((_filtered = filtered) != null && _filtered.length) {
            dispatch(addAction(t, filtered));
          } else if (data != null && data.length) {
            doGetChannels(t);
          } else {
            dispatch(StopAction);
          }
        });
        return function (_x, _x2) {
          return _ref4.apply(this, arguments);
        };
      }();
      return function () {
        loadedChannels.current = /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {/* Do nothing */});
      };
    }, [joinedChannels]);
    (0, _react.useEffect)(function () {
      if (!isSearch) {
        doGetChannels(typeOfChannels);
      }
    }, [typeOfChannels, isSearch]);
    (0, _did_update.default)(function () {
      if (isSearch) {
        setVisibleChannels(filterChannelsByType(searchResults, joinedChannels, typeOfChannels));
      } else {
        setVisibleChannels(activeChannels);
      }
    }, [activeChannels, isSearch && searchResults, isSearch && typeOfChannels, joinedChannels]);

    // Make sure enough channels are loaded to allow the FlatList to scroll,
    // and let it call the onReachEnd function.
    (0, _did_update.default)(function () {
      if (loading || isSearch || visibleChannels.length >= MIN_CHANNELS_LOADED) {
        return;
      }
      var next;
      switch (typeOfChannels) {
        case _browse_channels.PUBLIC:
          next = nextPublic.current;
          break;
        case _browse_channels.SHARED:
          next = nextShared.current;
          break;
        default:
          next = nextArchived.current;
      }
      if (next) {
        doGetChannels(typeOfChannels);
      }
    }, [visibleChannels.length >= MIN_CHANNELS_LOADED, loading, isSearch]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_browse_channels.default, Object.assign({}, passProps, {
      currentTeamId: currentTeamId,
      changeChannelType: changeChannelType,
      channels: visibleChannels,
      loading: loading,
      onEndReached: onEndReached,
      searchChannels: doSearchChannels,
      stopSearch: stopSearch,
      term: term,
      typeOfChannels: typeOfChannels
    }));
  }
