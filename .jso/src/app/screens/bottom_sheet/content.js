  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.TITLE_SEPARATOR_MARGIN_TABLET = exports.TITLE_SEPARATOR_MARGIN = exports.TITLE_HEIGHT = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var TITLE_MARGIN_TOP = 4;
  var TITLE_MARGIN_BOTTOM = 12;
  var TITLE_HEIGHT = exports.TITLE_HEIGHT = 46; // typography 600 line height
  var TITLE_SEPARATOR_MARGIN = exports.TITLE_SEPARATOR_MARGIN = 12;
  var TITLE_SEPARATOR_MARGIN_TABLET = exports.TITLE_SEPARATOR_MARGIN_TABLET = 20;
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flexGrow: 1
      },
      titleContainer: {
        marginTop: TITLE_MARGIN_TOP,
        marginBottom: TITLE_MARGIN_BOTTOM
      },
      titleText: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 600, 'SemiBold')),
      separator: {
        height: 1,
        right: 20,
        borderTopWidth: 1,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.08),
        marginBottom: 20
      }
    };
  });
  var BottomSheetContent = function BottomSheetContent(_ref) {
    var buttonText = _ref.buttonText,
      buttonIcon = _ref.buttonIcon,
      children = _ref.children,
      disableButton = _ref.disableButton,
      onPress = _ref.onPress,
      showButton = _ref.showButton,
      showTitle = _ref.showTitle,
      testID = _ref.testID,
      title = _ref.title,
      titleSeparator = _ref.titleSeparator;
    var dimensions = (0, _reactNative.useWindowDimensions)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[8]).useIsTablet)();
    var styles = getStyleSheet(theme);
    var separatorWidth = Math.max(dimensions.width, 450);
    var buttonTestId = `${testID}.${buttonText == null ? undefined : buttonText.replace(/ /g, '_').toLocaleLowerCase()}.button`;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      testID: `${testID}.screen`,
      children: [showTitle && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.titleContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.titleText,
          testID: `${testID}.title`,
          children: title
        })
      }), titleSeparator && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.separator, {
          width: separatorWidth,
          marginBottom: isTablet ? TITLE_SEPARATOR_MARGIN_TABLET : TITLE_SEPARATOR_MARGIN
        }]
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: children
      }), showButton && /*#__PURE__*/(0, _jsxRuntime.jsx)(_button.default, {
        disabled: disableButton,
        onPress: onPress,
        icon: buttonIcon,
        testID: buttonTestId,
        text: buttonText
      })]
    });
  };
  var _default = exports.default = BottomSheetContent;
