  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  Object.defineProperty(exports, "BUTTON_HEIGHT", {
    enumerable: true,
    get: function get() {
      return _button.BUTTON_HEIGHT;
    }
  });
  Object.defineProperty(exports, "BottomSheetButton", {
    enumerable: true,
    get: function get() {
      return _button.default;
    }
  });
  Object.defineProperty(exports, "BottomSheetContent", {
    enumerable: true,
    get: function get() {
      return _content.default;
    }
  });
  Object.defineProperty(exports, "TITLE_HEIGHT", {
    enumerable: true,
    get: function get() {
      return _content.TITLE_HEIGHT;
    }
  });
  exports.getStyleSheet = exports.default = exports.animatedConfig = undefined;
  var _bottomSheet = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _indicator = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _redirectController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var _button = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _content = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var PADDING_TOP_MOBILE = 20;
  var PADDING_TOP_TABLET = 8;
  var getStyleSheet = exports.getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[11]).makeStyleSheetFromTheme)(function (theme) {
    return {
      bottomSheet: {
        backgroundColor: theme.centerChannelBg,
        borderTopStartRadius: 24,
        borderTopEndRadius: 24,
        shadowOffset: {
          width: 0,
          height: 8
        },
        shadowOpacity: 0.12,
        shadowRadius: 24,
        shadowColor: '#000',
        elevation: 24
      },
      bottomSheetBackground: {
        backgroundColor: theme.centerChannelBg,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[11]).changeOpacity)(theme.centerChannelColor, 0.16)
      },
      content: {
        flex: 1,
        paddingHorizontal: 20,
        paddingTop: PADDING_TOP_MOBILE
      },
      contentTablet: {
        paddingTop: PADDING_TOP_TABLET
      },
      separator: {
        height: 1,
        borderTopWidth: 1,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[11]).changeOpacity)(theme.centerChannelColor, 0.08)
      }
    };
  });
  var animatedConfig = exports.animatedConfig = {
    damping: 50,
    mass: 0.3,
    stiffness: 121.6,
    overshootClamping: true,
    restSpeedThreshold: 0.3,
    restDisplacementThreshold: 0.3,
    reduceMotion: _$$_REQUIRE(_dependencyMap[12]).ReduceMotion.Never
  };
  var BottomSheet = function BottomSheet(_ref) {
    var closeButtonId = _ref.closeButtonId,
      componentId = _ref.componentId,
      contentStyle = _ref.contentStyle,
      _ref$initialSnapIndex = _ref.initialSnapIndex,
      initialSnapIndex = _ref$initialSnapIndex === undefined ? 1 : _ref$initialSnapIndex,
      footerComponent = _ref.footerComponent,
      renderContent = _ref.renderContent,
      _ref$snapPoints = _ref.snapPoints,
      snapPoints = _ref$snapPoints === undefined ? [1, '50%', '80%'] : _ref$snapPoints,
      testID = _ref.testID;
    var sheetRef = (0, _react.useRef)(null);
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[13]).useIsTablet)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[14]).useTheme)();
    var styles = getStyleSheet(theme);
    var interaction = (0, _react.useRef)();
    var timeoutRef = (0, _react.useRef)();
    var _useRedirectMessageCo = (0, _redirectController.default)(),
      changeisRedirecMessageState = _useRedirectMessageCo.changeisRedirecMessageState;
    (0, _react.useEffect)(function () {
      interaction.current = _reactNative.InteractionManager.createInteractionHandle();
    }, []);
    var bottomSheetBackgroundStyle = (0, _react.useMemo)(function () {
      return [styles.bottomSheetBackground, {
        borderWidth: isTablet ? 0 : 1
      }];
    }, [isTablet, styles]);
    var close = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[15]).dismissModal)({
        componentId: componentId
      });
      changeisRedirecMessageState(false);
    }, [componentId]);
    (0, _react.useEffect)(function () {
      var listener = _reactNative.DeviceEventEmitter.addListener(_$$_REQUIRE(_dependencyMap[16]).Events.CLOSE_BOTTOM_SHEET, function () {
        if (sheetRef.current) {
          sheetRef.current.close();
        } else {
          close();
        }
      });
      return function () {
        return listener.remove();
      };
    }, [close]);
    var handleAnimationStart = (0, _react.useCallback)(function () {
      if (!interaction.current) {
        interaction.current = _reactNative.InteractionManager.createInteractionHandle();
      }
    }, []);
    var handleClose = (0, _react.useCallback)(function () {
      if (sheetRef.current) {
        sheetRef.current.close();
      } else {
        close();
      }
    }, []);
    var handleChange = (0, _react.useCallback)(function (index) {
      timeoutRef.current = setTimeout(function () {
        if (interaction.current) {
          _reactNative.InteractionManager.clearInteractionHandle(interaction.current);
          interaction.current = undefined;
        }
      });
      if (index <= 0) {
        close();
      }
    }, []);
    (0, _android_back_handler.default)(componentId, handleClose);
    (0, _navigation_button_pressed.default)(closeButtonId || '', componentId, close, [close]);
    (0, _react.useEffect)(function () {
      (0, _$$_REQUIRE(_dependencyMap[17]).hapticFeedback)();
      _reactNative.Keyboard.dismiss();
      return function () {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        if (interaction.current) {
          _reactNative.InteractionManager.clearInteractionHandle(interaction.current);
        }
      };
    }, []);
    var renderBackdrop = (0, _react.useCallback)(function (props) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_bottomSheet.BottomSheetBackdrop, Object.assign({}, props, {
        disappearsOnIndex: 0,
        appearsOnIndex: 1,
        opacity: 0.6
      }));
    }, []);
    var renderContainerContent = function renderContainerContent() {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.content, isTablet && styles.contentTablet, contentStyle],
        testID: `${testID}.screen`,
        children: renderContent()
      });
    };
    if (isTablet) {
      var FooterComponent = footerComponent;
      return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.separator
        }), renderContainerContent(), FooterComponent && /*#__PURE__*/(0, _jsxRuntime.jsx)(FooterComponent, {})]
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_bottomSheet.default, {
      ref: sheetRef,
      index: initialSnapIndex,
      snapPoints: snapPoints,
      animateOnMount: true,
      backdropComponent: renderBackdrop,
      onAnimate: handleAnimationStart,
      onChange: handleChange,
      animationConfigs: animatedConfig,
      handleComponent: _indicator.default,
      style: styles.bottomSheet,
      backgroundStyle: bottomSheetBackgroundStyle,
      footerComponent: footerComponent,
      keyboardBehavior: "extend",
      keyboardBlurBehavior: "restore",
      onClose: close,
      children: renderContainerContent()
    });
  };
  var _default = exports.default = BottomSheet;
