  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[4]).makeStyleSheetFromTheme)(function (theme) {
    return {
      dragIndicatorContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        top: 10
      },
      dragIndicator: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[4]).changeOpacity)(theme.sidebarText, 0.16),
        height: 5,
        width: 42,
        opacity: 0.9,
        borderRadius: 25
      }
    };
  });
  var Indicator = function Indicator() {
    var theme = (0, _$$_REQUIRE(_dependencyMap[5]).useTheme)();
    var styles = getStyleSheet(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
      style: styles.dragIndicatorContainer,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.dragIndicator
      })
    });
  };
  var _default = exports.default = Indicator;
