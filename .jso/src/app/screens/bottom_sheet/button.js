  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.BUTTON_HEIGHT = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      button: {
        display: 'flex',
        flexDirection: 'row'
      },
      buttonContainer: {
        paddingHorizontal: 20
      },
      container: {
        backgroundColor: theme.centerChannelBg
      },
      iconContainer: {
        width: 24,
        height: 24,
        top: -1,
        marginRight: 4
      },
      separator: {
        height: 1,
        right: 20,
        borderTopWidth: 1,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.08),
        marginBottom: 20
      }
    };
  });
  var BUTTON_HEIGHT = exports.BUTTON_HEIGHT = 101;
  function BottomSheetButton(_ref) {
    var _ref$disabled = _ref.disabled,
      disabled = _ref$disabled === undefined ? false : _ref$disabled,
      onPress = _ref.onPress,
      icon = _ref.icon,
      testID = _ref.testID,
      text = _ref.text;
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var dimensions = (0, _reactNative.useWindowDimensions)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[8]).useIsTablet)();
    var styles = getStyleSheet(theme);
    var separatorWidth = Math.max(dimensions.width, 450);
    var buttonType = disabled ? 'disabled' : 'default';
    var styleButtonText = (0, _$$_REQUIRE(_dependencyMap[9]).buttonTextStyle)(theme, 'lg', 'primary', buttonType);
    var styleButtonBackground = (0, _$$_REQUIRE(_dependencyMap[9]).buttonBackgroundStyle)(theme, 'lg', 'primary', buttonType);
    var iconColor = disabled ? (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.32) : theme.buttonColor;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.separator, {
          width: separatorWidth
        }]
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.buttonContainer,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_touchable_with_feedback.default, {
          onPress: onPress,
          type: "opacity",
          style: [styles.button, styleButtonBackground],
          testID: testID,
          children: [icon && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.iconContainer,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
              size: 24,
              name: icon,
              color: iconColor
            })
          }), text && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styleButtonText,
            children: text
          })]
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: {
            paddingBottom: _reactNative.Platform.select({
              ios: isTablet ? 20 : 32,
              android: 20
            })
          }
        })]
      })]
    });
  }
  var _default = exports.default = BottomSheetButton;
