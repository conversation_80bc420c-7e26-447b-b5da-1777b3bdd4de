  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      textContainer: {
        marginBottom: 32,
        maxWidth: 600,
        width: '100%',
        paddingHorizontal: 20
      },
      title: Object.assign({
        letterSpacing: -1,
        color: theme.centerChannelColor,
        marginVertical: 12
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 1000, 'SemiBold')),
      description: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.64)
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200, 'Light'))
    };
  });
  var EditServerHeader = function EditServerHeader(_ref) {
    var theme = _ref.theme;
    var styles = getStyleSheet(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.textContainer,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        defaultMessage: "Edit server name",
        id: "edit_server.title",
        style: styles.title,
        testID: "edit_server_header.title"
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        defaultMessage: "Specify a display name for this server",
        id: "edit_server.description",
        style: styles.description,
        testID: "edit_server_header.description"
      })]
    });
  };
  var _default = exports.default = EditServerHeader;
