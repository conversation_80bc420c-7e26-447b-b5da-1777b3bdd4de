  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _manager = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _background = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _form = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _header = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[12]).makeStyleSheetFromTheme)(function (theme) {
    return {
      appInfo: {
        color: (0, _$$_REQUIRE(_dependencyMap[12]).changeOpacity)(theme.centerChannelColor, 0.56)
      },
      flex: {
        flex: 1
      },
      scrollContainer: {
        alignItems: 'center',
        height: '100%',
        justifyContent: 'center'
      }
    };
  });
  var EditServer = function EditServer(_ref) {
    var closeButtonId = _ref.closeButtonId,
      componentId = _ref.componentId,
      server = _ref.server,
      theme = _ref.theme;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[13]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var keyboardAwareRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      saving = _useState2[0],
      setSaving = _useState2[1];
    var _useState3 = (0, _react.useState)(server.displayName),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      displayName = _useState4[0],
      setDisplayName = _useState4[1];
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      buttonDisabled = _useState6[0],
      setButtonDisabled = _useState6[1];
    var _useState7 = (0, _react.useState)(),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      displayNameError = _useState8[0],
      setDisplayNameError = _useState8[1];
    var styles = getStyleSheet(theme);
    var close = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[14]).dismissModal)({
        componentId: componentId
      });
    }, [componentId]);
    (0, _react.useEffect)(function () {
      setButtonDisabled(Boolean(!displayName || displayName === server.displayName));
    }, [displayName]);
    var handleUpdate = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (buttonDisabled) {
        return;
      }
      if (displayNameError) {
        setDisplayNameError(undefined);
      }
      setSaving(true);
      var knownServer = yield (0, _$$_REQUIRE(_dependencyMap[15]).getServerByDisplayName)(displayName);
      if (knownServer && knownServer.lastActiveAt > 0 && knownServer.url !== server.url) {
        setButtonDisabled(true);
        setDisplayNameError(formatMessage({
          id: 'mobile.server_name.exists',
          defaultMessage: 'You are using this name for another server.'
        }));
        setSaving(false);
        return;
      }
      yield _manager.default.updateServerDisplayName(server.url, displayName);
      (0, _$$_REQUIRE(_dependencyMap[14]).dismissModal)({
        componentId: componentId
      });
    }), [!buttonDisabled && displayName, !buttonDisabled && displayNameError]);
    var handleDisplayNameTextChanged = (0, _react.useCallback)(function (text) {
      setDisplayName(text);
      setDisplayNameError(undefined);
    }, []);
    (0, _navigation_button_pressed.default)(closeButtonId || '', componentId, close, []);
    (0, _android_back_handler.default)(componentId, close);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.flex,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_background.default, {
        theme: theme
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).SafeAreaView, {
        style: styles.flex,
        testID: "edit_server.screen",
        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[17]).KeyboardAwareScrollView, {
          bounces: false,
          contentContainerStyle: styles.scrollContainer,
          enableAutomaticScroll: _reactNative.Platform.OS === 'android',
          enableOnAndroid: false,
          enableResetScrollToCoords: true,
          extraScrollHeight: 20,
          keyboardDismissMode: "on-drag",
          keyboardShouldPersistTaps: "handled",
          ref: keyboardAwareRef,
          scrollToOverflowEnabled: true,
          style: styles.flex,
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_header.default, {
            theme: theme
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_form.default, {
            buttonDisabled: buttonDisabled,
            connecting: saving,
            displayName: displayName,
            displayNameError: displayNameError,
            handleUpdate: handleUpdate,
            handleDisplayNameTextChanged: handleDisplayNameTextChanged,
            keyboardAwareRef: keyboardAwareRef,
            serverUrl: server.url,
            theme: theme
          })]
        })
      }, 'server_content')]
    });
  };
  var _default = exports.default = EditServer;
