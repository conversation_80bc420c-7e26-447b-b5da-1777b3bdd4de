  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _floating_text_input_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      formContainer: {
        alignItems: 'center',
        maxWidth: 600,
        width: '100%',
        paddingHorizontal: 20
      },
      fullWidth: {
        width: '100%'
      },
      error: {
        marginBottom: 18
      },
      chooseText: Object.assign({
        alignSelf: 'flex-start',
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.64),
        marginTop: 8
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 75, 'Light')),
      connectButton: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.08),
        width: '100%',
        marginTop: 32,
        marginLeft: 20,
        marginRight: 20
      },
      loadingContainerStyle: {
        marginRight: 10,
        padding: 0,
        top: -2
      },
      loading: {
        height: 20,
        width: 20
      }
    };
  });
  var EditServerForm = function EditServerForm(_ref) {
    var buttonDisabled = _ref.buttonDisabled,
      connecting = _ref.connecting,
      _ref$displayName = _ref.displayName,
      displayName = _ref$displayName === undefined ? '' : _ref$displayName,
      displayNameError = _ref.displayNameError,
      handleUpdate = _ref.handleUpdate,
      handleDisplayNameTextChanged = _ref.handleDisplayNameTextChanged,
      keyboardAwareRef = _ref.keyboardAwareRef,
      serverUrl = _ref.serverUrl,
      theme = _ref.theme;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[9]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[10]).useIsTablet)();
    var dimensions = (0, _reactNative.useWindowDimensions)();
    var displayNameRef = (0, _react.useRef)(null);
    var styles = getStyleSheet(theme);
    var onBlur = (0, _react.useCallback)(function () {
      if (_reactNative.Platform.OS === 'ios') {
        var _displayNameRef$curre;
        var reset = !((_displayNameRef$curre = displayNameRef.current) != null && _displayNameRef$curre.isFocused());
        if (reset) {
          var _keyboardAwareRef$cur;
          (_keyboardAwareRef$cur = keyboardAwareRef.current) == null ? undefined : _keyboardAwareRef$cur.scrollToPosition(0, 0);
        }
      }
    }, []);
    var onUpdate = (0, _react.useCallback)(function () {
      _reactNative.Keyboard.dismiss();
      handleUpdate();
    }, [handleUpdate]);
    var onFocus = (0, _react.useCallback)(function () {
      // For iOS we set the position of the input instead of
      // having the KeyboardAwareScrollView figure it out by itself
      // on Android KeyboardAwareScrollView does nothing as is handled
      // by the OS
      if (_reactNative.Platform.OS === 'ios') {
        var offsetY = 160;
        if (isTablet) {
          var width = dimensions.width,
            height = dimensions.height;
          var isLandscape = width > height;
          offsetY = isLandscape ? 230 : 100;
        }
        requestAnimationFrame(function () {
          var _keyboardAwareRef$cur2;
          (_keyboardAwareRef$cur2 = keyboardAwareRef.current) == null ? undefined : _keyboardAwareRef$cur2.scrollToPosition(0, offsetY);
        });
      }
    }, [dimensions, isTablet]);
    (0, _react.useEffect)(function () {
      if (_reactNative.Platform.OS === 'ios' && isTablet) {
        var _displayNameRef$curre2;
        if ((_displayNameRef$curre2 = displayNameRef.current) != null && _displayNameRef$curre2.isFocused()) {
          onFocus();
        } else {
          var _keyboardAwareRef$cur3;
          (_keyboardAwareRef$cur3 = keyboardAwareRef.current) == null ? undefined : _keyboardAwareRef$cur3.scrollToPosition(0, 0);
        }
      }
    }, [onFocus]);
    var buttonType = buttonDisabled ? 'disabled' : 'default';
    var styleButtonText = (0, _$$_REQUIRE(_dependencyMap[11]).buttonTextStyle)(theme, 'lg', 'primary', buttonType);
    var styleButtonBackground = (0, _$$_REQUIRE(_dependencyMap[11]).buttonBackgroundStyle)(theme, 'lg', 'primary', buttonType);
    var buttonID = (0, _$$_REQUIRE(_dependencyMap[12]).t)('edit_server.save');
    var buttonText = 'Save';
    var buttonIcon;
    if (connecting) {
      buttonID = (0, _$$_REQUIRE(_dependencyMap[12]).t)('edit_server.saving');
      buttonText = 'Saving';
      buttonIcon = /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
        containerStyle: styles.loadingContainerStyle,
        color: theme.buttonColor
      });
    }
    var saveButtonTestId = buttonDisabled ? 'edit_server_form.save.button.disabled' : 'edit_server_form.save.button';
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.formContainer,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.fullWidth, displayNameError != null && displayNameError.length ? styles.error : undefined],
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_text_input_label.default, {
          autoCorrect: false,
          autoCapitalize: 'none',
          enablesReturnKeyAutomatically: true,
          error: displayNameError,
          label: formatMessage({
            id: 'mobile.components.select_server_view.displayName',
            defaultMessage: 'Display Name'
          }),
          onBlur: onBlur,
          onChangeText: handleDisplayNameTextChanged,
          onFocus: onFocus,
          onSubmitEditing: onUpdate,
          ref: displayNameRef,
          returnKeyType: "done",
          spellCheck: false,
          testID: "edit_server_form.server_display_name.input",
          theme: theme,
          value: displayName
        })
      }), !displayNameError && /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        defaultMessage: 'Server: {url}',
        id: 'edit_server.display_help',
        style: styles.chooseText,
        testID: 'edit_server_form.display_help',
        values: {
          url: (0, _$$_REQUIRE(_dependencyMap[13]).removeProtocol)((0, _$$_REQUIRE(_dependencyMap[13]).stripTrailingSlashes)(serverUrl))
        }
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[14]).Button, {
        containerStyle: styles.connectButton,
        buttonStyle: styleButtonBackground,
        disabledStyle: styleButtonBackground,
        disabled: buttonDisabled,
        onPress: onUpdate,
        testID: saveButtonTestId,
        children: [buttonIcon, /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          defaultMessage: buttonText,
          id: buttonID,
          style: styleButtonText
        })]
      })]
    });
  };
  var _default = exports.default = EditServerForm;
