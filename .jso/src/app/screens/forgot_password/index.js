  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _floating_text_input_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _background = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _inbox = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var AnimatedSafeArea = _reactNativeReanimated.default.createAnimatedComponent(_$$_REQUIRE(_dependencyMap[12]).SafeAreaView);
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[13]).makeStyleSheetFromTheme)(function (theme) {
    return {
      centered: {
        width: '100%',
        maxWidth: 600
      },
      container: {
        flex: 1,
        justifyContent: 'center',
        marginTop: _reactNative.Platform.select({
          android: 56
        })
      },
      error: {
        marginTop: 64
      },
      flex: {
        flex: 1
      },
      form: {
        marginTop: 20
      },
      header: Object.assign({
        color: theme.centerChannelColor,
        marginBottom: 12
      }, (0, _$$_REQUIRE(_dependencyMap[14]).typography)('Heading', 1000, 'SemiBold')),
      innerContainer: {
        alignItems: 'center',
        height: '100%',
        justifyContent: 'center',
        paddingHorizontal: 24
      },
      returnButton: {
        marginTop: 32
      },
      subheader: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[13]).changeOpacity)(theme.centerChannelColor, 0.6),
        marginBottom: 12
      }, (0, _$$_REQUIRE(_dependencyMap[14]).typography)('Heading', 200, 'Light')),
      successContainer: {
        alignItems: 'center',
        paddingHorizontal: 24,
        justifyContent: 'center',
        flex: 1
      },
      successText: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[13]).changeOpacity)(theme.centerChannelColor, 0.64)
      }, (0, _$$_REQUIRE(_dependencyMap[14]).typography)('Heading', 200, 'SemiBold'), {
        textAlign: 'center'
      }),
      successTitle: Object.assign({
        color: theme.centerChannelColor,
        marginBottom: 12
      }, (0, _$$_REQUIRE(_dependencyMap[14]).typography)('Heading', 1000))
    };
  });
  var _worklet_16856612993970_init_data = {
    code: "function indexTsx1(){const{Platform,withTiming,translateX}=this.__closure;const duration=Platform.OS==='android'?250:350;return{transform:[{translateX:withTiming(translateX.value,{duration:duration})}]};}"
  };
  var ForgotPassword = function ForgotPassword(_ref) {
    var componentId = _ref.componentId,
      serverUrl = _ref.serverUrl,
      theme = _ref.theme;
    var dimensions = (0, _reactNative.useWindowDimensions)();
    var translateX = (0, _reactNativeReanimated.useSharedValue)(dimensions.width);
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[15]).useIsTablet)();
    var _useState = (0, _react.useState)(''),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      email = _useState2[0],
      setEmail = _useState2[1];
    var _useState3 = (0, _react.useState)(''),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      error = _useState4[0],
      setError = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isPasswordLinkSent = _useState6[0],
      setIsPasswordLinkSent = _useState6[1];
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[16]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var keyboardAwareRef = (0, _react.useRef)(null);
    var styles = getStyleSheet(theme);
    var changeEmail = (0, _react.useCallback)(function (emailAddress) {
      setEmail(emailAddress);
      setError('');
    }, []);
    var onFocus = (0, _react.useCallback)(function () {
      if (_reactNative.Platform.OS === 'ios') {
        var offsetY = 150;
        if (isTablet) {
          var width = dimensions.width,
            height = dimensions.height;
          var isLandscape = width > height;
          offsetY = isLandscape ? 230 : 150;
        }
        requestAnimationFrame(function () {
          var _keyboardAwareRef$cur;
          (_keyboardAwareRef$cur = keyboardAwareRef.current) == null ? undefined : _keyboardAwareRef$cur.scrollToPosition(0, offsetY);
        });
      }
    }, [dimensions]);
    var onReturn = (0, _react.useCallback)(function () {
      _$$_REQUIRE(_dependencyMap[17]).Navigation.popTo(_$$_REQUIRE(_dependencyMap[18]).Screens.LOGIN);
    }, []);
    var submitResetPassword = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      _reactNative.Keyboard.dismiss();
      if (!(0, _$$_REQUIRE(_dependencyMap[19]).isEmail)(email)) {
        setError(formatMessage({
          id: 'password_send.error',
          defaultMessage: 'Please enter a valid email address.'
        }));
        return;
      }
      var _yield$sendPasswordRe = yield (0, _$$_REQUIRE(_dependencyMap[20]).sendPasswordResetEmail)(serverUrl, email),
        status = _yield$sendPasswordRe.status;
      if (status === 'OK') {
        setIsPasswordLinkSent(true);
        return;
      }
      setError(formatMessage({
        id: 'password_send.generic_error',
        defaultMessage: 'We were unable to send you a reset password link. Please contact your System Admin for assistance.'
      }));
    }), [email]);
    var getCenterContent = function getCenterContent() {
      if (isPasswordLinkSent) {
        return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.successContainer,
          testID: 'password_send.link.sent',
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_inbox.default, {}), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
            style: styles.successTitle,
            id: "password_send.link.title",
            defaultMessage: "Reset Link Sent"
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
            style: styles.successText,
            id: "password_send.link",
            defaultMessage: "If the account exists, a password reset email will be sent to:"
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.successText,
            children: email
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[21]).Button, {
            testID: "password_send.return",
            onPress: onReturn,
            buttonStyle: [styles.returnButton, (0, _$$_REQUIRE(_dependencyMap[22]).buttonBackgroundStyle)(theme, 'lg', 'primary', 'default')],
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
              id: "password_send.return",
              defaultMessage: "Return to Log In",
              style: (0, _$$_REQUIRE(_dependencyMap[22]).buttonTextStyle)(theme, 'lg', 'primary', 'default')
            })
          })]
        });
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[23]).KeyboardAwareScrollView, {
        bounces: false,
        contentContainerStyle: styles.innerContainer,
        enableAutomaticScroll: _reactNative.Platform.OS === 'android',
        enableOnAndroid: false,
        enableResetScrollToCoords: true,
        extraScrollHeight: 0,
        keyboardDismissMode: "on-drag",
        keyboardShouldPersistTaps: "handled",
        ref: keyboardAwareRef,
        scrollToOverflowEnabled: true,
        style: styles.flex,
        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.centered,
          testID: 'password_send.link.prepare',
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
            defaultMessage: "Reset Your Password",
            id: "password_send.reset",
            testID: "password_send.reset",
            style: styles.header
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
            style: styles.subheader,
            id: "password_send.description",
            defaultMessage: "To reset your password, enter the email address you used to sign up"
          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.form,
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_text_input_label.default, {
              autoCorrect: false,
              autoCapitalize: 'none',
              blurOnSubmit: true,
              disableFullscreenUI: true,
              enablesReturnKeyAutomatically: true,
              error: error,
              keyboardType: "email-address",
              label: formatMessage({
                id: 'login.email',
                defaultMessage: 'Email'
              }),
              onChangeText: changeEmail,
              onFocus: onFocus,
              onSubmitEditing: submitResetPassword,
              returnKeyType: "next",
              spellCheck: false,
              testID: "forgot.password.email",
              theme: theme,
              value: email
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[21]).Button, {
              testID: "forgot.password.button",
              buttonStyle: [styles.returnButton, (0, _$$_REQUIRE(_dependencyMap[22]).buttonBackgroundStyle)(theme, 'lg', 'primary', 'default'), error ? styles.error : undefined],
              disabledStyle: [styles.returnButton, (0, _$$_REQUIRE(_dependencyMap[22]).buttonBackgroundStyle)(theme, 'lg', 'primary', 'disabled'), error ? styles.error : undefined],
              disabled: !email,
              onPress: submitResetPassword,
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
                id: "password_send.reset",
                defaultMessage: "Reset my password",
                style: (0, _$$_REQUIRE(_dependencyMap[22]).buttonTextStyle)(theme, 'lg', 'primary', email ? 'default' : 'disabled')
              })
            })]
          })]
        })
      });
    };
    var transform = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        var duration = _reactNative.Platform.OS === 'android' ? 250 : 350;
        return {
          transform: [{
            translateX: (0, _reactNativeReanimated.withTiming)(translateX.value, {
              duration: duration
            })
          }]
        };
      };
      indexTsx1.__closure = {
        Platform: _reactNative.Platform,
        withTiming: _reactNativeReanimated.withTiming,
        translateX: translateX
      };
      indexTsx1.__workletHash = 16856612993970;
      indexTsx1.__initData = _worklet_16856612993970_init_data;
      return indexTsx1;
    }(), []);
    (0, _react.useEffect)(function () {
      var listener = {
        componentDidAppear: function componentDidAppear() {
          translateX.value = 0;
        },
        componentDidDisappear: function componentDidDisappear() {
          translateX.value = -dimensions.width;
        }
      };
      var unsubscribe = _$$_REQUIRE(_dependencyMap[17]).Navigation.events().registerComponentListener(listener, componentId);
      return function () {
        return unsubscribe.remove();
      };
    }, [dimensions]);
    (0, _react.useEffect)(function () {
      translateX.value = 0;
    }, []);
    (0, _android_back_handler.default)(componentId, onReturn);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.flex,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_background.default, {
        theme: theme
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(AnimatedSafeArea, {
        testID: "forgot.password.screen",
        style: [styles.container, transform],
        children: getCenterContent()
      })]
    });
  };
  var _default = exports.default = ForgotPassword;
