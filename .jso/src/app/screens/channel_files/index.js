  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _channel_files = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhance = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var channel = (0, _$$_REQUIRE(_dependencyMap[3]).observeChannel)(database, channelId);
    return {
      channel: channel,
      canDownloadFiles: (0, _$$_REQUIRE(_dependencyMap[4]).observeCanDownloadFiles)(database),
      publicLinkEnabled: (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'EnablePublicLink')
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhance(_channel_files.default));
