  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _file_filter = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var TEST_ID = 'channel_files';
  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[4]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingTop: 20,
        backgroundColor: theme.centerChannelBg
      },
      title: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[5]).typography)('Heading', 300, 'SemiBold')),
      badge: {
        backgroundColor: theme.buttonBg,
        borderColor: theme.centerChannelBg,
        marginTop: 2
      },
      iconsContainer: {
        alignItems: 'center',
        flexDirection: 'row',
        marginLeft: 'auto'
      }
    };
  });
  var Header = function Header(_ref) {
    var onFilterChanged = _ref.onFilterChanged,
      selectedFilter = _ref.selectedFilter,
      _ref$height = _ref.height,
      height = _ref$height === undefined ? undefined : _ref$height;
    var theme = (0, _$$_REQUIRE(_dependencyMap[6]).useTheme)();
    var styles = getStyleFromTheme(theme);
    var intl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)();
    var _useSafeAreaInsets = (0, _$$_REQUIRE(_dependencyMap[8]).useSafeAreaInsets)(),
      bottom = _useSafeAreaInsets.bottom;
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[9]).useIsTablet)();
    var hasFilters = selectedFilter !== _$$_REQUIRE(_dependencyMap[10]).FileFilters.ALL;
    var messageObject = hasFilters ? {
      id: _file_filter.FilterData[selectedFilter].id,
      defaultMessage: _file_filter.FilterData[selectedFilter].defaultMessage
    } : {
      id: (0, _$$_REQUIRE(_dependencyMap[11]).t)('screen.channel_files.header.recent_files'),
      defaultMessage: 'Recent Files'
    };
    var messagesText = intl.formatMessage(messageObject);
    var title = intl.formatMessage({
      id: 'screen.channel_files.results.filter.title',
      defaultMessage: 'Filter by file type'
    });
    var snapPoints = (0, _react.useMemo)(function () {
      return [1, (0, _$$_REQUIRE(_dependencyMap[12]).bottomSheetSnapPoint)(_file_filter.NUMBER_FILTER_ITEMS, _file_filter.FILTER_ITEM_HEIGHT, bottom) + _$$_REQUIRE(_dependencyMap[13]).TITLE_HEIGHT + _file_filter.DIVIDERS_HEIGHT + (isTablet ? _$$_REQUIRE(_dependencyMap[13]).TITLE_SEPARATOR_MARGIN_TABLET : _$$_REQUIRE(_dependencyMap[13]).TITLE_SEPARATOR_MARGIN)];
    }, []);
    var handleFilterPress = (0, _react.useCallback)(function () {
      var renderContent = function renderContent() {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_file_filter.default, {
          initialFilter: selectedFilter,
          setFilter: onFilterChanged,
          title: title
        });
      };
      (0, _$$_REQUIRE(_dependencyMap[14]).bottomSheet)({
        closeButtonId: 'close-search-filters',
        renderContent: renderContent,
        snapPoints: snapPoints,
        theme: theme,
        title: title
      });
    }, [onFilterChanged, selectedFilter]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Pressable, {
      onPress: handleFilterPress
      //style={styles.container}
      ,
      style: {
        borderColor: (0, _$$_REQUIRE(_dependencyMap[4]).changeOpacity)(theme.centerChannelColor, 0.10),
        borderWidth: 1,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        //paddingHorizontal: 12,
        paddingVertical: height ? undefined : 12,
        height: height ? height : undefined,
        width: 40,
        marginStart: 5
      },
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[15]).FunnelIcon, {
        color: (0, _$$_REQUIRE(_dependencyMap[4]).changeOpacity)(theme.centerChannelColor, 0.52),
        size: 24,
        style: {
          height: 15,
          width: 15
        }
      })
    });
  };
  var _default = exports.default = Header;
