  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _file_results = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _search2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _header = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var TEST_ID = 'channel_files';
  var edges = ['bottom', 'left', 'right'];
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    },
    empty: {
      alignItems: 'center',
      flex: 1,
      justifyContent: 'center'
    },
    list: {
      paddingVertical: 8
    },
    loading: {
      justifyContent: 'center',
      flex: 1
    },
    searchBar: {
      marginLeft: 20,
      marginRight: _reactNative.Platform.select({
        ios: 12,
        default: 20
      }),
      marginTop: 20
    },
    noPaddingTop: {
      paddingTop: 0
    }
  });
  var getSearchParams = function getSearchParams(channelId, searchTerm, filterValue) {
    var term = `channel:${channelId}`;
    var fileExtensions = (0, _$$_REQUIRE(_dependencyMap[11]).filterFileExtensions)(filterValue);
    var extensionTerms = fileExtensions ? ' ' + fileExtensions : '';
    var searchTerms = searchTerm ? ' ' + searchTerm : '';
    return {
      terms: term + searchTerms + extensionTerms,
      is_or_search: true
    };
  };
  var emptyFileResults = [];
  function ChannelFiles(_ref) {
    var channel = _ref.channel,
      componentId = _ref.componentId,
      canDownloadFiles = _ref.canDownloadFiles,
      publicLinkEnabled = _ref.publicLinkEnabled;
    var theme = (0, _$$_REQUIRE(_dependencyMap[12]).useTheme)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[13]).useServerUrl)();
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[14]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var searchTimeoutId = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(_$$_REQUIRE(_dependencyMap[11]).FileFilters.ALL),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      filter = _useState2[0],
      setFilter = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loading = _useState4[0],
      setLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(''),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      term = _useState6[0],
      setTerm = _useState6[1];
    var lastSearchRequest = (0, _react.useRef)();
    var _useState7 = (0, _react.useState)(emptyFileResults),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      fileInfos = _useState8[0],
      setFileInfos = _useState8[1];
    var close = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[15]).popTopScreen)(componentId);
    }, [componentId]);
    (0, _android_back_handler.default)(componentId, close);
    var handleSearch = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (searchTerm, ftr) {
        var t = Date.now();
        lastSearchRequest.current = t;
        var searchParams = getSearchParams(channel.id, searchTerm, ftr);
        var _yield$searchFiles = yield (0, _$$_REQUIRE(_dependencyMap[16]).searchFiles)(serverUrl, channel.teamId, searchParams, channel),
          files = _yield$searchFiles.files;
        if (lastSearchRequest.current !== t) {
          return;
        }
        setFileInfos(files != null && files.length ? files : emptyFileResults);
        setLoading(false);
      });
      return function (_x, _x2) {
        return _ref2.apply(this, arguments);
      };
    }(), [serverUrl, channel]);
    (0, _react.useEffect)(function () {
      if (searchTimeoutId.current) {
        clearTimeout(searchTimeoutId.current);
      }
      searchTimeoutId.current = setTimeout(function () {
        handleSearch(term, filter);
      }, _$$_REQUIRE(_dependencyMap[17]).General.SEARCH_TIMEOUT_MILLISECONDS);
    }, [filter, term, handleSearch]);
    var handleFilterChange = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (filterValue) {
        setLoading(true);
        setFilter(filterValue);
      });
      return function (_x3) {
        return _ref3.apply(this, arguments);
      };
    }(), []);
    var clearSearch = (0, _react.useCallback)(function () {
      setTerm('');
    }, []);
    var onTextChange = (0, _react.useCallback)(function (searchTerm) {
      if (term !== searchTerm) {
        setLoading(true);
        setTerm(searchTerm);
      }
    }, [term]);
    var fileChannels = (0, _react.useMemo)(function () {
      return [channel];
    }, [channel]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[18]).AudioProvider, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[19]).SafeAreaView, {
          edges: edges,
          style: styles.flex,
          testID: `${TEST_ID}.screen`,
          children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: [styles.searchBar, {
              flexDirection: 'row'
            }],
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: {
                width: '88%',
                alignItems: 'center',
                justifyContent: 'center'
              },
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_search2.default, {
                testID: `${TEST_ID}.search_bar`,
                placeholder: formatMessage({
                  id: 'search_bar.search',
                  defaultMessage: 'Search'
                }),
                cancelButtonTitle: formatMessage({
                  id: 'mobile.post.cancel',
                  defaultMessage: 'Cancel'
                }),
                placeholderTextColor: (0, _$$_REQUIRE(_dependencyMap[20]).changeOpacity)(theme.centerChannelColor, 0.5),
                onChangeText: onTextChange,
                onCancel: clearSearch,
                autoCapitalize: "none",
                keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[20]).getKeyboardAppearanceFromTheme)(theme),
                value: term
              })
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_header.default, {
              height: 53,
              onFilterChanged: handleFilterChange,
              selectedFilter: filter
            })]
          }), loading && /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
            color: theme.buttonBg,
            size: "large",
            containerStyle: styles.loading
          }), !loading && /*#__PURE__*/(0, _jsxRuntime.jsx)(_file_results.default, {
            canDownloadFiles: canDownloadFiles,
            fileChannels: fileChannels,
            fileInfos: fileInfos,
            paddingTop: styles.noPaddingTop,
            publicLinkEnabled: publicLinkEnabled,
            searchValue: term,
            isChannelFiles: true,
            isFilterEnabled: filter !== _$$_REQUIRE(_dependencyMap[11]).FileFilters.ALL
          })]
        })
      })
    });
  }
  var _default = exports.default = ChannelFiles;
