  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _freeze_screen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _body = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _performance_metrics_manager = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var edges = ['left', 'right'];
  var trackKeyboardForScreens = [_$$_REQUIRE(_dependencyMap[9]).Screens.THREAD];
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    }
  });
  var Favorite = function Favorite(_ref) {
    var _ref$categories = _ref.categories,
      categories = _ref$categories === undefined ? [] : _ref$categories,
      curentTeam = _ref.curentTeam;
    var postDraftRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      containerHeight = _useState2[0],
      setContainerHeight = _useState2[1];
    var intl = (0, _$$_REQUIRE(_dependencyMap[10]).useIntl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[11]).useIsTablet)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[12]).useServerUrl)();
    var onLayout = (0, _react.useCallback)(function (e) {
      setContainerHeight(e.nativeEvent.layout.height);
    }, []);
    var onChannelSwitch = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (c) {
        _performance_metrics_manager.default.startMetric('mobile_channel_switch');
        (0, _$$_REQUIRE(_dependencyMap[13]).switchToChannelById)(serverUrl, c.id);
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }(), [serverUrl]);
    var renderCategory = (0, _react.useCallback)(function (data) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_body.default, {
          category: data.item,
          isTablet: isTablet,
          locale: intl.locale,
          onChannelSwitch: onChannelSwitch
        })
      });
    }, [intl.locale, isTablet, onChannelSwitch]);
    var filterCategory = categories.filter(function (x) {
      return x.displayName === 'Favorites';
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_freeze_screen.default, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[14]).SafeAreaView, {
        style: styles.flex,
        mode: "margin",
        edges: edges,
        testID: "thread.screen",
        onLayout: onLayout,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: filterCategory
          // ref={listRef}
          ,
          renderItem: renderCategory,
          style: {
            flex: 1
          },
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false
          //  keyExtractor={extractKey}
          ,
          initialNumToRender: filterCategory.length

          // @ts-expect-error strictMode not included in the types
          ,
          strictMode: true
        })
      })
    });
  };
  var _default = exports.default = Favorite;
