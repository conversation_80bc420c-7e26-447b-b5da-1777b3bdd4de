  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _channel_bookmarks = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        backgroundColor: theme.sidebarBg,
        width: '100%',
        position: 'absolute'
      },
      content: {
        backgroundColor: theme.centerChannelBg,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12
      },
      separator: {
        height: 1,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.08)
      },
      separatorContainer: {
        backgroundColor: theme.centerChannelBg,
        zIndex: 1
      },
      padding: {
        paddingTop: 2
      },
      paddingHorizontal: {
        paddingHorizontal: 10
      }
    };
  });
  var ChannelHeaderBookmarks = function ChannelHeaderBookmarks(_ref) {
    var canAddBookmarks = _ref.canAddBookmarks,
      channelId = _ref.channelId;
    var theme = (0, _$$_REQUIRE(_dependencyMap[6]).useTheme)();
    var defaultHeight = (0, _$$_REQUIRE(_dependencyMap[7]).useDefaultHeaderHeight)();
    var styles = getStyleSheet(theme);
    var containerStyle = (0, _react.useMemo)(function () {
      return Object.assign({}, styles.content, {
        top: defaultHeight,
        zIndex: 1
      });
    }, [defaultHeight]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: containerStyle,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.content,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.paddingHorizontal,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_bookmarks.default, {
            channelId: channelId,
            showInInfo: false,
            canAddBookmarks: canAddBookmarks,
            separator: false
          })
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.separatorContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.separator
        })
      })]
    });
  };
  var _default = exports.default = ChannelHeaderBookmarks;
