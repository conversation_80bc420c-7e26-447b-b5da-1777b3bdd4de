  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _header = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[4]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var currentUserId = (0, _$$_REQUIRE(_dependencyMap[5]).observeCurrentUserId)(database);
    var teamId = (0, _$$_REQUIRE(_dependencyMap[5]).observeCurrentTeamId)(database);
    var members = (0, _$$_REQUIRE(_dependencyMap[6]).observeChannelMembers)(database, channelId);
    var channel = (0, _$$_REQUIRE(_dependencyMap[6]).observeChannel)(database, channelId);
    var channelType = channel.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(c == null ? undefined : c.type);
    }));
    var channelInfo = (0, _$$_REQUIRE(_dependencyMap[6]).observeChannelInfo)(database, channelId);
    var dmUser = currentUserId.pipe((0, _$$_REQUIRE(_dependencyMap[7]).combineLatestWith)(channel), (0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        userId = _ref3[0],
        c = _ref3[1];
      if ((c == null ? undefined : c.type) === _$$_REQUIRE(_dependencyMap[9]).General.DM_CHANNEL) {
        var teammateId = (0, _$$_REQUIRE(_dependencyMap[10]).getUserIdFromChannelName)(userId, c.name);
        return (0, _$$_REQUIRE(_dependencyMap[11]).observeUser)(database, teammateId);
      }
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(undefined);
    }));
    var isOwnDirectMessage = currentUserId.pipe((0, _$$_REQUIRE(_dependencyMap[7]).combineLatestWith)(dmUser), (0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref4) {
      var _ref5 = (0, _slicedToArray2.default)(_ref4, 2),
        userId = _ref5[0],
        dm = _ref5[1];
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(userId === (dm == null ? undefined : dm.id));
    }));
    var customStatus = dmUser.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (dm) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)((0, _$$_REQUIRE(_dependencyMap[10]).getUserCustomStatus)(dm));
    }));
    var isCustomStatusExpired = dmUser.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (dm) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)((0, _$$_REQUIRE(_dependencyMap[10]).isCustomStatusExpired)(dm));
    }));
    var isCustomStatusEnabled = (0, _$$_REQUIRE(_dependencyMap[5]).observeConfigBooleanValue)(database, 'EnableCustomUserStatuses');
    var searchTerm = channel.pipe((0, _$$_REQUIRE(_dependencyMap[7]).combineLatestWith)(dmUser), (0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref6) {
      var _ref7 = (0, _slicedToArray2.default)(_ref6, 2),
        c = _ref7[0],
        dm = _ref7[1];
      if ((c == null ? undefined : c.type) === _$$_REQUIRE(_dependencyMap[9]).General.DM_CHANNEL) {
        return (0, _$$_REQUIRE(_dependencyMap[8]).of)(dm ? `@${dm.username}` : '');
      } else if ((c == null ? undefined : c.type) === _$$_REQUIRE(_dependencyMap[9]).General.GM_CHANNEL) {
        return (0, _$$_REQUIRE(_dependencyMap[8]).of)(`@${c.name}`);
      }
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(c == null ? undefined : c.name);
    }));
    var displayName = channel.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(c == null ? undefined : c.displayName);
    }));
    var memberCount = channelInfo.pipe((0, _$$_REQUIRE(_dependencyMap[7]).combineLatestWith)(dmUser), (0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref8) {
      var _ref9 = (0, _slicedToArray2.default)(_ref8, 2),
        ci = _ref9[0],
        dm = _ref9[1];
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(dm ? undefined : ci == null ? undefined : ci.memberCount);
    }));
    var hasBookmarks = (0, _$$_REQUIRE(_dependencyMap[12]).queryBookmarks)(database, channelId).observeCount(false).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (count) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(count > 0);
    }), (0, _$$_REQUIRE(_dependencyMap[7]).distinctUntilChanged)());
    var isBookmarksEnabled = (0, _$$_REQUIRE(_dependencyMap[5]).observeConfigBooleanValue)(database, 'FeatureFlagChannelBookmarks');
    var canAddBookmarks = (0, _$$_REQUIRE(_dependencyMap[12]).observeCanAddBookmarks)(database, channelId);
    var currentUser = dmUser;
    return {
      channel: channel,
      currentUserId: currentUserId,
      members: members,
      canAddBookmarks: canAddBookmarks,
      channelType: channelType,
      customStatus: customStatus,
      displayName: displayName,
      hasBookmarks: hasBookmarks,
      isBookmarksEnabled: isBookmarksEnabled,
      isCustomStatusEnabled: isCustomStatusEnabled,
      isCustomStatusExpired: isCustomStatusExpired,
      isOwnDirectMessage: isOwnDirectMessage,
      memberCount: memberCount,
      searchTerm: searchTerm,
      teamId: teamId,
      currentUser: currentUser
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[4]).withDatabase)(enhanced(_react2.default.memo(_header.default)));
