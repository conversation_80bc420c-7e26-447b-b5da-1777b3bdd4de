  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.SEPARATOR_HEIGHT = exports.MARGIN = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _channel_actions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _info_box = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _leave_channel_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var SEPARATOR_HEIGHT = exports.SEPARATOR_HEIGHT = 17;
  var MARGIN = exports.MARGIN = 8;
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        //  flex: 1,
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
        // opacity: 0.80,
        // zIndex:-1,
        // backgroundColor: 'rgba(0,0,0,0.5)'
      },
      line: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.08),
        height: 1,
        marginVertical: MARGIN
      },
      wrapper: {
        flex: 1,
        marginBottom: MARGIN
      },
      separator: {
        width: MARGIN
      }
    };
  });
  var ChannelQuickAction = function ChannelQuickAction(_ref) {
    var channelId = _ref.channelId,
      callsEnabled = _ref.callsEnabled,
      isDMorGM = _ref.isDMorGM;
    var theme = (0, _$$_REQUIRE(_dependencyMap[8]).useTheme)();
    var styles = getStyleSheet(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.wrapper,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_actions.default, {
          channelId: channelId,
          dismissChannelInfo: _$$_REQUIRE(_dependencyMap[9]).dismissBottomSheet,
          callsEnabled: callsEnabled,
          testID: "channel.quick_actions"
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: {
          display: "flex",
          flexDirection: "row",
          //alignItems: "center",
          justifyContent: "flex-end",
          marginBottom: 20
        },
        children: [isDMorGM && /*#__PURE__*/(0, _jsxRuntime.jsx)(_leave_channel_label.default, {
          channelId: channelId,
          testID: "channel.quick_actions.leave_channel.action"
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: {
            flex: 1
          }
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_info_box.default, {
          isDMorGM: isDMorGM,
          channelId: channelId,
          showAsLabel: true,
          testID: "channel.quick_actions.channel_info.action"
        })]
      })]
    });
  };
  /*callsEnabled && !isDMorGM && // if calls is not enabled, copy link will show in the channel actions
                <CopyChannelLinkOption
                    channelId={channelId}
                    showAsLabel={true}
                />
            */
  var _default = exports.default = ChannelQuickAction;
