  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _custom_status_emoji = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _navigation_header = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _other_mentions_badge = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _rounded_header_context = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _bookmarks = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _PostController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[11]).makeStyleSheetFromTheme)(function (theme) {
    return {
      customStatusContainer: {
        flexDirection: 'row',
        height: 15,
        left: _reactNative.Platform.select({
          ios: undefined,
          default: -2
        }),
        marginTop: _reactNative.Platform.select({
          ios: undefined,
          default: 1
        })
      },
      customStatusEmoji: {
        marginRight: 5,
        marginTop: _reactNative.Platform.select({
          ios: undefined,
          default: -2
        })
      },
      customStatusText: {
        alignItems: 'center',
        height: 15
      },
      subtitle: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[11]).changeOpacity)(theme.sidebarHeaderTextColor, 0.72)
      }, (0, _$$_REQUIRE(_dependencyMap[12]).typography)('Heading', 75), {
        lineHeight: 12,
        marginBottom: 8,
        marginTop: 2,
        height: 13
      })
    };
  });
  var ChannelHeader = function ChannelHeader(_ref) {
    var _ref$channel = _ref.channel,
      channel = _ref$channel === undefined ? null : _ref$channel,
      canAddBookmarks = _ref.canAddBookmarks,
      channelId = _ref.channelId,
      channelType = _ref.channelType,
      componentId = _ref.componentId,
      customStatus = _ref.customStatus,
      displayName = _ref.displayName,
      hasBookmarks = _ref.hasBookmarks,
      isBookmarksEnabled = _ref.isBookmarksEnabled,
      isCustomStatusEnabled = _ref.isCustomStatusEnabled,
      isCustomStatusExpired = _ref.isCustomStatusExpired,
      isOwnDirectMessage = _ref.isOwnDirectMessage,
      memberCount = _ref.memberCount,
      searchTerm = _ref.searchTerm,
      teamId = _ref.teamId,
      callsEnabledInChannel = _ref.callsEnabledInChannel,
      groupCallsAllowed = _ref.groupCallsAllowed,
      isTabletView = _ref.isTabletView,
      shouldRenderBookmarks = _ref.shouldRenderBookmarks,
      _ref$currentUser = _ref.currentUser,
      currentUser = _ref$currentUser === undefined ? undefined : _ref$currentUser;
    var windowHeight = _reactNative.Dimensions.get('window').height;
    var intl = (0, _$$_REQUIRE(_dependencyMap[13]).useIntl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[14]).useIsTablet)();
    var _useSafeAreaInsets = (0, _$$_REQUIRE(_dependencyMap[15]).useSafeAreaInsets)(),
      bottom = _useSafeAreaInsets.bottom;
    var theme = (0, _$$_REQUIRE(_dependencyMap[16]).useTheme)();
    var styles = getStyleSheet(theme);
    var defaultHeight = (0, _$$_REQUIRE(_dependencyMap[17]).useDefaultHeaderHeight)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[18]).useServerUrl)();
    var _usePostStore = (0, _PostController.default)(),
      editPost = _usePostStore.editPost;
    var callsConfig = (0, _$$_REQUIRE(_dependencyMap[19]).getCallsConfig)(serverUrl);
    var windowWidth = _reactNative.Dimensions.get('window').width;
    var _AudioPlayController = (0, _$$_REQUIRE(_dependencyMap[20]).AudioPlayController)(),
      playingState = _AudioPlayController.playingState,
      currentPosition = _AudioPlayController.currentPosition,
      fileDuration = _AudioPlayController.fileDuration,
      currentTrack = _AudioPlayController.currentTrack,
      playAudio = _AudioPlayController.playAudio,
      changeCurrentSpeed = _AudioPlayController.changeCurrentSpeed,
      speedType = _AudioPlayController.speedType,
      seekToPosition = _AudioPlayController.seekToPosition;
    (0, _react.useEffect)(function () {
      console.log('this shown the current position', currentPosition);
    }, [currentPosition]);
    (0, _react.useEffect)(function () {
      console.log('this shown the current state', playingState.toString());
    }, [playingState]);

    // NOTE: callsEnabledInChannel will be true/false (not undefined) based on explicit state + the DefaultEnabled system setting
    //   which ultimately comes from channel/index.tsx, and observeIsCallsEnabledInChannel
    var callsAvailable = callsConfig.pluginEnabled && callsEnabledInChannel;
    if (!groupCallsAllowed && channelType !== _$$_REQUIRE(_dependencyMap[21]).General.DM_CHANNEL) {
      callsAvailable = false;
    }
    var isDMorGM = (0, _$$_REQUIRE(_dependencyMap[22]).isTypeDMorGM)(channelType);
    var contextStyle = (0, _react.useMemo)(function () {
      return {
        top: defaultHeight
      };
    }, [defaultHeight]);
    var leftComponent = (0, _react.useMemo)(function () {
      if (isTablet || !channelId || !teamId) {
        return undefined;
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_other_mentions_badge.default, {
        channelId: channelId
      });
    }, [isTablet, channelId, teamId]);
    var onBackPress = (0, _react.useCallback)(function () {
      console.log(`\n\n\n\n\n\n\n`);
      editPost(undefined);
      _reactNative.Keyboard.dismiss();
      (0, _$$_REQUIRE(_dependencyMap[23]).popTopScreen)(componentId);
    }, [componentId]);
    var onTitlePress = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[24]).preventDoubleTap)(function () {
      var title;
      switch (channelType) {
        case _$$_REQUIRE(_dependencyMap[21]).General.DM_CHANNEL:
          title = intl.formatMessage({
            id: 'screens.channel_info.dm',
            defaultMessage: 'Direct message info'
          });
          break;
        case _$$_REQUIRE(_dependencyMap[21]).General.GM_CHANNEL:
          title = intl.formatMessage({
            id: 'screens.channel_info.gm',
            defaultMessage: 'Group message info'
          });
          break;
        default:
          title = intl.formatMessage({
            id: 'screens.channel_info',
            defaultMessage: 'Channel info'
          });
          break;
      }
      var closeButton = _compass_icon.default.getImageSourceSync('close', 24, theme.sidebarHeaderTextColor);
      var closeButtonId = 'close-channel-info';
      var options = {
        topBar: {
          leftButtons: [{
            id: closeButtonId,
            icon: closeButton,
            testID: 'close.channel_info.button'
          }]
        },
        layout: {
          componentBackgroundColor: theme.centerChannelBg
        },
        statusBar: {
          visible: true,
          backgroundColor: theme.sidebarBg
        }
      };
      (0, _$$_REQUIRE(_dependencyMap[23]).showModal)(_$$_REQUIRE(_dependencyMap[21]).Screens.CHANNEL_INFO, title, {
        channelId: channelId,
        closeButtonId: closeButtonId
      }, options);
    }), [channelId, channelType, intl, theme]);

    /* const onChannelQuickAction = useCallback(() => {
         if (isTablet) {
             onTitlePress();
             return;
         }
           // When calls is enabled, we need space to move the "Copy Link" from a button to an option
         const items = callsAvailable && !isDMorGM ? 3 : 2;
         const height = CHANNEL_ACTIONS_OPTIONS_HEIGHT + SEPARATOR_HEIGHT + MARGIN + (items * ITEM_HEIGHT);
           const renderContent = () => {
             return (
                 <QuickActions
                     channelId={channelId}
                     callsEnabled={callsAvailable}
                     isDMorGM={isDMorGM}
                 />
             );
         };
           bottomSheet({
             title: '',
             renderContent,
             snapPoints: [1, bottomSheetSnapPoint(1, height, windowHeight)],
             theme,
             closeButtonId: 'close-channel-quick-actions',
         });
     }, [bottom, channelId, isDMorGM, isTablet, onTitlePress, theme, callsAvailable]);
    */

    var rightButtons = (0, _react.useMemo)(function () {
      return [

        // {
        //     iconName: 'magnify',
        //     onPress: () => {
        //         DeviceEventEmitter.emit(Navigation.NAVIGATE_TO_TAB, {screen: 'Search', params: {searchTerm: `in: ${searchTerm}`}});
        //         if (!isTablet) {
        //             popTopScreen(componentId);
        //         }
        //     },
        // },
        //{
        //    iconName: Platform.select({android: 'dots-vertical', default: 'dots-horizontal'}),
        //   onPress: onChannelQuickAction,
        //    buttonType: 'opacity',
        //     testID: 'channel_header.channel_quick_actions.button',
        // }
      ];
    }, [isTablet, searchTerm]);
    var title = displayName;
    if (isOwnDirectMessage) {
      title = intl.formatMessage({
        id: 'channel_header.directchannel.you',
        defaultMessage: '{displayName} (you)'
      }, {
        displayName: displayName
      });
    }
    var subtitle;
    if (memberCount) {
      subtitle = intl.formatMessage({
        id: 'channel_header.member_count',
        defaultMessage: '{count, plural, one {# member} other {# members}}'
      }, {
        count: memberCount
      });
    } else if (!customStatus || !customStatus.text || isCustomStatusExpired) {
      subtitle = intl.formatMessage({
        id: 'channel_header.info',
        defaultMessage: 'View info'
      });
    }
    var subtitleCompanion = (0, _react.useMemo)(function () {
      if (memberCount || !customStatus || !customStatus.text || isCustomStatusExpired) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          color: (0, _$$_REQUIRE(_dependencyMap[11]).changeOpacity)(theme.sidebarHeaderTextColor, 0.72),
          name: "chevron-right",
          size: 14
        });
      } else if (customStatus && customStatus.text) {
        return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.customStatusContainer,
          children: [isCustomStatusEnabled && Boolean(customStatus.emoji) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_emoji.default, {
            customStatus: customStatus,
            emojiSize: 13,
            style: styles.customStatusEmoji
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.customStatusText,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
              numberOfLines: 1,
              ellipsizeMode: "tail",
              style: styles.subtitle,
              testID: "channel_header.custom_status.custom_status_text",
              children: customStatus.text
            })
          })]
        });
      }
      return undefined;
    }, [memberCount, customStatus, isCustomStatusExpired]);
    var audioPlayIcon = (0, _react.useMemo)(function () {
      if (playingState === _$$_REQUIRE(_dependencyMap[20]).enAudioState.playing) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[25]).PauseIcon, {
          size: 25,
          color: "#808887"
        });
      } else {
        ;
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[25]).PlayIcon, {
          size: 25,
          color: "#808887"
        });
      }
      ;
    }, [playingState]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_navigation_header.default, {
        isLargeTitle: false,
        leftComponent: leftComponent,
        onBackPress: onBackPress,
        onTitlePress: onTitlePress,
        rightButtons: rightButtons,
        showBackButton: !isTablet || !isTabletView,
        subtitle: subtitle,
        subtitleCompanion: subtitleCompanion,
        title: title,
        channel: channel,
        onFilterChanged: function onFilterChanged() {},
        currentUser: currentUser
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: contextStyle,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_rounded_header_context.default, {})
      }), isBookmarksEnabled && hasBookmarks && shouldRenderBookmarks && /*#__PURE__*/(0, _jsxRuntime.jsx)(_bookmarks.default, {
        canAddBookmarks: canAddBookmarks,
        channelId: channelId
      })]
    });
  };
  var _default = exports.default = ChannelHeader;
