  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _intro = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[4]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var channel = (0, _$$_REQUIRE(_dependencyMap[5]).observeChannel)(database, channelId);
    var myChannelRoles = (0, _$$_REQUIRE(_dependencyMap[5]).observeMyChannelRoles)(database, channelId);
    var meRoles = (0, _$$_REQUIRE(_dependencyMap[6]).observeCurrentUserRoles)(database);
    var roles = (0, _$$_REQUIRE(_dependencyMap[7]).combineLatest)([meRoles, myChannelRoles]).pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        user = _ref3[0],
        member = _ref3[1];
      var userRoles = user == null ? undefined : user.split(' ');
      var memberRoles = member == null ? undefined : member.split(' ');
      var combinedRoles = [];
      if (userRoles) {
        combinedRoles.push.apply(combinedRoles, (0, _toConsumableArray2.default)(userRoles));
      }
      if (memberRoles) {
        combinedRoles.push.apply(combinedRoles, (0, _toConsumableArray2.default)(memberRoles));
      }
      return (0, _$$_REQUIRE(_dependencyMap[9]).queryRolesByNames)(database, combinedRoles).observeWithColumns(['permissions']);
    }));
    return {
      channel: channel,
      roles: roles
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[4]).withDatabase)(enhanced(_intro.default));
