  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _add_members_box = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _favorite_box = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _info_box = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _set_header_box = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    container: {
      justifyContent: 'center',
      flexDirection: 'row',
      marginBottom: 8,
      marginTop: 28,
      width: '100%'
    },
    margin: {
      marginRight: 8
    },
    item: {
      alignItems: 'center',
      borderRadius: 4,
      height: 70,
      justifyContent: 'center',
      maxHeight: undefined,
      paddingHorizontal: 16,
      paddingVertical: 12,
      width: 112
    }
  });
  var IntroOptions = function IntroOptions(_ref) {
    var channelId = _ref.channelId,
      header = _ref.header,
      favorite = _ref.favorite,
      canAddMembers = _ref.canAddMembers;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [canAddMembers && /*#__PURE__*/(0, _jsxRuntime.jsx)(_add_members_box.default, {
        channelId: channelId,
        containerStyle: [styles.item, styles.margin],
        testID: "channel_post_list.intro_options.add_members.action",
        inModal: false
      }), header && /*#__PURE__*/(0, _jsxRuntime.jsx)(_set_header_box.default, {
        channelId: channelId,
        containerStyle: [styles.item, styles.margin],
        testID: "channel_post_list.intro_options.set_header.action"
      }), favorite && /*#__PURE__*/(0, _jsxRuntime.jsx)(_favorite_box.default, {
        channelId: channelId,
        containerStyle: [styles.item, styles.margin],
        testID: "channel_post_list.intro_options"
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_info_box.default, {
        channelId: channelId,
        containerStyle: styles.item,
        testID: "channel_post_list.intro_options.channel_info.action"
      })]
    });
  };
  var _default = exports.default = IntroOptions;
