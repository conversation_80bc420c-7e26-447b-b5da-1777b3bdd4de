  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[4]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        // alignItems: 'center',
        flexDirection: 'row',
        marginBottom: 12,
        backgroundColor: 'red'
      },
      profile: {
        borderColor: theme.centerChannelBg,
        borderRadius: 36,
        borderWidth: 2,
        height: 72,
        width: 72
      }
    };
  });
  var Group = function Group(_ref) {
    var theme = _ref.theme,
      users = _ref.users;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[5]).useServerUrl)();
    var styles = getStyleSheet(theme);
    var rows = (0, _$$_REQUIRE(_dependencyMap[6]).chunk)(users, 5);
    var groups = rows.map(function (c, k) {
      var group = c.map(function (u, i) {
        var pictureUrl = (0, _$$_REQUIRE(_dependencyMap[7]).buildProfileImageUrlFromUser)(serverUrl, u);
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[8]).Image, {
          style: [styles.profile
          //  { transform: [{ translateX: -(i * 24) }] }
          ],
          source: {
            uri: (0, _$$_REQUIRE(_dependencyMap[9]).buildAbsoluteUrl)(serverUrl, pictureUrl)
          }
        }, pictureUrl + i.toString());
      });
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [
        // styles.container,
        {
          flexDirection: 'row',
          justifyContent: 'center',
          alignContent: 'center'
          // width:'100%',
          // backgroundColor: 'green',
          // right:50
        }
        // { left: (c.length - 1) * 12 }
        ],
        children: group
      }, 'group_avatar' + k.toString());
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: {
          // display: 'flex', 
          // overflow: 'hidden', 
          flexWrap: 'wrap'

          //  alignItems: 'center' 
        },
        children: groups
      })
    });
  };
  var _default = exports.default = Group;
