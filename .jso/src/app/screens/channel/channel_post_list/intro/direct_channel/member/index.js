  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _member = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)([], function (_ref) {
    var member = _ref.member,
      database = _ref.database;
    return {
      user: (0, _$$_REQUIRE(_dependencyMap[3]).observeUser)(database, member.userId)
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_member.default));
