  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _profile_picture = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    profile: {
      height: 67,
      marginBottom: 12,
      marginRight: 12
    }
  });
  var Member = function Member(_ref) {
    var channelId = _ref.channelId,
      containerStyle = _ref.containerStyle,
      _ref$size = _ref.size,
      size = _ref$size === undefined ? 72 : _ref$size,
      _ref$showStatus = _ref.showStatus,
      showStatus = _ref$showStatus === undefined ? true : _ref$showStatus,
      theme = _ref.theme,
      user = _ref.user;
    var intl = (0, _$$_REQUIRE(_dependencyMap[6]).useIntl)();
    var onPress = (0, _react.useCallback)(function () {
      var screen = _$$_REQUIRE(_dependencyMap[7]).Screens.USER_PROFILE;
      var title = intl.formatMessage({
        id: 'mobile.routes.user_profile',
        defaultMessage: 'Profile'
      });
      var closeButtonId = 'close-user-profile';
      var props = {
        closeButtonId: closeButtonId,
        userId: user.id,
        channelId: channelId,
        location: _$$_REQUIRE(_dependencyMap[7]).Screens.CHANNEL
      };
      _reactNative.Keyboard.dismiss();
      (0, _$$_REQUIRE(_dependencyMap[8]).openAsBottomSheet)({
        screen: screen,
        title: title,
        theme: theme,
        closeButtonId: closeButtonId,
        props: props
      });
    }, [theme, intl.locale]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
      onPress: onPress
      // style={[styles.profile, containerStyle]}
      ,
      type: "opacity",
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_profile_picture.default, {
        author: user,
        size: size,
        iconSize: 48,
        showStatus: showStatus,
        statusSize: 24,
        testID: `channel_intro.${user.id}.profile_picture`
      })
    });
  };
  var _default = exports.default = Member;
