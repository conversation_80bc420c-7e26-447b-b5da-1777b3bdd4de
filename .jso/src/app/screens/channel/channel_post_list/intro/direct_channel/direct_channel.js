  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _group = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _member = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[8]).makeStyleSheetFromTheme)(function (theme) {
    return {
      botContainer: {
        alignSelf: 'flex-end',
        bottom: 7.5,
        height: 20,
        marginBottom: 0,
        marginLeft: 4,
        paddingVertical: 0
      },
      botText: {
        fontSize: 14,
        lineHeight: 20
      },
      container: {
        alignItems: 'center',
        marginHorizontal: 20
      },
      message: Object.assign({
        color: theme.centerChannelColor,
        marginTop: 8,
        textAlign: 'center'
      }, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 200, 'Regular')),
      boldText: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 200, 'SemiBold')),
      profilesContainer: {
        justifyContent: 'center',
        alignItems: 'center'
      },
      title: Object.assign({
        color: theme.centerChannelColor,
        marginTop: 4,
        textAlign: 'center'
      }, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 700, 'SemiBold')),
      titleGroup: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 600, 'SemiBold'))
    };
  });
  var gmIntroMessages = (0, _$$_REQUIRE(_dependencyMap[10]).defineMessages)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({
    muted: {
      id: 'intro.group_message.muted',
      defaultMessage: 'This group message is currently <b>muted</b>, so you will not be notified.'
    }
  }, _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.ALL, {
    id: 'intro.group_message.all',
    defaultMessage: 'You\'ll be notified <b>for all activity</b> in this group message.'
  }), _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.DEFAULT, {
    id: 'intro.group_message.all',
    defaultMessage: 'You\'ll be notified <b>for all activity</b> in this group message.'
  }), _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.MENTION, {
    id: 'intro.group_message.mention',
    defaultMessage: 'You have selected to be notified <b>only when mentioned</b> in this group message.'
  }), _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.NONE, {
    id: 'intro.group_message.none',
    defaultMessage: 'You have selected to <b>never</b> be notified in this group message.'
  }));
  var getGMIntroMessageSpecificPart = function getGMIntroMessageSpecificPart(userNotifyProps, channelNotifyProps, boldStyle) {
    var isMuted = (channelNotifyProps == null ? undefined : channelNotifyProps.mark_unread) === 'mention';
    if (isMuted) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, Object.assign({}, gmIntroMessages.muted, {
        values: {
          b: function b(chunk) {
            return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: boldStyle,
              children: chunk
            });
          }
        }
      }));
    }
    var channelNotifyProp = (channelNotifyProps == null ? undefined : channelNotifyProps.push) || _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.DEFAULT;
    var userNotifyProp = (userNotifyProps == null ? undefined : userNotifyProps.push) || _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.MENTION;
    var notifyLevelToUse = channelNotifyProp;
    if (notifyLevelToUse === _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.DEFAULT) {
      notifyLevelToUse = userNotifyProp;
    }
    if (channelNotifyProp === _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.DEFAULT && userNotifyProp === _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.MENTION) {
      notifyLevelToUse = _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.ALL;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, Object.assign({}, gmIntroMessages[notifyLevelToUse], {
      values: {
        b: function b(chunk) {
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: boldStyle,
            children: chunk
          });
        }
      }
    }));
  };
  var DirectChannel = function DirectChannel(_ref) {
    var channel = _ref.channel,
      isBot = _ref.isBot,
      currentUserId = _ref.currentUserId,
      members = _ref.members,
      theme = _ref.theme,
      hasGMasDMFeature = _ref.hasGMasDMFeature,
      channelNotifyProps = _ref.channelNotifyProps,
      userNotifyProps = _ref.userNotifyProps,
      _ref$isOnHeader = _ref.isOnHeader,
      isOnHeader = _ref$isOnHeader === undefined ? false : _ref$isOnHeader;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[12]).useServerUrl)();
    var styles = getStyleSheet(theme);
    (0, _react.useEffect)(function () {
      var channelMembers = members == null ? undefined : members.filter(function (m) {
        return m.userId !== currentUserId;
      });
      if (!(channelMembers != null && channelMembers.length)) {
        (0, _$$_REQUIRE(_dependencyMap[13]).fetchProfilesInChannel)(serverUrl, channel.id, currentUserId, undefined, false);
      }
    }, []);
    var message = (0, _react.useMemo)(function () {
      if (channel.type === _$$_REQUIRE(_dependencyMap[11]).General.DM_CHANNEL) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          defaultMessage: 'This is the start of your conversation with {teammate}. Messages and files shared here are not shown to anyone else.',
          id: "intro.direct_message",
          style: styles.message,
          values: {
            teammate: channel.displayName
          }
        });
      }
      if (!hasGMasDMFeature) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          defaultMessage: 'This is the start of your conversation with this group. Messages and files shared here are not shown to anyone else outside of the group.',
          id: "intro.group_message.after_gm_as_dm",
          style: styles.message
        });
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.Text, {
        style: styles.message,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          defaultMessage: 'This is the start of your conversation with this group.',
          id: "intro.group_message.common"
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          children: " "
        }), getGMIntroMessageSpecificPart(userNotifyProps, channelNotifyProps, styles.boldText)]
      });
    }, [channel.displayName, theme, channelNotifyProps, userNotifyProps]);
    var profiles = (0, _react.useMemo)(function () {
      if (channel.type === _$$_REQUIRE(_dependencyMap[11]).General.DM_CHANNEL) {
        var teammateId = (0, _$$_REQUIRE(_dependencyMap[14]).getUserIdFromChannelName)(currentUserId, channel.name);
        var teammate = members == null ? undefined : members.find(function (m) {
          return m.userId === teammateId;
        });
        if (!teammate) {
          return null;
        }
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_member.default, {
          channelId: channel.id,
          containerStyle: {
            height: 96,
            width: 50
          },
          member: teammate,
          size: 96,
          theme: theme
        });
      }
      var channelMembers = members == null ? undefined : members.filter(function (m) {
        return m.userId !== currentUserId;
      });
      if (!(channelMembers != null && channelMembers.length)) {
        return null;
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_group.default, {
        theme: theme,
        userIds: channelMembers.map(function (cm) {
          return cm.userId;
        })
      });
    }, [members, theme]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.container],
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.profilesContainer],
        children: profiles
      }), !isOnHeader && /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: {
            flexDirection: 'row'
          },
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: [styles.title, channel.type === _$$_REQUIRE(_dependencyMap[11]).General.GM_CHANNEL ? styles.titleGroup : undefined],
            testID: "channel_post_list.intro.display_name",
            children: channel.displayName
          }), isBot && /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[15]).BotTag, {
            style: styles.botContainer,
            textStyle: styles.botText
          })]
        })
      })]
    });
  };
  var _default = exports.default = DirectChannel;
