  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _direct_channel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var observeIsBot = function observeIsBot(user) {
    return (0, _$$_REQUIRE(_dependencyMap[2]).of)(<PERSON><PERSON>an(user == null ? undefined : user.isBot));
  };
  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var channel = _ref.channel,
      database = _ref.database;
    var currentUserId = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentUserId)(database);
    var members = (0, _$$_REQUIRE(_dependencyMap[5]).observeChannelMembers)(database, channel.id);
    var hasGMasDMFeature = (0, _$$_REQUIRE(_dependencyMap[6]).observeHasGMasDMFeature)(database);
    var channelNotifyProps = (0, _$$_REQUIRE(_dependencyMap[5]).observeNotifyPropsByChannels)(database, [channel]).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (v) {
      return (0, _$$_REQUIRE(_dependencyMap[2]).of)(v[channel.id]);
    }));
    var userNotifyProps = (0, _$$_REQUIRE(_dependencyMap[8]).observeCurrentUser)(database).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (v) {
      return (0, _$$_REQUIRE(_dependencyMap[2]).of)(v == null ? undefined : v.notifyProps);
    }));
    var isBot = (0, _$$_REQUIRE(_dependencyMap[2]).of)(false);
    if (channel.type === _$$_REQUIRE(_dependencyMap[9]).General.DM_CHANNEL) {
      isBot = currentUserId.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (userId) {
        var otherUserId = (0, _$$_REQUIRE(_dependencyMap[10]).getUserIdFromChannelName)(userId, channel.name);
        return (0, _$$_REQUIRE(_dependencyMap[8]).observeUser)(database, otherUserId).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(observeIsBot));
      }));
    }
    return {
      currentUserId: currentUserId,
      isBot: isBot,
      members: members,
      hasGMasDMFeature: hasGMasDMFeature,
      channelNotifyProps: channelNotifyProps,
      userNotifyProps: userNotifyProps
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_direct_channel.default));
