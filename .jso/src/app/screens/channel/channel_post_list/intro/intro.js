  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _did_update = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _ephemeral_store = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _direct_channel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _public_or_private_channel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _townsquare = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var PADDING_TOP = _reactNative.Platform.select({
    ios: 150,
    default: 100
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginVertical: 12,
      paddingTop: PADDING_TOP,
      overflow: 'hidden'
    }
  });
  var ProfileImage = function ProfileImage(_ref) {
    var channel = _ref.channel,
      roles = _ref.roles;
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[11]).useServerUrl)();
    var _useState = (0, _react.useState)(_ephemeral_store.default.isLoadingMessagesForChannel(serverUrl, (channel == null ? undefined : channel.id) || '')),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      fetching = _useState2[0],
      setFetching = _useState2[1];
    var element = (0, _react.useMemo)(function () {
      if (!channel) {
        return null;
      }
      if (channel.type === _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL && channel.name === _$$_REQUIRE(_dependencyMap[12]).General.DEFAULT_CHANNEL) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_townsquare.default, {
          channelId: channel.id,
          displayName: channel.displayName,
          roles: roles,
          theme: theme
        });
      }
      switch (channel.type) {
        case _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL:
        case _$$_REQUIRE(_dependencyMap[12]).General.PRIVATE_CHANNEL:
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_public_or_private_channel.default, {
            channel: channel,
            roles: roles,
            theme: theme
          });
        default:
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_direct_channel.default, {
            channel: channel,
            theme: theme
          });
      }
    }, [channel, roles, theme]);
    (0, _react.useEffect)(function () {
      var listener = _reactNative.DeviceEventEmitter.addListener(_$$_REQUIRE(_dependencyMap[12]).Events.LOADING_CHANNEL_POSTS, function (_ref2) {
        var eventServerUrl = _ref2.serverUrl,
          eventChannelId = _ref2.channelId,
          value = _ref2.value;
        if (eventServerUrl === serverUrl && eventChannelId === (channel == null ? undefined : channel.id)) {
          setFetching(value);
        }
      });
      return function () {
        return listener.remove();
      };
    }, [serverUrl, channel == null ? undefined : channel.id]);
    (0, _did_update.default)(function () {
      setFetching(_ephemeral_store.default.isLoadingMessagesForChannel(serverUrl, (channel == null ? undefined : channel.id) || ''));
    }, [serverUrl, channel == null ? undefined : channel.id]);
    if (fetching) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        size: "small",
        color: theme.centerChannelColor,
        style: styles.container
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: element
    });
  };
  var _default = exports.default = ProfileImage;
