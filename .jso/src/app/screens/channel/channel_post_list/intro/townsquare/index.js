  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _town_square = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _options = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        alignItems: 'center',
        marginHorizontal: 20
      },
      message: Object.assign({
        color: theme.centerChannelColor,
        marginTop: 16,
        textAlign: 'center'
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 200, 'Light'), {
        width: '100%'
      }),
      title: Object.assign({
        color: theme.centerChannelColor,
        marginTop: 16
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 700, 'SemiBold'))
    };
  });
  var TownSquare = function TownSquare(_ref) {
    var channelId = _ref.channelId,
      displayName = _ref.displayName,
      roles = _ref.roles,
      theme = _ref.theme;
    var styles = getStyleSheet(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_town_square.default, {
        theme: theme
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.title,
        testID: "channel_post_list.intro.display_name",
        children: displayName === "Off-Topic" ? "المجموعة الفرعية" : displayName === "Town Square" ? "المجموعة الرئيسية" : displayName
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        defaultMessage: "Welcome to {name}. Everyone automatically becomes a member of this channel when they join the team.",
        id: "intro.townsquare",
        style: styles.message,
        values: {
          name: displayName === "Off-Topic" ? "المجموعة الفرعية" : displayName === "Town Square" ? "المجموعة الرئيسية" : displayName
        }
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_options.default, {
        channelId: channelId,
        header: (0, _$$_REQUIRE(_dependencyMap[9]).hasPermission)(roles, _$$_REQUIRE(_dependencyMap[10]).Permissions.MANAGE_PUBLIC_CHANNEL_PROPERTIES),
        canAddMembers: false
      })]
    });
  };
  var _default = exports.default = TownSquare;
