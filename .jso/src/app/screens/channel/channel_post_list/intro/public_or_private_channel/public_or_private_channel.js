  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _private = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _public = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _options = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[8]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        alignItems: 'center',
        marginHorizontal: 20
      },
      created: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.centerChannelColor, 0.64)
      }, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 50, 'Light')),
      icon: {
        marginRight: 5
      },
      message: Object.assign({
        color: theme.centerChannelColor,
        marginTop: 16,
        textAlign: 'center'
      }, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 200, 'Light')),
      title: Object.assign({
        color: theme.centerChannelColor,
        marginTop: 8,
        marginBottom: 8,
        textAlign: 'center'
      }, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 700, 'SemiBold'))
    };
  });
  var PublicOrPrivateChannel = function PublicOrPrivateChannel(_ref) {
    var channel = _ref.channel,
      creator = _ref.creator,
      roles = _ref.roles,
      theme = _ref.theme;
    var intl = (0, _$$_REQUIRE(_dependencyMap[10]).useIntl)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[11]).useServerUrl)();
    var styles = getStyleSheet(theme);
    var illustration = (0, _react.useMemo)(function () {
      if (channel.type === _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_public.default, {
          theme: theme
        });
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_private.default, {
        theme: theme
      });
    }, [channel.type, theme]);
    (0, _react.useEffect)(function () {
      if (!creator && channel.creatorId) {
        (0, _$$_REQUIRE(_dependencyMap[13]).fetchChannelCreator)(serverUrl, channel.id);
      }
    }, []);
    var canManagePeople = (0, _react.useMemo)(function () {
      if (channel.deleteAt !== 0) {
        return false;
      }
      var permission = channel.type === _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL ? _$$_REQUIRE(_dependencyMap[12]).Permissions.MANAGE_PUBLIC_CHANNEL_MEMBERS : _$$_REQUIRE(_dependencyMap[12]).Permissions.MANAGE_PRIVATE_CHANNEL_MEMBERS;
      return (0, _$$_REQUIRE(_dependencyMap[14]).hasPermission)(roles, permission);
    }, [channel.type, roles, channel.deleteAt]);
    var canSetHeader = (0, _react.useMemo)(function () {
      if (channel.deleteAt !== 0) {
        return false;
      }
      var permission = channel.type === _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL ? _$$_REQUIRE(_dependencyMap[12]).Permissions.MANAGE_PUBLIC_CHANNEL_PROPERTIES : _$$_REQUIRE(_dependencyMap[12]).Permissions.MANAGE_PRIVATE_CHANNEL_PROPERTIES;
      return (0, _$$_REQUIRE(_dependencyMap[14]).hasPermission)(roles, permission);
    }, [channel.type, roles, channel.deleteAt]);
    var createdBy = (0, _react.useMemo)(function () {
      var id = channel.type === _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL ? (0, _$$_REQUIRE(_dependencyMap[15]).t)('intro.public_channel') : (0, _$$_REQUIRE(_dependencyMap[15]).t)('intro.private_channel');
      var defaultMessage = channel.type === _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL ? 'Public Channel' : 'Private Channel';
      var channelType = `${intl.formatMessage({
        id: id,
        defaultMessage: defaultMessage
      })} `;
      var date = intl.formatDate(channel.createAt, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      var by = intl.formatMessage({
        id: 'intro.created_by',
        defaultMessage: 'created by {creator} on {date}.'
      }, {
        creator: creator,
        date: date
      });
      return `${channelType} ${by}`;
    }, [channel.type, creator, theme]);
    var message = (0, _react.useMemo)(function () {
      var id = channel.type === _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL ? (0, _$$_REQUIRE(_dependencyMap[15]).t)('intro.welcome.public') : (0, _$$_REQUIRE(_dependencyMap[15]).t)('intro.welcome.private');
      var msg = channel.type === _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL ? 'Add some more team members to the channel or start a conversation below.' : 'Only invited members can see messages posted in this private channel.';
      var mainMessage = intl.formatMessage({
        id: 'intro.welcome',
        defaultMessage: 'Welcome to {displayName} channel.'
      }, {
        displayName: channel.displayName === "Town Square" ? "المجموعة الرئيسية" : channel.displayName === "Off-Topic" ? "المجموعة الفرعية" : channel.displayName
      });
      var suffix = intl.formatMessage({
        id: id,
        defaultMessage: msg
      });
      return `${mainMessage} ${suffix}`;
    }, [channel.displayName, channel.type, theme]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [illustration, /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.title,
        testID: "channel_post_list.intro.display_name",
        children: channel.displayName === "Town Square" ? "المجموعة الرئيسية" : channel.displayName === "Off-Topic" ? "المجموعة الفرعية" : channel.displayName
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: {
          flexDirection: 'row'
        },
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          name: channel.type === _$$_REQUIRE(_dependencyMap[12]).General.OPEN_CHANNEL ? 'globe' : 'lock',
          size: 14.4,
          color: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.centerChannelColor, 0.64),
          style: styles.icon
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.created,
          children: createdBy
        })]
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.message,
        children: message
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_options.default, {
        channelId: channel.id,
        header: canSetHeader,
        canAddMembers: canManagePeople
      })]
    });
  };
  var _default = exports.default = PublicOrPrivateChannel;
