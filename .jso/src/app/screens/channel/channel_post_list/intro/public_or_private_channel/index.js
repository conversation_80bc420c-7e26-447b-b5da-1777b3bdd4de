  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _public_or_private_channel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var channel = _ref.channel,
      database = _ref.database;
    var creator;
    if (channel.creatorId) {
      var me = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentUser)(database);
      var profile = (0, _$$_REQUIRE(_dependencyMap[4]).observeUser)(database, channel.creatorId);
      var teammateNameDisplay = (0, _$$_REQUIRE(_dependencyMap[4]).observeTeammateNameDisplay)(database);
      creator = (0, _$$_REQUIRE(_dependencyMap[5]).combineLatest)([profile, teammateNameDisplay, me]).pipe((0, _$$_REQUIRE(_dependencyMap[6]).map)(function (_ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref2, 3),
          user = _ref3[0],
          displaySetting = _ref3[1],
          currentUser = _ref3[2];
        return user ? (0, _$$_REQUIRE(_dependencyMap[7]).displayUsername)(user, currentUser == null ? undefined : currentUser.locale, displaySetting, true) : '';
      }));
    } else {
      creator = (0, _$$_REQUIRE(_dependencyMap[5]).of)(undefined);
    }
    return {
      creator: creator
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_public_or_private_channel.default));
