  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _channel_post_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[4]).withObservables)(['channelId'], function (_ref) {
    var database = _ref.database,
      channelId = _ref.channelId;
    var isCRTEnabledObserver = (0, _$$_REQUIRE(_dependencyMap[5]).observeIsCRTEnabled)(database);
    var postsInChannelObserver = (0, _$$_REQUIRE(_dependencyMap[6]).queryPostsInChannel)(database, channelId).observeWithColumns(['earliest', 'latest']);
    return {
      isCRTEnabled: isCRTEnabledObserver,
      lastViewedAt: (0, _$$_REQUIRE(_dependencyMap[7]).observeMyChannel)(database, channelId).pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (myChannel) {
        return (0, _$$_REQUIRE(_dependencyMap[9]).of)(myChannel == null ? undefined : myChannel.viewedAt);
      }), (0, _$$_REQUIRE(_dependencyMap[8]).distinctUntilChanged)()),
      posts: (0, _$$_REQUIRE(_dependencyMap[9]).combineLatest)([isCRTEnabledObserver, postsInChannelObserver]).pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (_ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
          isCRTEnabled = _ref3[0],
          postsInChannel = _ref3[1];
        if (!postsInChannel.length) {
          return (0, _$$_REQUIRE(_dependencyMap[9]).of)([]);
        }
        var _postsInChannel$ = postsInChannel[0],
          earliest = _postsInChannel$.earliest,
          latest = _postsInChannel$.latest;
        return (0, _$$_REQUIRE(_dependencyMap[6]).queryPostsBetween)(database, earliest, latest, _$$_REQUIRE(_dependencyMap[10]).Q.desc, '', channelId, isCRTEnabled ? '' : undefined).observe();
      })),
      shouldShowJoinLeaveMessages: (0, _$$_REQUIRE(_dependencyMap[11]).queryAdvanceSettingsPreferences)(database, _$$_REQUIRE(_dependencyMap[12]).Preferences.ADVANCED_FILTER_JOIN_LEAVE).observeWithColumns(['value']).pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (preferences) {
        return (0, _$$_REQUIRE(_dependencyMap[9]).of)((0, _$$_REQUIRE(_dependencyMap[13]).getAdvanceSettingPreferenceAsBool)(preferences, _$$_REQUIRE(_dependencyMap[12]).Preferences.ADVANCED_FILTER_JOIN_LEAVE, true));
      }), (0, _$$_REQUIRE(_dependencyMap[8]).distinctUntilChanged)())
    };
  });
  var _default = exports.default = _react2.default.memo((0, _$$_REQUIRE(_dependencyMap[4]).withDatabase)(enhanced(_channel_post_list.default)));
