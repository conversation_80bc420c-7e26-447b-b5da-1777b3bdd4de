  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _post_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _did_update = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _ephemeral_store = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _intro = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var edges = ['bottom'];
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    },
    containerStyle: {
      paddingTop: 12
    }
  });
  var ChannelPostList = function ChannelPostList(_ref) {
    var channelId = _ref.channelId,
      contentContainerStyle = _ref.contentContainerStyle,
      isCRTEnabled = _ref.isCRTEnabled,
      lastViewedAt = _ref.lastViewedAt,
      nativeID = _ref.nativeID,
      posts = _ref.posts,
      shouldShowJoinLeaveMessages = _ref.shouldShowJoinLeaveMessages,
      _ref$setLastViewedFil = _ref.setLastViewedFileInfo,
      setLastViewedFileInfo = _ref$setLastViewedFil === undefined ? undefined : _ref$setLastViewedFil,
      _ref$channelType = _ref.channelType,
      channelType = _ref$channelType === undefined ? undefined : _ref$channelType;
    var appState = (0, _$$_REQUIRE(_dependencyMap[10]).useAppState)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[10]).useIsTablet)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[11]).useServerUrl)();
    var canLoadPostsBefore = (0, _react.useRef)(true);
    var canLoadPost = (0, _react.useRef)(true);
    var _useState = (0, _react.useState)(_ephemeral_store.default.isLoadingMessagesForChannel(serverUrl, channelId)),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      fetchingPosts = _useState2[0],
      setFetchingPosts = _useState2[1];
    var oldPostsCount = (0, _react.useRef)(posts.length);
    var onEndReached = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[12]).debounce)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!fetchingPosts && canLoadPostsBefore.current && posts.length) {
        var lastPost = posts[posts.length - 1];
        var result = yield (0, _$$_REQUIRE(_dependencyMap[13]).fetchPostsBefore)(serverUrl, channelId, (lastPost == null ? undefined : lastPost.id) || '');
        canLoadPostsBefore.current = false;
        if (!('error' in result)) {
          var _result$posts$length, _result$posts;
          canLoadPostsBefore.current = ((_result$posts$length = (_result$posts = result.posts) == null ? undefined : _result$posts.length) != null ? _result$posts$length : 0) > 0;
        }
      }
    }), 500), [fetchingPosts, serverUrl, channelId, posts]);
    (0, _did_update.default)(function () {
      setFetchingPosts(_ephemeral_store.default.isLoadingMessagesForChannel(serverUrl, channelId));
    }, [serverUrl, channelId]);
    (0, _react.useEffect)(function () {
      var listener = _reactNative.DeviceEventEmitter.addListener(_$$_REQUIRE(_dependencyMap[14]).Events.LOADING_CHANNEL_POSTS, function (_ref3) {
        var eventServerUrl = _ref3.serverUrl,
          eventChannelId = _ref3.channelId,
          value = _ref3.value;
        if (eventServerUrl === serverUrl && eventChannelId === channelId) {
          setFetchingPosts(value);
        }
      });
      return function () {
        return listener.remove();
      };
    }, [serverUrl, channelId]);
    (0, _react.useEffect)(function () {
      // If we have too few posts so the onEndReached may have been called while fetching
      // we call fetchPosts to make sure we have at least the latest page of posts
      if (!fetchingPosts && canLoadPost.current && posts.length < _$$_REQUIRE(_dependencyMap[15]).PER_PAGE_DEFAULT) {
        // We do this just once
        canLoadPost.current = false;
        (0, _$$_REQUIRE(_dependencyMap[13]).fetchPosts)(serverUrl, channelId);
      }
    }, [fetchingPosts, posts]);
    (0, _did_update.default)(function () {
      if (oldPostsCount.current < posts.length && appState === 'active') {
        oldPostsCount.current = posts.length;
        (0, _$$_REQUIRE(_dependencyMap[16]).markChannelAsRead)(serverUrl, channelId, true);
      }
    }, [posts.length]);
    (0, _did_update.default)(function () {
      if (appState === 'active') {
        (0, _$$_REQUIRE(_dependencyMap[16]).markChannelAsRead)(serverUrl, channelId, true);
      }
      if (appState !== 'active') {
        (0, _$$_REQUIRE(_dependencyMap[16]).unsetActiveChannelOnServer)(serverUrl);
      }
    }, [appState === 'active']);
    (0, _react.useEffect)(function () {
      return function () {
        (0, _$$_REQUIRE(_dependencyMap[16]).unsetActiveChannelOnServer)(serverUrl);
      };
    }, []);
    var intro = /*#__PURE__*/(0, _jsxRuntime.jsx)(_intro.default, {
      channelId: channelId
    });
    var postList = /*#__PURE__*/(0, _jsxRuntime.jsx)(_post_list.default, {
      setLastViewedFileInfo: setLastViewedFileInfo,
      channelId: channelId,
      contentContainerStyle: [contentContainerStyle, !isCRTEnabled && styles.containerStyle],
      isCRTEnabled: isCRTEnabled,
      footer: intro,
      lastViewedAt: lastViewedAt,
      location: _$$_REQUIRE(_dependencyMap[14]).Screens.CHANNEL,
      nativeID: nativeID,
      onEndReached: onEndReached,
      posts: posts,
      shouldShowJoinLeaveMessages: shouldShowJoinLeaveMessages,
      showMoreMessages: true,
      testID: "channel.post_list",
      channelType: channelType
    });
    if (isTablet) {
      return postList;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[17]).SafeAreaView, {
      edges: edges,
      style: styles.flex,
      children: postList
    });
  };
  var _default = exports.default = ChannelPostList;
