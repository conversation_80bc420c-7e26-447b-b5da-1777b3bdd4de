  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _floating_call_container = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _freeze_screen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _post_draft = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _ephemeral_store = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _channel_post_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _header2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _use_gm_as_dm_notice = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _toasts = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[14]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _otpController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var edges = ["left", "right"];
  var trackKeyboardForScreens = [_$$_REQUIRE(_dependencyMap[18]).Screens.HOME, _$$_REQUIRE(_dependencyMap[18]).Screens.CHANNEL];
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    }
  });
  var _worklet_6586891313714_init_data = {
    code: "function channelTsx1(){const{positioned}=this.__closure;return{transform:[{translateY:positioned.value}]};}"
  };
  var Channel = function Channel(_ref) {
    var channelId = _ref.channelId,
      componentId = _ref.componentId,
      showJoinCallBanner = _ref.showJoinCallBanner,
      isInACall = _ref.isInACall,
      isCallsEnabledInChannel = _ref.isCallsEnabledInChannel,
      groupCallsAllowed = _ref.groupCallsAllowed,
      showIncomingCalls = _ref.showIncomingCalls,
      isTabletView = _ref.isTabletView,
      dismissedGMasDMNotice = _ref.dismissedGMasDMNotice,
      channelType = _ref.channelType,
      currentUserId = _ref.currentUserId,
      hasGMasDMFeature = _ref.hasGMasDMFeature,
      includeBookmarkBar = _ref.includeBookmarkBar;
    var database = (0, _$$_REQUIRE(_dependencyMap[19]).useDatabase)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[20]).useTheme)();
    var windowWidth = _reactNative.Dimensions.get("window").width;
    var windowHeight = _reactNative.Dimensions.get("window").height;
    (0, _use_gm_as_dm_notice.default)(currentUserId, channelType, dismissedGMasDMNotice, hasGMasDMFeature);
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[21]).useIsTablet)();
    var insets = (0, _$$_REQUIRE(_dependencyMap[22]).useSafeAreaInsets)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      shouldRenderPosts = _useState2[0],
      setShouldRenderPosts = _useState2[1];
    var switchingTeam = (0, _$$_REQUIRE(_dependencyMap[23]).useTeamSwitch)();
    var switchingChannels = (0, _$$_REQUIRE(_dependencyMap[24]).useChannelSwitch)();
    var defaultHeight = (0, _$$_REQUIRE(_dependencyMap[25]).useDefaultHeaderHeight)();
    var postDraftRef = (0, _react.useRef)(null);
    var _useState3 = (0, _react.useState)(0),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      containerHeight = _useState4[0],
      setContainerHeight = _useState4[1];
    var shouldRender = !switchingTeam && !switchingChannels && shouldRenderPosts && Boolean(channelId);
    var handleBack = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[26]).popTopScreen)(componentId);
    }, [componentId]);
    (0, _$$_REQUIRE(_dependencyMap[27]).useKeyboardTrackingPaused)(postDraftRef, channelId, trackKeyboardForScreens);
    (0, _android_back_handler.default)(componentId, handleBack);
    var marginTop = defaultHeight + (isTablet ? 0 : -insets.top);
    (0, _react.useEffect)(function () {
      // This is done so that the header renders
      // and the screen does not look totally blank
      var raf = requestAnimationFrame(function () {
        setShouldRenderPosts(Boolean(channelId));
      });

      // This is done to give time to the WS event
      var t = setTimeout(function () {
        _ephemeral_store.default.removeSwitchingToChannel(channelId);
      }, 500);
      (0, _$$_REQUIRE(_dependencyMap[28]).storeLastViewedChannelIdAndServer)(channelId);
      return function () {
        cancelAnimationFrame(raf);
        clearTimeout(t);
        (0, _$$_REQUIRE(_dependencyMap[28]).removeLastViewedChannelIdAndServer)();
        _ephemeral_store.default.removeSwitchingToChannel(channelId);
      };
    }, [channelId]);
    var onLayout = (0, _react.useCallback)(function (e) {
      setContainerHeight(e.nativeEvent.layout.height);
    }, []);
    var showFloatingCallContainer = showJoinCallBanner || isInACall || showIncomingCalls;
    var _useState5 = (0, _react.useState)("downloading"),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      action = _useState6[0],
      setAction = _useState6[1];
    var _useState7 = (0, _react.useState)(undefined),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      lastViewedFileInfo = _useState8[0],
      setLastViewedFileInfo = _useState8[1];
    var _useState9 = (0, _react.useState)(true),
      _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
      enabled = _useState10[0],
      setEnabled = _useState10[1];
    (0, _react.useEffect)(function () {
      setAction("downloading");
      setLastViewedFileInfo(undefined);
    }, [action === "none"]);
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[29]).useServerUrl)();
    var callsConfig = (0, _$$_REQUIRE(_dependencyMap[30]).getCallsConfig)(serverUrl);
    var callsAvailable = callsConfig.pluginEnabled && isCallsEnabledInChannel;
    if (!groupCallsAllowed && channelType !== _$$_REQUIRE(_dependencyMap[18]).General.DM_CHANNEL) {
      callsAvailable = false;
    }
    var positioned = (0, _reactNativeReanimated.useSharedValue)(0);
    var panGesture = _$$_REQUIRE(_dependencyMap[31]).Gesture.Pan();
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var channelTsx1 = function channelTsx1() {
        return {
          transform: [{
            translateY: positioned.value
          }]
        };
      };
      channelTsx1.__closure = {
        positioned: positioned
      };
      channelTsx1.__workletHash = 6586891313714;
      channelTsx1.__initData = _worklet_6586891313714_init_data;
      return channelTsx1;
    }());
    var intl = (0, _$$_REQUIRE(_dependencyMap[32]).useIntl)();
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      isAhasSavedOtp = _useState12[0],
      setHasSavedOtp = _useState12[1];
    var _useOtpController = (0, _otpController.default)(),
      isHasSavedOtp = _useOtpController.isHasSavedOtp,
      validationNumber = _useOtpController.validationNumber,
      isInputOtpTheSameInShared = _useOtpController.isInputOtpTheSameInShared;
    var optValidation = (0, _$$_REQUIRE(_dependencyMap[33]).preventDoubleTap)(function (draftCount) {
      var renderContent = function renderContent() {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[34]).OtpInput, {
            numberOfDigits: 6,
            focusColor: "green",
            autoFocus: true
            // hideStick={true}
            ,
            placeholder: "******"
            // blurOnFilled={true}
            ,
            disabled: false,
            type: "numeric",
            secureTextEntry: false,
            onTextChange: (/*#__PURE__*/function () {
              var _ref2 = (0, _asyncToGenerator2.default)(function* (text) {
                if (text.length === 6) {
                  yield isInputOtpTheSameInShared(text).then(function (result) {
                    if (result === true) (0, _$$_REQUIRE(_dependencyMap[26]).dismissBottomSheet)();else {
                      text = "";
                    }
                  });
                }
              });
              return function (_x) {
                return _ref2.apply(this, arguments);
              };
            }()),
            textInputProps: {
              accessibilityLabel: "One-Time Password"
            }
          })
        });
      };
      var closeButtonId = "close-plus-menu";
      (0, _$$_REQUIRE(_dependencyMap[26]).bottomSheet)({
        closeButtonId: closeButtonId,
        renderContent: renderContent,
        snapPoints: [1, (0, _$$_REQUIRE(_dependencyMap[35]).bottomSheetSnapPoint)(3, 70, 0)],
        theme: theme,
        title: intl.formatMessage({
          id: "home.header.plus_menu",
          defaultMessage: "Options"
        })
      });
    });
    (0, _react.useEffect)(function () {
      isHasSavedOtp().then(function (resut) {
        setHasSavedOtp(resut);
      });
    }, []);
    (0, _react.useEffect)(function () {
      if (validationNumber === 0 && isAhasSavedOtp === true) optValidation();
    }, [isAhasSavedOtp && validationNumber === 0]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_freeze_screen.default, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[22]).SafeAreaView, {
        style: styles.flex,
        mode: "margin",
        edges: edges,
        testID: "channel.screen",
        onLayout: onLayout,
        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[36]).AudioProvider, {
          children: [isAhasSavedOtp && validationNumber === 0 && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: {
              height: windowHeight,
              width: windowWidth,
              position: "absolute",
              backgroundColor: "black",
              opacity: 0.5,
              zIndex: 100
            },
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: {
                height: windowHeight,
                width: windowWidth,
                position: "absolute",
                backgroundColor: "black",
                opacity: 0.5,
                zIndex: 100
              },
              onPress: function onPress() {
                return optValidation();
              }
            })
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_header2.default, {
            channelId: channelId,
            componentId: componentId,
            callsEnabledInChannel: isCallsEnabledInChannel,
            groupCallsAllowed: groupCallsAllowed,
            isTabletView: isTabletView,
            shouldRenderBookmarks: shouldRender
          }),
          /*#__PURE__*/
          //isVisible &&
          (0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[31]).GestureDetector, {
            gesture: panGesture,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
              style: [{
                width: 35,
                height: 96,
                backgroundColor: theme.centerChannelBg,
                position: "absolute",
                start: 28,
                bottom: -108,
                //+audioLockPostion,
                display: "flex",
                flexDirection: "column",
                // backgroundColor:'green',
                paddingHorizontal: 5,
                paddingVertical: 5,
                borderTopLeftRadius: 15,
                borderTopRightRadius: 15,
                zIndex: 1
              }, animatedStyle],
              children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: {},
                children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_lottieReactNative.default, {
                  style: {
                    height: 28,
                    width: 28,
                    marginStart: -3
                  },
                  source: _$$_REQUIRE(_dependencyMap[37]),
                  autoPlay: positioned.value > -20,
                  loop: true,
                  colorFilters: [{
                    keypath: "Layer 2",
                    // Replace with your specific layer name
                    color: theme.sidebarText // Change to your desired color
                  }, {
                    keypath: "Layer 1",
                    // Replace with your specific layer name
                    color: theme.sidebarText // Change to your desired color
                  }]
                }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_lottieReactNative.default, {
                  style: {
                    height: 28,
                    width: 26,
                    marginStart: -2
                  },
                  source: _$$_REQUIRE(_dependencyMap[38]),
                  autoPlay: positioned.value > -20,
                  loop: true,
                  colorFilters: [{
                    keypath: "Icon_ChevronDown_Bouncing",
                    // Replace with your specific layer name
                    color: theme.sidebarText // Change to your desired color
                  }]
                })]
              })
            })
          }), shouldRender && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: [styles.flex, {
                marginTop: marginTop
              }],
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_post_list.default, {
                setLastViewedFileInfo: setLastViewedFileInfo,
                channelId: channelId,
                nativeID: channelId,
                channelType: channelType
              })
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_post_draft.default, {
              pageName: "channel",
              channelId: channelId,
              scrollViewNativeID: channelId,
              accessoriesContainerID: _$$_REQUIRE(_dependencyMap[39]).ACCESSORIES_CONTAINER_NATIVE_ID,
              keyboardTracker: postDraftRef,
              testID: "channel.post_draft",
              containerHeight: containerHeight,
              groupCallsAllowed: groupCallsAllowed,
              isChannelScreen: true,
              translationY: positioned
              //  isVisibale = {isVisibale}
              //canShowPostPriority={true}
            })]
          }), showFloatingCallContainer && shouldRender && /*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_call_container.default, {
            channelId: channelId,
            showJoinCallBanner: showJoinCallBanner,
            showIncomingCalls: showIncomingCalls,
            isInACall: isInACall,
            includeBookmarkBar: includeBookmarkBar
          }), lastViewedFileInfo && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: {
              zIndex: 20
            },
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_toasts.default, {
              action: action,
              fileInfo: lastViewedFileInfo,
              setAction: setAction
            })
          })]
        })
      })
    });
  };
  var _default = exports.default = Channel;
