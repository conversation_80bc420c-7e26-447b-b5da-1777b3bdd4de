  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[1]);
  var _ephemeral_store = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var useGMasDMNotice = function useGMasDMNotice(userId, channelType, dismissedGMasDMNotice, hasGMasDMFeature) {
    var intl = (0, _$$_REQUIRE(_dependencyMap[3]).useIntl)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[4]).useServerUrl)();
    var _useAlert = (0, _$$_REQUIRE(_dependencyMap[5]).useAlert)(),
      showAlert = _useAlert.showAlert;
    (0, _react.useEffect)(function () {
      if (!hasGMasDMFeature) {
        return;
      }
      var preferenceValue = (0, _$$_REQUIRE(_dependencyMap[6]).getPreferenceAsBool)(dismissedGMasDMNotice, _$$_REQUIRE(_dependencyMap[7]).Preferences.CATEGORIES.SYSTEM_NOTICE, _$$_REQUIRE(_dependencyMap[7]).Preferences.NOTICES.GM_AS_DM);
      if (preferenceValue) {
        return;
      }
      if (channelType !== 'G') {
        return;
      }
      if (_ephemeral_store.default.noticeShown.has(_$$_REQUIRE(_dependencyMap[7]).Preferences.NOTICES.GM_AS_DM)) {
        return;
      }
      var onRemindMeLaterPress = function onRemindMeLaterPress() {
        _ephemeral_store.default.noticeShown.add(_$$_REQUIRE(_dependencyMap[7]).Preferences.NOTICES.GM_AS_DM);
      };
      var onHideAndForget = function onHideAndForget() {
        _ephemeral_store.default.noticeShown.add(_$$_REQUIRE(_dependencyMap[7]).Preferences.NOTICES.GM_AS_DM);
        (0, _$$_REQUIRE(_dependencyMap[8]).savePreference)(serverUrl, [{
          category: _$$_REQUIRE(_dependencyMap[7]).Preferences.CATEGORIES.SYSTEM_NOTICE,
          name: _$$_REQUIRE(_dependencyMap[7]).Preferences.NOTICES.GM_AS_DM,
          value: 'true',
          user_id: userId
        }]);
      };

      // Show the GM as DM notice if needed
      showAlert(intl.formatMessage({
        id: 'system_notice.title.gm_as_dm',
        defaultMessage: 'Updates to Group Messages'
      }), intl.formatMessage({
        id: 'system_noticy.body.gm_as_dm',
        defaultMessage: 'You will now be notified for all activity in your group messages along with a notification badge for every new message.\n\nYou can configure this in notification preferences for each group message.'
      }), [{
        text: intl.formatMessage({
          id: 'system_notice.remind_me',
          defaultMessage: 'Remind Me Later'
        }),
        onPress: onRemindMeLaterPress
      }, {
        text: intl.formatMessage({
          id: 'system_notice.dont_show',
          defaultMessage: 'Don\'t Show Again'
        }),
        onPress: onHideAndForget
      }]);
    }, []);
  };
  var _default = exports.default = useGMasDMNotice;
