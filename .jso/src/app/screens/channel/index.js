  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _channel2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var database = _ref.database,
      serverUrl = _ref.serverUrl;
    var channelId = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentChannelId)(database);
    // const members = observeChannelMembers(database,channelId);

    var dismissedGMasDMNotice = (0, _$$_REQUIRE(_dependencyMap[5]).queryPreferencesByCategoryAndName)(database, _$$_REQUIRE(_dependencyMap[6]).Preferences.CATEGORIES.SYSTEM_NOTICE, _$$_REQUIRE(_dependencyMap[6]).Preferences.NOTICES.GM_AS_DM).observe();
    var channelType = (0, _$$_REQUIRE(_dependencyMap[7]).observeCurrentChannel)(database).pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(c == null ? undefined : c.type);
    }));
    var currentUserId = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentUserId)(database);
    var hasGMasDMFeature = (0, _$$_REQUIRE(_dependencyMap[9]).observeHasGMasDMFeature)(database);
    var isBookmarksEnabled = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'FeatureFlagChannelBookmarks');
    var hasBookmarks = function hasBookmarks(count) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(count > 0);
    };
    var includeBookmarkBar = channelId.pipe((0, _$$_REQUIRE(_dependencyMap[8]).combineLatestWith)(isBookmarksEnabled), (0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        cId = _ref3[0],
        enabled = _ref3[1];
      if (!enabled) {
        return (0, _$$_REQUIRE(_dependencyMap[8]).of)(false);
      }
      return (0, _$$_REQUIRE(_dependencyMap[10]).queryBookmarks)(database, cId).observeCount(false).pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(hasBookmarks), (0, _$$_REQUIRE(_dependencyMap[8]).distinctUntilChanged)());
    }));
    var groupCallsAllowed = (0, _$$_REQUIRE(_dependencyMap[11]).observeCallsConfig)(serverUrl).pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (config) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(config.GroupCallsAllowed);
    }), (0, _$$_REQUIRE(_dependencyMap[8]).distinctUntilChanged)());
    return Object.assign({
      // members,
      channelId: channelId
    }, (0, _$$_REQUIRE(_dependencyMap[12]).observeCallStateInChannel)(serverUrl, database, channelId), {
      isCallsEnabledInChannel: (0, _$$_REQUIRE(_dependencyMap[12]).observeIsCallsEnabledInChannel)(database, serverUrl, channelId),
      groupCallsAllowed: groupCallsAllowed,
      dismissedGMasDMNotice: dismissedGMasDMNotice,
      channelType: channelType,
      currentUserId: currentUserId,
      hasGMasDMFeature: hasGMasDMFeature,
      includeBookmarkBar: includeBookmarkBar
    });
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)((0, _$$_REQUIRE(_dependencyMap[13]).withServerUrl)(enhanced(_channel2.default)));
