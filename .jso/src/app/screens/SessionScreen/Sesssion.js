  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _freeze_screen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _numberToArabicMonth = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var edges = ['left', 'right'];
  var trackKeyboardForScreens = [_$$_REQUIRE(_dependencyMap[10]).Screens.THREAD];
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    }
  });
  var SesssionScreen = function SesssionScreen(_ref) {
    var currentUser = _ref.currentUser;
    var postDraftRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      containerHeight = _useState2[0],
      setContainerHeight = _useState2[1];
    var intl = (0, _$$_REQUIRE(_dependencyMap[11]).useIntl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[12]).useIsTablet)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[13]).useServerUrl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[14]).useTheme)();
    var windowWidth = _reactNative.Dimensions.get('window').width;
    var onLayout = (0, _react.useCallback)(function (e) {
      setContainerHeight(e.nativeEvent.layout.height);
    }, []);
    var openSessionDialogData = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[15]).preventDoubleTap)(function (sessionData) {
      console.log(`\n\nthis the session data ${JSON.stringify(sessionData)}\n\n`);
      var renderContent = function renderContent() {
        var _sessionData$props, _sessionData$props2, _sessionData$props3, _sessionData$props4, _sessionData$props5, _sessionData$props6, _sessionData$props7, _sessionData$props8;
        if (sessionData === undefined) return /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
        return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: {
            marginTop: 10,
            width: windowWidth - 20,
            marginStart: -10,
            height: 204,
            borderRadius: 7,
            paddingVertical: 10,
            flexDirection: 'column',
            alignItems: 'center'
          },
          children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: {
              width: 'auto',
              flexDirection: 'column',
              marginTop: 3,
              marginBottom: 5,
              alignItems: 'center'
            },
            children: [((_sessionData$props = sessionData.props) == null ? undefined : _sessionData$props.browser.split(' ')[0]) === 'Desktop' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).ComputerDesktopIcon, {
              style: {
                //  marginStart: 35
              },
              size: 24,
              color: theme.sidebarText
            }) : ((_sessionData$props2 = sessionData.props) == null ? undefined : _sessionData$props2.browser.split(' ')[0]) === 'Browser' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).GlobeAltIcon, {
              size: 24,
              color: theme.sidebarText,
              style: {
                //     marginStart: 35
              }
            }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).DevicePhoneMobileIcon, {
              size: 24,
              color: theme.sidebarText,
              style: {
                //   marginStart: 35
              }
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: {
                color: theme.sidebarText,
                fontFamily: 'IBMPlexSansArabic-Bold',
                fontSize: 19,
                width: 'auto'
              },
              children: ((_sessionData$props3 = sessionData.props) == null ? undefined : _sessionData$props3.browser.split(' ')[0]) === 'Desktop' ? intl.formatMessage({
                id: 'session.account.deskTop',
                defaultMessage: 'desktop sessionaccount'
              }) : ((_sessionData$props4 = sessionData.props) == null ? undefined : _sessionData$props4.browser.split(' ')[0]) === 'Browser' ? intl.formatMessage({
                id: 'session.account.browser',
                defaultMessage: 'browser sessionaccount'
              }) : intl.formatMessage({
                id: 'session.account.android',
                defaultMessage: 'android sessionaccount'
              })
            })]
          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: {
              flexDirection: 'row',
              marginTop: 15,
              justifyContent: 'space-between',
              width: windowWidth,
              paddingEnd: 18
            },
            children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: {
                flexDirection: 'row',
                justifyContent: 'flex-start',
                start: 15,
                flex: 1
              },
              children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).EyeIcon, {
                size: 24,
                color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                style: {
                  marginEnd: 5
                }
              }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                style: {
                  color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                  fontFamily: 'IBMPlexSansArabic-SemiBold',
                  fontSize: 17
                },
                children: intl.formatMessage({
                  id: 'last_seen',
                  defaultMessage: 'last seen'
                })
              })]
            }), lastActiveShape(new Date(sessionData.last_activity_at), true)]
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: {
              backgroundColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
              width: windowWidth - 30,
              borderBottomWidth: 1,
              borderBottomColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
              marginStart: 0
            }
          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: {
              flexDirection: 'row',
              marginTop: 15,
              justifyContent: 'space-between',
              width: windowWidth,
              paddingEnd: 18
            },
            children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: {
                flexDirection: 'row',
                justifyContent: 'flex-start',
                start: 15,
                flex: 1
              },
              children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).BoltIcon, {
                size: 24,
                color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                style: {
                  marginEnd: 5
                }
              }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                style: {
                  color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                  fontWeight: 200,
                  fontFamily: 'IBMPlexSansArabic-SemiBold',
                  fontSize: 17
                },
                children: intl.formatMessage({
                  id: 'first_seen',
                  defaultMessage: 'first time seen'
                })
              })]
            }), lastActiveShape(new Date(sessionData.create_at), true)]
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: {
              backgroundColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
              width: windowWidth - 30,
              borderBottomWidth: 1,
              borderBottomColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
              marginStart: 0
            }
          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: {
              flexDirection: 'row',
              marginTop: 15,
              justifyContent: 'space-between',
              width: windowWidth,
              paddingEnd: 18
            },
            children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: {
                flexDirection: 'row',
                justifyContent: 'flex-start',
                start: 15,
                flex: 1
              },
              children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).CommandLineIcon, {
                size: 24,
                color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                style: {
                  marginEnd: 5
                }
              }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                style: {
                  color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                  fontWeight: 200,
                  fontFamily: 'IBMPlexSansArabic-SemiBold',
                  fontSize: 17
                },
                children: intl.formatMessage({
                  id: 'os',
                  defaultMessage: 'os'
                })
              })]
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: {
                color: theme.sidebarText,
                fontWeight: 200,
                fontFamily: 'IBMPlexSansArabic-Bold',
                fontSize: 17
              },
              children: ((_sessionData$props5 = sessionData.props) == null ? undefined : _sessionData$props5.os) !== "" ? (_sessionData$props6 = sessionData.props) == null ? undefined : _sessionData$props6.os : (_sessionData$props7 = sessionData.props) != null && _sessionData$props7.isMobile ? 'Android' : ''
            })]
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: {
              backgroundColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
              width: windowWidth - 30,
              borderBottomWidth: 1,
              borderBottomColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
              marginStart: 0
            }
          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: {
              flexDirection: 'row',
              marginTop: 15,
              justifyContent: 'space-between',
              width: windowWidth,
              paddingEnd: 18
            },
            children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: {
                flexDirection: 'row',
                justifyContent: 'flex-start',
                start: 15,
                flex: 1
              },
              children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).CommandLineIcon, {
                size: 24,
                color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                style: {
                  marginEnd: 5
                }
              }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                style: {
                  color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                  fontWeight: 200,
                  fontFamily: 'IBMPlexSansArabic-SemiBold',
                  fontSize: 17
                },
                children: intl.formatMessage({
                  id: 'browser',
                  defaultMessage: 'browser'
                })
              })]
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: {
                color: theme.sidebarText,
                fontWeight: 200,
                fontFamily: 'IBMPlexSansArabic-Bold',
                fontSize: 17
              },
              children: (_sessionData$props8 = sessionData.props) == null ? undefined : _sessionData$props8.browser
            })]
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: {
              backgroundColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
              width: windowWidth - 30,
              borderBottomWidth: 1,
              borderBottomColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
              marginStart: 0
            }
          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: {
              flexDirection: 'row',
              marginTop: 15,
              justifyContent: 'space-between',
              width: windowWidth,
              paddingEnd: 18
            },
            children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: {
                flexDirection: 'row',
                justifyContent: 'flex-start',
                start: 15,
                flex: 1
              },
              children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).CodeBracketIcon, {
                size: 24,
                color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                style: {
                  marginEnd: 5
                }
              }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
                style: {
                  width: windowWidth - 50
                },
                children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                  style: {
                    color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.60),
                    fontWeight: 200,
                    fontFamily: 'IBMPlexSansArabic-SemiBold',
                    fontSize: 17
                  },
                  children: intl.formatMessage({
                    id: 'session.id',
                    defaultMessage: 'browser'
                  })
                })
              })]
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: {
                width: windowWidth - 159
                //overflow:'hidden'
              },
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                numberOfLines: 1,
                style: {
                  color: theme.sidebarText,
                  fontWeight: 200,
                  fontFamily: 'IBMPlexSansArabic-Bold',
                  fontSize: 17
                },
                children: sessionData.id
              })
            })]
          })]
        });
      };
      var closeButtonId = 'close-plus-menu';
      var items = 1;
      var separators = 0;
      (0, _$$_REQUIRE(_dependencyMap[18]).bottomSheet)({
        closeButtonId: closeButtonId,
        renderContent: renderContent,
        snapPoints: [1, (0, _$$_REQUIRE(_dependencyMap[19]).bottomSheetSnapPoint)(items, 360, 1)],
        theme: theme,
        title: intl.formatMessage({
          id: 'home.header.plus_menu',
          defaultMessage: 'Options'
        })
      });
    }), [intl, isTablet, theme]);
    var lastActiveShape = function lastActiveShape(dataHolder) {
      var isColord = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: {
          flexDirection: 'row',
          marginBottom: 10,
          justifyContent: 'center'
        },
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: {
            color: isColord ? theme.sidebarText : (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.36),
            fontFamily: isColord ? 'IBMPlexSansArabic-Bold' : 'IBMPlexSansArabic-Regular',
            fontSize: 16,
            marginStart: 4
          },
          children: dataHolder.getDay() > 9 ? dataHolder.getDay() : '0' + dataHolder.getDay()
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: {
            color: isColord ? theme.sidebarText : (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.36),
            fontFamily: isColord ? 'IBMPlexSansArabic-Bold' : 'IBMPlexSansArabic-Regular',
            fontSize: 16,
            marginStart: 4
          },
          children: (0, _numberToArabicMonth.default)(dataHolder.getMonth())
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: {
            color: isColord ? theme.sidebarText : (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.36),
            fontFamily: isColord ? 'IBMPlexSansArabic-Bold' : 'IBMPlexSansArabic-Regular',
            fontSize: 16,
            marginStart: 4
          },
          children: dataHolder.getFullYear()
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: {
            color: isColord ? theme.sidebarText : (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.36),
            fontFamily: isColord ? 'IBMPlexSansArabic-Bold' : 'IBMPlexSansArabic-Regular',
            fontSize: 16,
            marginStart: 4
          },
          children: ': ' + dataHolder.getMinutes()
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: {
            color: isColord ? theme.sidebarText : (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.36),
            fontFamily: isColord ? 'IBMPlexSansArabic-Bold' : 'IBMPlexSansArabic-Regular',
            fontSize: 16,
            marginStart: 4
          },
          children: dataHolder.getHours() > 12 ? dataHolder.getHours() - 12 + ' م' : dataHolder.getHours() + 'ص'
        })]
      });
    };
    var renderSessions = (0, _react.useCallback)(function (data) {
      var _data$item$props, _data$item$props2, _data$item$props3, _data$item$props4;
      var lastActiveAt = new Date(data.item.last_activity_at);
      return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: {
          marginTop: 10,
          width: windowWidth - 20,
          marginStart: 10,
          height: 204,
          borderWidth: 1,
          borderColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
          borderRadius: 7,
          paddingHorizontal: 10,
          paddingVertical: 10,
          flexDirection: 'column'
        },
        children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: {
            width: 'auto',
            flexDirection: 'row',
            marginTop: 3
          },
          children: [((_data$item$props = data.item.props) == null ? undefined : _data$item$props.browser.split(' ')[0]) === 'Desktop' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).ComputerDesktopIcon, {
            size: 24,
            color: theme.sidebarText
          }) : ((_data$item$props2 = data.item.props) == null ? undefined : _data$item$props2.browser.split(' ')[0]) === 'Browser' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).GlobeAltIcon, {
            size: 24,
            color: theme.sidebarText
          }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[16]).DevicePhoneMobileIcon, {
            size: 24,
            color: theme.sidebarText
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: {
              color: theme.sidebarText,
              fontFamily: 'IBMPlexSansArabic-Bold',
              fontSize: 16,
              marginStart: 4
            },
            children: ((_data$item$props3 = data.item.props) == null ? undefined : _data$item$props3.browser.split(' ')[0]) === 'Desktop' ? intl.formatMessage({
              id: 'session.account.deskTop',
              defaultMessage: 'desktop sessionaccount'
            }) : ((_data$item$props4 = data.item.props) == null ? undefined : _data$item$props4.browser.split(' ')[0]) === 'Browser' ? intl.formatMessage({
              id: 'session.account.browser',
              defaultMessage: 'browser sessionaccount'
            }) : intl.formatMessage({
              id: 'session.account.android',
              defaultMessage: 'android sessionaccount'
            })
          })]
        }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: {
            width: 'auto',
            flexDirection: 'row',
            marginTop: 3
          },
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: {
              color: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.36),
              fontFamily: 'IBMPlexSansArabic-Light',
              fontSize: 17,
              marginStart: 4
            },
            children: intl.formatMessage({
              id: 'channel_creator_last_seen',
              defaultMessage: 'last seen'
            })
          }), lastActiveShape(new Date(data.item.last_activity_at))]
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[20]).TouchableOpacity, {
          onPress: function onPress() {
            return openSessionDialogData(data.item);
          },
          style: {
            width: 'auto',
            alignItems: 'center',
            marginTop: 15
          },
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: {
              color: theme.buttonBg,
              fontFamily: 'IBMPlexSansArabic-Bold',
              fontSize: 14
            },
            children: intl.formatMessage({
              id: 'mobile.search.show_more.info',
              defaultMessage: 'show more info'
            })
          })
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[20]).TouchableOpacity, {
          onPress: function onPress() {
            return revockSession(data.item.id);
          },
          style: {
            alignItems: 'center',
            marginTop: 25,
            height: 40,
            width: windowWidth - 40,
            paddingTop: 5,
            borderWidth: 1,
            borderColor: (0, _$$_REQUIRE(_dependencyMap[17]).changeOpacity)(theme.sidebarText, 0.16),
            borderRadius: 7
          },
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: {
              color: theme.dndIndicator,
              fontFamily: 'IBMPlexSansArabic-Bold',
              fontSize: 14
            },
            children: intl.formatMessage({
              id: 'mobile.account.logout',
              defaultMessage: 'logout'
            })
          })
        })]
      });
    }, [intl.locale, isTablet]);
    var _useState3 = (0, _react.useState)(undefined),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      sessions = _useState4[0],
      changeSession = _useState4[1];
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isLoading = _useState6[0],
      changeLoadingState = _useState6[1];
    var getSessions = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      yield (0, _$$_REQUIRE(_dependencyMap[21]).fetchSessions)(serverUrl, currentUser.id).then(function (data) {
        changeLoadingState(false);
        changeSession(data);
        //  console.log(data[0])
        // data?.forEach((x) => {

        // })
      }).catch(function (err) {
        changeLoadingState(false);
      });
    }), [currentUser == null ? undefined : currentUser.id]);
    var revockSession = (0, _$$_REQUIRE(_dependencyMap[15]).preventDoubleTap)(/*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (sessionId) {
        if (currentUser !== undefined) {
          yield (0, _$$_REQUIRE(_dependencyMap[21]).removeSessions)(serverUrl, currentUser.id, sessionId).catch(function (error) {
            console.log(`\n\nthis the error from removeing session ${error}\n\n`);
          }).then(function (data) {
            var sessionssCurrent = sessions == null ? undefined : sessions.filter(function (x) {
              return x.id !== sessionId;
            });
            changeSession(function (l) {
              return l == null ? undefined : l.filter(function (x) {
                return x.id !== sessionId;
              });
            });
            console.log(`\n\nthis the  data from removeing session ${data}\n\n`);
          });
        }
      });
      return function (_x) {
        return _ref3.apply(this, arguments);
      };
    }());
    (0, _react.useEffect)(function () {
      getSessions();
    }, []);
    var onBackPress = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[18]).popTopScreen)("SessionScreen");
    }, ["SessionScreen"]);
    (0, _android_back_handler.default)("SessionScreen", onBackPress);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_freeze_screen.default, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[22]).SafeAreaView, {
        style: styles.flex,
        mode: "margin",
        edges: edges,
        testID: "thread.screen",
        onLayout: onLayout,
        children: [isLoading && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: {
            height: '100%',
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center'
          },
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
            size: 40
          })
        }), sessions && sessions.length > 0 && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          data: sessions
          // ref={listRef}
          ,
          renderItem: renderSessions,
          style: {
            flex: 1
          },
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false
          //  keyExtractor={extractKey}
          ,
          initialNumToRender: sessions.length

          // @ts-expect-error strictMode not included in the types
          ,
          strictMode: true
        })]
      })
    });
  };
  var _default = exports.default = SesssionScreen;
