  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeWebview = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  var AI = function AI(_ref) {
    var token = _ref.token;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[6]).useServerUrl)();
    var webViewRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      canGoBack = _useState2[0],
      setCanGoBack = _useState2[1];
    var _useState3 = (0, _react.useState)(`${serverUrl}/dfg/messages/@ai`),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      currentUrl = _useState4[0],
      setCurrentUrl = _useState4[1];
    var _useState5 = (0, _react.useState)(0),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      authAttempts = _useState6[0],
      setAuthAttempts = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      showError = _useState8[0],
      setShowError = _useState8[1];
    var MAX_AUTH_ATTEMPTS = 3;
    (0, _react.useEffect)(function () {
      console.log(`\n\n\nthe component is playing\n\n\n`);
    }, [token.length > 0]);

    // Debug function to log all requests
    var onShouldStartLoadWithRequest = function onShouldStartLoadWithRequest(request) {
      console.log('Loading URL:', request.url);
      console.log('Request Headers:', request.headers);
      return true;
    };

    // JavaScript to inject for authentication
    var authScript = `
    try {
      // Try localStorage first
      localStorage.setItem('mattermost_token', '${token}');
      
      // Then try cookies as fallback
      document.cookie = 'MMTOKEN=${token}; path=/; secure; samesite=none';
      
      // If we're on login page, try to redirect
      if (window.location.pathname.includes('/login')) {
        window.location.href = '${serverUrl}/playbooks/start';
      }
      
      // Send confirmation back to React Native
      window.ReactNativeWebView.postMessage('AUTH_INJECTED');
    } catch (e) {
      window.ReactNativeWebView.postMessage('AUTH_ERROR:' + e.message);
    }
    true;
  `;

    // Handle messages from WebView
    var handleMessage = function handleMessage(event) {
      var message = event.nativeEvent.data;
      console.log('Message from WebView:', message);
      if (message === 'AUTH_INJECTED') {
        console.log('Authentication injected successfully');
      } else if (message.startsWith('AUTH_ERROR')) {
        console.error('Authentication injection failed:', message);
      } else if (message === 'NEEDS_AUTH') {
        handleAuthFailure();
      }
    };

    // Handle authentication failures
    var handleAuthFailure = function handleAuthFailure() {
      if (authAttempts < MAX_AUTH_ATTEMPTS) {
        var _webViewRef$current;
        console.log(`Attempting re-authentication (${authAttempts + 1}/${MAX_AUTH_ATTEMPTS})`);
        setAuthAttempts(authAttempts + 1);
        (_webViewRef$current = webViewRef.current) == null ? undefined : _webViewRef$current.injectJavaScript(authScript);
      } else {
        setShowError(true);
      }
    };

    // Handle navigation state changes
    var handleNavigationStateChange = function handleNavigationStateChange(navState) {
      // console.log('Navigation State Change:', JSON.stringify(navState));
      if (!navState.url.includes('@ai')) {
        (0, _$$_REQUIRE(_dependencyMap[7]).popTopScreen)("AI");
      }
      setCanGoBack(navState.canGoBack);
      setCurrentUrl(navState.url);

      // Check if we've been redirected to login
      if (navState.url.includes('/login') && authAttempts < MAX_AUTH_ATTEMPTS) {
        console.log('Detected login page redirect');
        setTimeout(function () {
          var _webViewRef$current2;
          (_webViewRef$current2 = webViewRef.current) == null ? undefined : _webViewRef$current2.injectJavaScript(authScript);
        }, 1000);
      }
    };

    // Back button handler
    var handleBackPress = (0, _react.useCallback)(function () {
      if (canGoBack && webViewRef.current) {
        webViewRef.current.goBack();
        return true;
      }
      (0, _$$_REQUIRE(_dependencyMap[7]).popTopScreen)("PlayBook");
      return true;
    }, [canGoBack]);
    (0, _react.useEffect)(function () {
      var backHandler = _reactNative.BackHandler.addEventListener('hardwareBackPress', handleBackPress);
      return function () {
        return backHandler.remove();
      };
    }, [handleBackPress]);
    if (showError) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center'
        },
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          children: "Authentication failed. Please try again later."
        })
      });
    }
    if (token.length === 0) return null;
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeWebview.default, {
      ref: webViewRef,
      source: {
        uri: currentUrl,
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Auth-Token': token,
          Cookie: `MMTOKEN=${token}`
        }
      },
      injectedJavaScript: authScript,
      onMessage: handleMessage,
      onShouldStartLoadWithRequest: onShouldStartLoadWithRequest,
      javaScriptEnabled: true,
      domStorageEnabled: true,
      sharedCookiesEnabled: true,
      thirdPartyCookiesEnabled: true,
      startInLoadingState: true,
      userAgent: `Mozilla/5.0 (${'Linux; Android 10'}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36 MattermostApp`,
      onNavigationStateChange: handleNavigationStateChange,
      onHttpError: function onHttpError(error) {
        return console.log('HTTP Error:', error.nativeEvent);
      },
      onError: function onError(error) {
        return console.log('WebView Error:', error.nativeEvent);
      },
      cacheEnabled: false,
      incognito: false,
      mixedContentMode: "compatibility",
      allowsBackForwardNavigationGestures: true,
      style: {
        flex: 1
      }
    });
  };
  var _default = exports.default = AI;
  var styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      marginHorizontal: 16
    },
    dropdown: {
      height: 40,
      marginTop: 7,
      borderColor: "#E1E4EA",
      borderWidth: 1.5,
      borderRadius: 8
    },
    center: {
      flexDirection: 'column',
      justifyContent: "center",
      alignItems: "center",
      height: _$$_REQUIRE(_dependencyMap[8]).WINDOW_HEIGHT
    },
    text_lg: {
      fontSize: 14,
      fontFamily: "IBMPlexSansArabic-SemiBold",
      marginTop: 10
    },
    text_sm: {
      fontSize: 12,
      fontFamily: "IBMPlexSansArabic-Regular",
      marginTop: 6
    },
    messagePosition: {
      position: "absolute",
      bottom: 12,
      left: 0,
      right: 0
    },
    grid: {
      display: "flex",
      flexDirection: "row",
      gap: 12
    },
    buttonScrollView: {
      marginVertical: 8,
      paddingHorizontal: 8
    },
    button: {
      paddingHorizontal: 16,
      paddingVertical: 2,
      borderRadius: 8,
      height: 34,
      borderColor: "#E1E4EA",
      borderWidth: 1,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      marginHorizontal: 5
    },
    selectedButton: {
      backgroundColor: "#00987E"
    },
    buttonText: {
      color: "#000"
    },
    selectedButtonText: {
      color: "#fff"
    },
    lable: {
      fontFamily: "IBMPlexSansArabic-SemiBold",
      color: "#001210",
      fontSize: 12
    },
    sendButton: {
      color: "#00987E",
      //position: "absolute",
      // top: 26,
      // right: 10,
      transform: [{
        rotate: '180deg'
      }]
      //zIndex: 10
    },
    sendButtonText: {
      color: "#fff",
      fontSize: 16
    },
    resultImage: {
      width: 300,
      height: 300,
      resizeMode: "contain",
      marginVertical: 16
    },
    captionText: {
      fontSize: 16,
      textAlign: "right",
      width: '100%'
    },
    input: {
      //  marginTop: 10,
      //height: 56,
      textAlign: "right",
      paddingHorizontal: 20,
      fontFamily: "IBMPlexSansArabic-Regular",
      fontSize: 14,
      color: "#001210"
    },
    inputGroup: {
      borderColor: "#E1E4EA",
      borderWidth: 1,
      borderRadius: 8
    }
  });
