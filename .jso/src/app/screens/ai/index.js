  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _ai = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['rootId'], function (_ref) {
    var database = _ref.database,
      serverUrl = _ref.serverUrl,
      rootId = _ref.rootId;
    var currentTeamId = (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentTeamId)(database);
    var categories = currentTeamId.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (ctid) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).queryCategoriesByTeamIds)(database, [ctid]).observeWithColumns(['sort_order']);
    }));
    return {
      categories: categories
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)((0, _$$_REQUIRE(_dependencyMap[6]).withServerUrl)(enhanced(_ai.default)));
