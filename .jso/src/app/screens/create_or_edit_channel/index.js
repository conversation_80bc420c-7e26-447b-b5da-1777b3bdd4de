  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _create_or_edit_channel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)([], function (_ref) {
    var database = _ref.database,
      channelId = _ref.channelId;
    var channel = channelId ? (0, _$$_REQUIRE(_dependencyMap[3]).observeChannel)(database, channelId) : (0, _$$_REQUIRE(_dependencyMap[4]).of)(undefined);
    var channelInfo = channelId ? (0, _$$_REQUIRE(_dependencyMap[3]).observeChannelInfo)(database, channelId) : (0, _$$_REQUIRE(_dependencyMap[4]).of)(undefined);
    return {
      channel: channel,
      channelInfo: channelInfo
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_create_or_edit_channel.default));
