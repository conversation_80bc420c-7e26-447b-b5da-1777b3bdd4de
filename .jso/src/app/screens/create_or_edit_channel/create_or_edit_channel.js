  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _channel_info_form = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var CLOSE_BUTTON_ID = 'close-channel';
  var EDIT_BUTTON_ID = 'update-channel';
  var CREATE_BUTTON_ID = 'create-channel';
  var RequestActions = /*#__PURE__*/function (RequestActions) {
    RequestActions["START"] = "Start";
    RequestActions["COMPLETE"] = "Complete";
    RequestActions["FAILURE"] = "Failure";
    return RequestActions;
  }(RequestActions || {});
  var close = function close(componentId, isModal) {
    _reactNative.Keyboard.dismiss();
    if (isModal) {
      (0, _$$_REQUIRE(_dependencyMap[10]).dismissModal)({
        componentId: componentId
      });
    } else {
      (0, _$$_REQUIRE(_dependencyMap[10]).popTopScreen)(componentId);
    }
  };
  var isDirect = function isDirect(channel) {
    return (channel == null ? undefined : channel.type) === _$$_REQUIRE(_dependencyMap[11]).General.DM_CHANNEL || (channel == null ? undefined : channel.type) === _$$_REQUIRE(_dependencyMap[11]).General.GM_CHANNEL;
  };
  var makeCloseButton = function makeCloseButton(icon) {
    return (0, _$$_REQUIRE(_dependencyMap[10]).buildNavigationButton)(CLOSE_BUTTON_ID, 'close.create_or_edit_channel.button', icon);
  };
  var CreateOrEditChannel = function CreateOrEditChannel(_ref) {
    var componentId = _ref.componentId,
      channel = _ref.channel,
      channelInfo = _ref.channelInfo,
      headerOnly = _ref.headerOnly,
      isModal = _ref.isModal;
    var intl = (0, _$$_REQUIRE(_dependencyMap[12]).useIntl)();
    var formatMessage = intl.formatMessage;
    var theme = (0, _$$_REQUIRE(_dependencyMap[13]).useTheme)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[14]).useServerUrl)();
    var editing = Boolean(channel);
    var _useState = (0, _react.useState)((channel == null ? undefined : channel.type) || _$$_REQUIRE(_dependencyMap[11]).General.OPEN_CHANNEL),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      type = _useState2[0],
      setType = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      canSave = _useState4[0],
      setCanSave = _useState4[1];
    var _useState5 = (0, _react.useState)((channel == null ? undefined : channel.displayName) || ''),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      displayName = _useState6[0],
      setDisplayName = _useState6[1];
    var _useState7 = (0, _react.useState)((channelInfo == null ? undefined : channelInfo.purpose) || ''),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      purpose = _useState8[0],
      setPurpose = _useState8[1];
    var _useState9 = (0, _react.useState)((channelInfo == null ? undefined : channelInfo.header) || ''),
      _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
      header = _useState10[0],
      setHeader = _useState10[1];
    var _useReducer = (0, _react.useReducer)(function (state, action) {
        switch (action.type) {
          case RequestActions.START:
            return {
              error: '',
              saving: true
            };
          case RequestActions.COMPLETE:
            return {
              error: '',
              saving: false
            };
          case RequestActions.FAILURE:
            return {
              error: action.error,
              saving: false
            };
          default:
            return state;
        }
      }, {
        error: '',
        saving: false
      }),
      _useReducer2 = (0, _slicedToArray2.default)(_useReducer, 2),
      appState = _useReducer2[0],
      dispatch = _useReducer2[1];
    var rightButton = (0, _react.useMemo)(function () {
      var base = (0, _$$_REQUIRE(_dependencyMap[10]).buildNavigationButton)(editing ? EDIT_BUTTON_ID : CREATE_BUTTON_ID, editing ? 'create_or_edit_channel.save.button' : 'create_or_edit_channel.create.button', undefined, editing ? formatMessage({
        id: 'mobile.edit_channel',
        defaultMessage: 'Save'
      }) : formatMessage({
        id: 'mobile.create_channel',
        defaultMessage: 'Create'
      }));
      base.enabled = canSave;
      base.showAsAction = 'always';
      base.color = theme.sidebarHeaderTextColor;
      return base;
    }, [editing, theme.sidebarHeaderTextColor, intl, canSave]);
    (0, _react.useEffect)(function () {
      (0, _$$_REQUIRE(_dependencyMap[10]).setButtons)(componentId, {
        rightButtons: [rightButton]
      });
    }, [rightButton, componentId]);
    (0, _react.useEffect)(function () {
      if (isModal) {
        var icon = _compass_icon.default.getImageSourceSync('close', 24, theme.sidebarHeaderTextColor);
        (0, _$$_REQUIRE(_dependencyMap[10]).setButtons)(componentId, {
          leftButtons: [makeCloseButton(icon)]
        });
      }
    }, [theme, isModal]);
    (0, _react.useEffect)(function () {
      setCanSave(displayName.length >= _$$_REQUIRE(_dependencyMap[15]).MIN_CHANNEL_NAME_LENGTH && (displayName !== (channel == null ? undefined : channel.displayName) || purpose !== (channelInfo == null ? undefined : channelInfo.purpose) || header !== (channelInfo == null ? undefined : channelInfo.header) || type !== channel.type));
    }, [channel, displayName, purpose, header, type]);
    var isValidDisplayName = (0, _react.useCallback)(function () {
      if (isDirect(channel)) {
        return true;
      }
      var result = (0, _$$_REQUIRE(_dependencyMap[16]).validateDisplayName)(intl, displayName);
      if (result.error) {
        dispatch({
          type: RequestActions.FAILURE,
          error: result.error
        });
        return false;
      }
      return true;
    }, [channel, displayName]);
    var onCreateChannel = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      dispatch({
        type: RequestActions.START
      });
      _reactNative.Keyboard.dismiss();
      if (!isValidDisplayName()) {
        return;
      }
      setCanSave(false);
      var createdChannel = yield (0, _$$_REQUIRE(_dependencyMap[17]).createChannel)(serverUrl, displayName, purpose, header, type);
      if (createdChannel.error) {
        dispatch({
          type: RequestActions.FAILURE,
          error: createdChannel.error
        });
        return;
      }
      dispatch({
        type: RequestActions.COMPLETE
      });
      close(componentId, isModal);
      (0, _$$_REQUIRE(_dependencyMap[17]).switchToChannelById)(serverUrl, createdChannel.channel.id, createdChannel.channel.team_id);
    }), [serverUrl, type, displayName, header, isModal, purpose, isValidDisplayName]);
    var onUpdateChannel = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!channel) {
        return;
      }
      dispatch({
        type: RequestActions.START
      });
      _reactNative.Keyboard.dismiss();
      if (!isValidDisplayName()) {
        return;
      }
      var patchChannel = Object.assign({
        header: header
      }, !isDirect(channel) && {
        display_name: displayName,
        purpose: purpose
      });
      setCanSave(false);
      var patchedChannel = yield (0, _$$_REQUIRE(_dependencyMap[17]).patchChannel)(serverUrl, channel.id, patchChannel);
      if (patchedChannel.error) {
        dispatch({
          type: RequestActions.FAILURE,
          error: patchedChannel.error
        });
        return;
      }
      dispatch({
        type: RequestActions.COMPLETE
      });
      close(componentId, isModal);
    }), [channel == null ? undefined : channel.id, channel == null ? undefined : channel.type, displayName, header, isModal, purpose, isValidDisplayName]);
    var handleClose = (0, _react.useCallback)(function () {
      close(componentId, isModal);
    }, [isModal]);
    (0, _navigation_button_pressed.default)(CLOSE_BUTTON_ID, componentId, handleClose, [handleClose]);
    (0, _navigation_button_pressed.default)(CREATE_BUTTON_ID, componentId, onCreateChannel, [onCreateChannel]);
    (0, _navigation_button_pressed.default)(EDIT_BUTTON_ID, componentId, onUpdateChannel, [onUpdateChannel]);
    (0, _android_back_handler.default)(componentId, handleClose);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_info_form.default, {
      error: appState.error,
      saving: appState.saving,
      channelType: channel == null ? undefined : channel.type,
      editing: editing,
      onTypeChange: setType,
      type: type,
      displayName: displayName,
      onDisplayNameChange: setDisplayName,
      header: header,
      headerOnly: headerOnly,
      onHeaderChange: setHeader,
      purpose: purpose,
      onPurposeChange: setPurpose
    });
  };
  var _default = exports.default = CreateOrEditChannel;
