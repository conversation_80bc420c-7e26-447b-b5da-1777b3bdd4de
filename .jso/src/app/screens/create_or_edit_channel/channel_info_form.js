  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = ChannelInfoForm;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _autocomplete = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _error_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _floating_text_input_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var FIELD_MARGIN_BOTTOM = 24;
  var MAKE_PRIVATE_MARGIN_BOTTOM = 32;
  var BOTTOM_AUTOCOMPLETE_SEPARATION = _reactNative.Platform.select({
    ios: 10,
    default: 10
  });
  var LIST_PADDING = 32;
  var AUTOCOMPLETE_ADJUST = 5;
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[11]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1
      },
      scrollView: {
        paddingVertical: LIST_PADDING,
        paddingHorizontal: 20
      },
      errorContainer: {
        width: '100%'
      },
      errorWrapper: {
        justifyContent: 'center',
        alignItems: 'center'
      },
      loading: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
      },
      makePrivateContainer: {
        marginBottom: MAKE_PRIVATE_MARGIN_BOTTOM
      },
      fieldContainer: {
        marginBottom: FIELD_MARGIN_BOTTOM
      },
      helpText: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[12]).typography)('Heading', 75, 'Light'), {
        color: (0, _$$_REQUIRE(_dependencyMap[11]).changeOpacity)(theme.centerChannelColor, 0.5),
        marginTop: 8
      })
    };
  });
  function ChannelInfoForm(_ref) {
    var channelType = _ref.channelType,
      displayName = _ref.displayName,
      onDisplayNameChange = _ref.onDisplayNameChange,
      editing = _ref.editing,
      error = _ref.error,
      header = _ref.header,
      headerOnly = _ref.headerOnly,
      onHeaderChange = _ref.onHeaderChange,
      onTypeChange = _ref.onTypeChange,
      purpose = _ref.purpose,
      onPurposeChange = _ref.onPurposeChange,
      saving = _ref.saving,
      type = _ref.type;
    var intl = (0, _$$_REQUIRE(_dependencyMap[13]).useIntl)();
    var formatMessage = intl.formatMessage;
    var theme = (0, _$$_REQUIRE(_dependencyMap[14]).useTheme)();
    var styles = getStyleSheet(theme);
    var nameInput = (0, _react.useRef)(null);
    var purposeInput = (0, _react.useRef)(null);
    var headerInput = (0, _react.useRef)(null);
    var scrollViewRef = (0, _react.useRef)(null);
    var updateScrollTimeout = (0, _react.useRef)();
    var mainView = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      wrapperHeight = _useState2[0],
      setWrapperHeight = _useState2[1];
    var keyboardOverlap = (0, _$$_REQUIRE(_dependencyMap[15]).useKeyboardOverlap)(mainView, wrapperHeight);
    var _useInputPropagation = (0, _$$_REQUIRE(_dependencyMap[16]).useInputPropagation)(),
      _useInputPropagation2 = (0, _slicedToArray2.default)(_useInputPropagation, 2),
      propagateValue = _useInputPropagation2[0],
      shouldProcessEvent = _useInputPropagation2[1];
    var keyboardHeight = (0, _$$_REQUIRE(_dependencyMap[15]).useKeyboardHeight)();
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      keyboardVisible = _useState4[0],
      setKeyBoardVisible = _useState4[1];
    var _useState5 = (0, _react.useState)(0),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      scrollPosition = _useState6[0],
      setScrollPosition = _useState6[1];
    var _useState7 = (0, _react.useState)(0),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      errorHeight = _useState8[0],
      setErrorHeight = _useState8[1];
    var _useState9 = (0, _react.useState)(0),
      _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
      displayNameFieldHeight = _useState10[0],
      setDisplayNameFieldHeight = _useState10[1];
    var _useState11 = (0, _react.useState)(0),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      makePrivateHeight = _useState12[0],
      setMakePrivateHeight = _useState12[1];
    var _useState13 = (0, _react.useState)(0),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      purposeFieldHeight = _useState14[0],
      setPurposeFieldHeight = _useState14[1];
    var _useState15 = (0, _react.useState)(0),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      headerFieldHeight = _useState16[0],
      setHeaderFieldHeight = _useState16[1];
    var _useState17 = (0, _react.useState)(0),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      headerPosition = _useState18[0],
      setHeaderPosition = _useState18[1];
    var optionalText = formatMessage({
      id: (0, _$$_REQUIRE(_dependencyMap[17]).t)('channel_modal.optional'),
      defaultMessage: '(optional)'
    });
    var labelDisplayName = formatMessage({
      id: (0, _$$_REQUIRE(_dependencyMap[17]).t)('channel_modal.name'),
      defaultMessage: 'Name'
    });
    var labelPurpose = formatMessage({
      id: (0, _$$_REQUIRE(_dependencyMap[17]).t)('channel_modal.purpose'),
      defaultMessage: 'Purpose'
    }) + ' ' + optionalText;
    var labelHeader = formatMessage({
      id: (0, _$$_REQUIRE(_dependencyMap[17]).t)('channel_modal.header'),
      defaultMessage: 'Header'
    }) + ' ' + optionalText;
    var placeholderDisplayName = formatMessage({
      id: (0, _$$_REQUIRE(_dependencyMap[17]).t)('channel_modal.nameEx'),
      defaultMessage: 'Bugs, Marketing'
    });
    var placeholderPurpose = formatMessage({
      id: (0, _$$_REQUIRE(_dependencyMap[17]).t)('channel_modal.purposeEx'),
      defaultMessage: 'A channel to file bugs and improvements'
    });
    var placeholderHeader = formatMessage({
      id: (0, _$$_REQUIRE(_dependencyMap[17]).t)('channel_modal.headerEx'),
      defaultMessage: 'Use Markdown to format header text'
    });
    var makePrivateLabel = formatMessage({
      id: (0, _$$_REQUIRE(_dependencyMap[17]).t)('channel_modal.makePrivate.label'),
      defaultMessage: 'Make Private'
    });
    var makePrivateDescription = formatMessage({
      id: (0, _$$_REQUIRE(_dependencyMap[17]).t)('channel_modal.makePrivate.description'),
      defaultMessage: 'When a channel is set to private, only invited team members can access and participate in that channel.'
    });
    var displayHeaderOnly = headerOnly || channelType === _$$_REQUIRE(_dependencyMap[18]).General.DM_CHANNEL || channelType === _$$_REQUIRE(_dependencyMap[18]).General.GM_CHANNEL;
    var showSelector = !displayHeaderOnly && !editing;
    var isPrivate = type === _$$_REQUIRE(_dependencyMap[18]).General.PRIVATE_CHANNEL;
    var handlePress = function handlePress() {
      var chtype = isPrivate ? _$$_REQUIRE(_dependencyMap[18]).General.OPEN_CHANNEL : _$$_REQUIRE(_dependencyMap[18]).General.PRIVATE_CHANNEL;
      onTypeChange(chtype);
    };
    var blur = (0, _react.useCallback)(function () {
      var _nameInput$current, _purposeInput$current, _headerInput$current, _scrollViewRef$curren;
      (_nameInput$current = nameInput.current) == null ? undefined : _nameInput$current.blur();
      (_purposeInput$current = purposeInput.current) == null ? undefined : _purposeInput$current.blur();
      (_headerInput$current = headerInput.current) == null ? undefined : _headerInput$current.blur();
      (_scrollViewRef$curren = scrollViewRef.current) == null ? undefined : _scrollViewRef$curren.scrollToPosition(0, 0, true);
    }, []);
    var scrollHeaderToTop = (0, _react.useCallback)(function () {
      if (scrollViewRef != null && scrollViewRef.current) {
        var _scrollViewRef$curren2;
        (_scrollViewRef$curren2 = scrollViewRef.current) == null ? undefined : _scrollViewRef$curren2.scrollToPosition(0, headerPosition);
      }
    }, [headerPosition]);
    var onScroll = (0, _react.useCallback)(function (e) {
      var pos = e.nativeEvent.contentOffset.y;
      if (updateScrollTimeout.current) {
        clearTimeout(updateScrollTimeout.current);
      }
      updateScrollTimeout.current = setTimeout(function () {
        setScrollPosition(pos);
        updateScrollTimeout.current = undefined;
      }, 200);
    }, []);
    (0, _react.useEffect)(function () {
      if (keyboardVisible && !keyboardHeight) {
        setKeyBoardVisible(false);
      }
      if (!keyboardVisible && keyboardHeight) {
        setKeyBoardVisible(true);
      }
    }, [keyboardHeight]);
    var onHeaderAutocompleteChange = (0, _react.useCallback)(function (value) {
      onHeaderChange(value);
      propagateValue(value);
    }, [onHeaderChange]);
    var onHeaderInputChange = (0, _react.useCallback)(function (value) {
      if (!shouldProcessEvent(value)) {
        return;
      }
      onHeaderChange(value);
    }, [onHeaderChange]);
    var onLayoutError = (0, _react.useCallback)(function (e) {
      setErrorHeight(e.nativeEvent.layout.height);
    }, []);
    var onLayoutMakePrivate = (0, _react.useCallback)(function (e) {
      setMakePrivateHeight(e.nativeEvent.layout.height);
    }, []);
    var onLayoutDisplayName = (0, _react.useCallback)(function (e) {
      setDisplayNameFieldHeight(e.nativeEvent.layout.height);
    }, []);
    var onLayoutPurpose = (0, _react.useCallback)(function (e) {
      setPurposeFieldHeight(e.nativeEvent.layout.height);
    }, []);
    var onLayoutHeader = (0, _react.useCallback)(function (e) {
      setHeaderFieldHeight(e.nativeEvent.layout.height);
      setHeaderPosition(e.nativeEvent.layout.y);
    }, []);
    var onLayoutWrapper = (0, _react.useCallback)(function (e) {
      setWrapperHeight(e.nativeEvent.layout.height);
    }, []);
    var otherElementsSize = LIST_PADDING + errorHeight + (showSelector ? makePrivateHeight + MAKE_PRIVATE_MARGIN_BOTTOM : 0) + (displayHeaderOnly ? 0 : purposeFieldHeight + FIELD_MARGIN_BOTTOM + displayNameFieldHeight + FIELD_MARGIN_BOTTOM);
    var workingSpace = wrapperHeight - keyboardOverlap;
    var spaceOnTop = otherElementsSize - scrollPosition - AUTOCOMPLETE_ADJUST;
    var spaceOnBottom = workingSpace + scrollPosition - (otherElementsSize + headerFieldHeight + BOTTOM_AUTOCOMPLETE_SEPARATION);
    var autocompletePosition = spaceOnBottom > spaceOnTop ? otherElementsSize + headerFieldHeight - scrollPosition : workingSpace + scrollPosition + AUTOCOMPLETE_ADJUST + keyboardOverlap - otherElementsSize;
    var autocompleteAvailableSpace = spaceOnBottom > spaceOnTop ? spaceOnBottom : spaceOnTop;
    var growDown = spaceOnBottom > spaceOnTop;
    var _useAutocompleteDefau = (0, _$$_REQUIRE(_dependencyMap[19]).useAutocompleteDefaultAnimatedValues)(autocompletePosition, autocompleteAvailableSpace),
      _useAutocompleteDefau2 = (0, _slicedToArray2.default)(_useAutocompleteDefau, 2),
      animatedAutocompletePosition = _useAutocompleteDefau2[0],
      animatedAutocompleteAvailableSpace = _useAutocompleteDefau2[1];
    if (saving) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.container,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
          containerStyle: styles.loading,
          color: theme.centerChannelColor,
          size: "large"
        })
      });
    }
    var displayError;
    if (error) {
      displayError = /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[20]).SafeAreaView, {
        edges: ['bottom', 'left', 'right'],
        style: styles.errorContainer,
        onLayout: onLayoutError,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.errorWrapper,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_error_text.default, {
            testID: "edit_channel_info.error.text",
            error: error
          })
        })
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[20]).SafeAreaView, {
      edges: ['bottom', 'left', 'right'],
      style: styles.container,
      testID: "create_or_edit_channel.screen",
      onLayout: onLayoutWrapper,
      ref: mainView,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[21]).KeyboardAwareScrollView, {
        testID: 'create_or_edit_channel.scroll_view',
        ref: scrollViewRef,
        keyboardShouldPersistTaps: 'always',
        enableAutomaticScroll: !keyboardVisible,
        contentContainerStyle: styles.scrollView,
        onScroll: onScroll,
        children: [displayError, /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
          onPress: blur,
          children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            children: [showSelector && /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
              testID: "channel_info_form.make_private",
              label: makePrivateLabel,
              description: makePrivateDescription,
              action: handlePress,
              type: 'toggle',
              selected: isPrivate,
              icon: 'lock-outline',
              containerStyle: styles.makePrivateContainer,
              onLayout: onLayoutMakePrivate
            }), !displayHeaderOnly && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
              children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_text_input_label.default, {
                autoCorrect: false,
                autoCapitalize: 'none',
                blurOnSubmit: false,
                disableFullscreenUI: true,
                enablesReturnKeyAutomatically: true,
                label: labelDisplayName,
                placeholder: placeholderDisplayName,
                onChangeText: onDisplayNameChange,
                maxLength: _$$_REQUIRE(_dependencyMap[18]).Channel.MAX_CHANNEL_NAME_LENGTH,
                keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[11]).getKeyboardAppearanceFromTheme)(theme),
                returnKeyType: "next",
                showErrorIcon: false,
                spellCheck: false,
                testID: "channel_info_form.display_name.input",
                value: displayName === "Off-Topic" ? "المجموعة الفرعية" : displayName === "Town Square" ? "المجموعة الرئيسية" : displayName,
                ref: nameInput,
                containerStyle: styles.fieldContainer,
                theme: theme,
                onLayout: onLayoutDisplayName
              }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: styles.fieldContainer,
                onLayout: onLayoutPurpose,
                children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_text_input_label.default, {
                  autoCorrect: false,
                  autoCapitalize: 'none',
                  blurOnSubmit: false,
                  disableFullscreenUI: true,
                  enablesReturnKeyAutomatically: true,
                  label: labelPurpose,
                  placeholder: placeholderPurpose,
                  onChangeText: onPurposeChange,
                  keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[11]).getKeyboardAppearanceFromTheme)(theme),
                  returnKeyType: "next",
                  showErrorIcon: false,
                  spellCheck: false,
                  testID: "channel_info_form.purpose.input",
                  value: purpose,
                  ref: purposeInput,
                  theme: theme
                }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
                  style: styles.helpText,
                  id: "channel_modal.descriptionHelp",
                  defaultMessage: "Describe how this channel should be used.",
                  testID: "channel_info_form.purpose.description"
                })]
              })]
            }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: styles.fieldContainer,
              children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_text_input_label.default, {
                autoCorrect: false,
                autoCapitalize: 'none',
                blurOnSubmit: false,
                disableFullscreenUI: true,
                enablesReturnKeyAutomatically: true,
                label: labelHeader,
                placeholder: placeholderHeader,
                onChangeText: onHeaderInputChange,
                multiline: true,
                keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[11]).getKeyboardAppearanceFromTheme)(theme),
                returnKeyType: "next",
                showErrorIcon: false,
                spellCheck: false,
                testID: "channel_info_form.header.input",
                value: header,
                ref: headerInput,
                theme: theme,
                onFocus: scrollHeaderToTop,
                onLayout: onLayoutHeader
              }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
                style: styles.helpText,
                id: "channel_modal.headerHelp",
                defaultMessage: 'Specify text to appear in the channel header beside the channel name. For example, include frequently used links by typing link text [Link Title](http://example.com).',
                testID: "channel_info_form.header.description"
              })]
            })]
          })
        })]
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_autocomplete.default, {
        position: animatedAutocompletePosition,
        updateValue: onHeaderAutocompleteChange,
        cursorPosition: header.length,
        value: header,
        nestedScrollEnabled: true,
        availableSpace: animatedAutocompleteAvailableSpace,
        inPost: false,
        growDown: growDown
      })]
    });
  }
