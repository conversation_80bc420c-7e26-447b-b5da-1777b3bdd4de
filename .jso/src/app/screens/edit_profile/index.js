  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _edit_profile = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var database = _ref.database;
    var ldapFirstNameAttributeSet = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'LdapFirstNameAttributeSet');
    var ldapLastNameAttributeSet = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'LdapLastNameAttributeSet');
    var ldapNicknameAttributeSet = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'LdapNicknameAttributeSet');
    var ldapPositionAttributeSet = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'LdapPositionAttributeSet');
    var samlFirstNameAttributeSet = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'SamlFirstNameAttributeSet');
    var samlLastNameAttributeSet = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'SamlLastNameAttributeSet');
    var samlNicknameAttributeSet = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'SamlNicknameAttributeSet');
    var samlPositionAttributeSet = (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'SamlPositionAttributeSet');
    return {
      currentUser: (0, _$$_REQUIRE(_dependencyMap[5]).observeCurrentUser)(database),
      lockedFirstName: (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([ldapFirstNameAttributeSet, samlFirstNameAttributeSet]).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
          ldap = _ref3[0],
          saml = _ref3[1];
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(ldap || saml);
      })),
      lockedLastName: (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([ldapLastNameAttributeSet, samlLastNameAttributeSet]).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref4) {
        var _ref5 = (0, _slicedToArray2.default)(_ref4, 2),
          ldap = _ref5[0],
          saml = _ref5[1];
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(ldap || saml);
      })),
      lockedNickname: (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([ldapNicknameAttributeSet, samlNicknameAttributeSet]).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref6) {
        var _ref7 = (0, _slicedToArray2.default)(_ref6, 2),
          ldap = _ref7[0],
          saml = _ref7[1];
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(ldap || saml);
      })),
      lockedPosition: (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([ldapPositionAttributeSet, samlPositionAttributeSet]).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref8) {
        var _ref9 = (0, _slicedToArray2.default)(_ref8, 2),
          ldap = _ref9[0],
          saml = _ref9[1];
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(ldap || saml);
      })),
      lockedPicture: (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'LdapPictureAttributeSet')
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_edit_profile.default));
