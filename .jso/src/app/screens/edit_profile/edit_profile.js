  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _tablet_title = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _form = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _profile_error = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _updating = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _user_profile_picture = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var edges = ['bottom', 'left', 'right'];
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    },
    top: {
      marginVertical: 32,
      marginStart: 24,
      alignItems: 'center',
      justifyContent: 'center'
    }
  });
  var CLOSE_BUTTON_ID = 'close-edit-profile';
  var UPDATE_BUTTON_ID = 'update-profile';
  var EditProfile = function EditProfile(_ref) {
    var componentId = _ref.componentId,
      currentUser = _ref.currentUser,
      isModal = _ref.isModal,
      isTablet = _ref.isTablet,
      lockedFirstName = _ref.lockedFirstName,
      lockedLastName = _ref.lockedLastName,
      lockedNickname = _ref.lockedNickname,
      lockedPosition = _ref.lockedPosition,
      lockedPicture = _ref.lockedPicture;
    var intl = (0, _$$_REQUIRE(_dependencyMap[14]).useIntl)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[15]).useServerUrl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[16]).useTheme)();
    var changedProfilePicture = (0, _react.useRef)(undefined);
    var scrollViewRef = (0, _react.useRef)();
    var hasUpdateUserInfo = (0, _react.useRef)(false);
    var _useState = (0, _react.useState)({
        email: (currentUser == null ? undefined : currentUser.email) || '',
        firstName: (currentUser == null ? undefined : currentUser.firstName) || '',
        lastName: (currentUser == null ? undefined : currentUser.lastName) || '',
        nickname: (currentUser == null ? undefined : currentUser.nickname) || '',
        position: (currentUser == null ? undefined : currentUser.position) || '',
        username: (currentUser == null ? undefined : currentUser.username) || ''
      }),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      userInfo = _useState2[0],
      setUserInfo = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      canSave = _useState4[0],
      setCanSave = _useState4[1];
    var _useState5 = (0, _react.useState)(),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      error = _useState6[0],
      setError = _useState6[1];
    var _useState7 = (0, _react.useState)(),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      usernameError = _useState8[0],
      setUsernameError = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
      updating = _useState10[0],
      setUpdating = _useState10[1];
    var buttonText = intl.formatMessage({
      id: 'mobile.account.settings.save',
      defaultMessage: 'Save'
    });
    var rightButton = (0, _react.useMemo)(function () {
      return isTablet ? null : {
        id: CLOSE_BUTTON_ID,
        icon: _compass_icon.default.getImageSourceSync('chevron-right', 24, theme.centerChannelColor),
        testID: 'close.edit_profile.button',
        fontFamily: 'IBMPlexSansArabic-Regular'
      };
    }, [isTablet, theme.centerChannelColor]);
    var leftButton = (0, _react.useMemo)(function () {
      return isTablet ? null : {
        id: 'update-profile',
        enabled: false,
        showAsAction: 'always',
        testID: 'edit_profile.save.button',
        color: theme.buttonBg,
        text: buttonText,
        fontFamily: 'IBMPlexSansArabic-Regular'
      };
    }, [isTablet, theme.buttonBg]);
    (0, _react.useEffect)(function () {
      if (!isTablet) {
        (0, _$$_REQUIRE(_dependencyMap[17]).setButtons)(componentId, {
          rightButtons: [rightButton],
          leftButtons: [leftButton]
        });
      }
    }, []);
    var close = (0, _react.useCallback)(function () {
      if (isModal) {
        (0, _$$_REQUIRE(_dependencyMap[17]).dismissModal)({
          componentId: componentId
        });
      } else if (isTablet) {
        _reactNative.DeviceEventEmitter.emit(_$$_REQUIRE(_dependencyMap[18]).Events.ACCOUNT_SELECT_TABLET_VIEW, '');
      } else {
        (0, _$$_REQUIRE(_dependencyMap[17]).popTopScreen)(componentId);
      }
    }, []);
    var enableSaveButton = (0, _react.useCallback)(function (value) {
      if (!isTablet) {
        var buttons = {
          leftButtons: [Object.assign({}, leftButton, {
            enabled: value
          })]
        };
        (0, _$$_REQUIRE(_dependencyMap[17]).setButtons)(componentId, buttons);
      }
      setCanSave(value);
    }, [componentId, leftButton]);
    var submitUser = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[19]).preventDoubleTap)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!currentUser) {
        return;
      }
      enableSaveButton(false);
      setError(undefined);
      setUpdating(true);
      try {
        var _changedProfilePictur, _changedProfilePictur2;
        var newUserInfo = {
          email: userInfo.email.trim(),
          first_name: userInfo.firstName.trim(),
          last_name: userInfo.lastName.trim(),
          nickname: userInfo.nickname.trim(),
          position: userInfo.position.trim(),
          username: userInfo.username.trim()
        };
        var localPath = (_changedProfilePictur = changedProfilePicture.current) == null ? undefined : _changedProfilePictur.localPath;
        var profileImageRemoved = (_changedProfilePictur2 = changedProfilePicture.current) == null ? undefined : _changedProfilePictur2.isRemoved;
        if (localPath) {
          var now = Date.now();
          var _yield$uploadUserProf = yield (0, _$$_REQUIRE(_dependencyMap[20]).uploadUserProfileImage)(serverUrl, localPath),
            uploadError = _yield$uploadUserProf.error;
          if (uploadError) {
            resetScreen(uploadError);
            return;
          }
          (0, _$$_REQUIRE(_dependencyMap[21]).updateLocalUser)(serverUrl, {
            last_picture_update: now
          });
        } else if (profileImageRemoved) {
          yield (0, _$$_REQUIRE(_dependencyMap[20]).setDefaultProfileImage)(serverUrl, currentUser.id);
        }
        if (hasUpdateUserInfo.current) {
          var _yield$updateMe = yield (0, _$$_REQUIRE(_dependencyMap[20]).updateMe)(serverUrl, newUserInfo),
            reqError = _yield$updateMe.error;
          if (reqError) {
            resetScreenForProfileError(reqError);
            return;
          }
        }
        close();
      } catch (e) {
        resetScreen(e);
      }
    })), [userInfo, enableSaveButton]);
    (0, _android_back_handler.default)(componentId, close);
    (0, _navigation_button_pressed.default)(UPDATE_BUTTON_ID, componentId, submitUser, [userInfo]);
    (0, _navigation_button_pressed.default)(CLOSE_BUTTON_ID, componentId, close, []);
    var onUpdateProfilePicture = (0, _react.useCallback)(function (newProfileImage) {
      changedProfilePicture.current = newProfileImage;
      enableSaveButton(true);
    }, [enableSaveButton]);
    var onUpdateField = (0, _react.useCallback)(function (fieldKey, name) {
      var update = Object.assign({}, userInfo);
      update[fieldKey] = name;
      setUserInfo(update);
      console.log(`\n\nthis to check the condtion ${((userInfo == null ? undefined : userInfo.firstName.length) > 0 && (userInfo == null ? undefined : userInfo.lastName.length) > 0 && (userInfo == null ? undefined : userInfo.nickname.length) > 0 && (userInfo == null ? undefined : userInfo.position.length) > 0 && (userInfo == null ? undefined : userInfo.username.length) > 0) === true}\n\n`);
      // @ts-expect-error access object property by string key
      var currentValue = currentUser[fieldKey];
      var didChange = currentValue !== name && ((userInfo == null ? undefined : userInfo.firstName.length) > 0 && (userInfo == null ? undefined : userInfo.lastName.length) > 0 && (userInfo == null ? undefined : userInfo.nickname.length) > 0 && (userInfo == null ? undefined : userInfo.position.length) > 0 && (userInfo == null ? undefined : userInfo.username.length) > 0) === true;
      hasUpdateUserInfo.current = currentValue !== name;
      enableSaveButton(didChange);
    }, [userInfo, currentUser, enableSaveButton]);
    var resetScreenForProfileError = (0, _react.useCallback)(function (resetError) {
      setUsernameError(resetError);
      _reactNative.Keyboard.dismiss();
      setUpdating(false);
      enableSaveButton(true);
    }, [enableSaveButton]);
    var resetScreen = (0, _react.useCallback)(function (resetError) {
      var _scrollViewRef$curren;
      setError(resetError);
      _reactNative.Keyboard.dismiss();
      setUpdating(false);
      enableSaveButton(true);
      (_scrollViewRef$curren = scrollViewRef.current) == null ? undefined : _scrollViewRef$curren.scrollToPosition(0, 0, true);
    }, [enableSaveButton]);
    var content = currentUser ? /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[22]).KeyboardAwareScrollView, {
      bounces: false,
      enableAutomaticScroll: _reactNative.Platform.select({
        ios: true,
        default: false
      }),
      enableOnAndroid: true,
      enableResetScrollToCoords: true,
      extraScrollHeight: _reactNative.Platform.select({
        ios: 45
      }),
      keyboardOpeningTime: 0,
      keyboardDismissMode: "none",
      keyboardShouldPersistTaps: "handled",
      scrollToOverflowEnabled: true,
      testID: "edit_profile.scroll_view",
      style: styles.flex,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: {
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginHorizontal: 15
        },
        children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: {
            flexDirection: 'row',
            alignItems: 'center'
          },
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[23]).TouchableOpacity, {
            onPress: close,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
              name: "chevron-right",
              color: theme.sidebarText,
              size: 28
            })
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[24]).typography)("Heading", 100, "Light"), {
              color: theme.sidebarText
            }),
            children: intl.formatMessage({
              id: 'edit.user.info',
              defaultMessage: 'edit user info'
            })
          })]
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[23]).TouchableOpacity, {
          disabled: canSave,
          onPress: submitUser,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[24]).typography)("Heading", 100, "Light"), {
              color: theme.buttonBg
            }),
            children: "حفظ"
          })
        })]
      }), updating && /*#__PURE__*/(0, _jsxRuntime.jsx)(_updating.default, {}), Boolean(error) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_profile_error.default, {
        error: error
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.top,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_user_profile_picture.default, {
          currentUser: currentUser,
          lockedPicture: lockedPicture,
          onUpdateProfilePicture: onUpdateProfilePicture
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_form.default, {
        canSave: canSave,
        currentUser: currentUser,
        isTablet: isTablet,
        lockedFirstName: lockedFirstName,
        lockedLastName: lockedLastName,
        lockedNickname: lockedNickname,
        lockedPosition: lockedPosition,
        error: usernameError,
        onUpdateField: onUpdateField,
        userInfo: userInfo,
        submitUser: submitUser
      })]
    }) : null;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [isTablet && /*#__PURE__*/(0, _jsxRuntime.jsx)(_tablet_title.default, {
        action: buttonText,
        enabled: canSave,
        onPress: submitUser,
        testID: "edit_profile",
        title: intl.formatMessage({
          id: 'mobile.screen.your_profile',
          defaultMessage: 'Your Profile'
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[25]).SafeAreaView, {
        edges: edges,
        style: styles.flex,
        testID: "edit_profile.screen",
        children: content
      })]
    });
  };
  var _default = exports.default = EditProfile;
