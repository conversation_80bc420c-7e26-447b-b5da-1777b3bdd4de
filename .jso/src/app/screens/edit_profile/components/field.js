  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _floating_text_input_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var _excluded = ["autoCapitalize", "autoCorrect", "fieldKey", "isDisabled", "isOptional", "keyboardType", "label", "maxLength", "onTextChange", "testID", "value", "fieldRef", "error", "onFocusNextField"]; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      viewContainer: {
        marginVertical: 8,
        alignItems: 'center',
        width: '100%'
      },
      disabledStyle: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.04)
      }
    };
  });
  var Field = function Field(_ref) {
    var _ref$autoCapitalize = _ref.autoCapitalize,
      autoCapitalize = _ref$autoCapitalize === undefined ? 'none' : _ref$autoCapitalize,
      _ref$autoCorrect = _ref.autoCorrect,
      autoCorrect = _ref$autoCorrect === undefined ? false : _ref$autoCorrect,
      fieldKey = _ref.fieldKey,
      _ref$isDisabled = _ref.isDisabled,
      isDisabled = _ref$isDisabled === undefined ? false : _ref$isDisabled,
      _ref$isOptional = _ref.isOptional,
      isOptional = _ref$isOptional === undefined ? false : _ref$isOptional,
      _ref$keyboardType = _ref.keyboardType,
      keyboardType = _ref$keyboardType === undefined ? 'default' : _ref$keyboardType,
      label = _ref.label,
      maxLength = _ref.maxLength,
      onTextChange = _ref.onTextChange,
      testID = _ref.testID,
      value = _ref.value,
      fieldRef = _ref.fieldRef,
      error = _ref.error,
      onFocusNextField = _ref.onFocusNextField,
      props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[8]).useIntl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[9]).useIsTablet)();
    var onChangeText = (0, _react.useCallback)(function (text) {
      return onTextChange(fieldKey, text);
    }, [fieldKey, onTextChange]);
    var onSubmitEditing = (0, _react.useCallback)(function () {
      onFocusNextField(fieldKey);
    }, [fieldKey, onFocusNextField]);
    var style = getStyleSheet(theme);
    var keyboard = _reactNative.Platform.OS === 'android' && keyboardType === 'url' ? 'default' : keyboardType;
    var optionalText = intl.formatMessage({
      id: 'channel_modal.optional',
      defaultMessage: '(optional)'
    });
    var formattedLabel = isOptional ? `${label} ${optionalText}` : label;
    var subContainer = [style.viewContainer, {
      paddingHorizontal: isTablet ? 42 : 20
    }];
    var fieldInputTestId = isDisabled ? `${testID}.input.disabled` : `${testID}.input`;
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      testID: testID,
      style: subContainer,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_text_input_label.default, Object.assign({
        autoCapitalize: autoCapitalize,
        autoCorrect: autoCorrect,
        disableFullscreenUI: true,
        editable: !isDisabled,
        keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[6]).getKeyboardAppearanceFromTheme)(theme),
        keyboardType: keyboard,
        label: formattedLabel,
        maxLength: maxLength,
        onChangeText: onChangeText,
        testID: fieldInputTestId,
        theme: theme,
        error: error,
        value: value,
        ref: fieldRef,
        onSubmitEditing: onSubmitEditing
      }, props))
    });
  };
  var _default = exports.default = (0, _react.memo)(Field);
