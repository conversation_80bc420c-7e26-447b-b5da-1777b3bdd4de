  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _profile_picture = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _edit_profile_picture = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var UserProfilePicture = function UserProfilePicture(_ref) {
    var currentUser = _ref.currentUser,
      lockedPicture = _ref.lockedPicture,
      onUpdateProfilePicture = _ref.onUpdateProfilePicture;
    if (lockedPicture) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_profile_picture.default, {
        author: currentUser,
        size: _$$_REQUIRE(_dependencyMap[5]).USER_PROFILE_PICTURE_SIZE,
        showStatus: false,
        testID: `edit_profile.${currentUser.id}.profile_picture`
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_edit_profile_picture.default, {
      onUpdateProfilePicture: onUpdateProfilePicture,
      user: currentUser
    });
  };
  var _default = exports.default = UserProfilePicture;
