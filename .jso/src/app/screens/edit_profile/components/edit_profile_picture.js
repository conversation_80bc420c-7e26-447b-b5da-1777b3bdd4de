  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _profile_picture = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _did_update = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _profile_image_picker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var SIZE = 128;
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[8]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        alignItems: 'center',
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.centerChannelColor, 0.08),
        borderRadius: 64,
        height: SIZE,
        justifyContent: 'center',
        width: SIZE
      },
      camera: {
        position: 'absolute',
        overflow: 'hidden',
        height: '100%',
        width: '100%'
      }
    };
  });
  var EditProfilePicture = function EditProfilePicture(_ref) {
    var user = _ref.user,
      onUpdateProfilePicture = _ref.onUpdateProfilePicture;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[9]).useServerUrl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var _useState = (0, _react.useState)(function () {
        return (0, _$$_REQUIRE(_dependencyMap[11]).buildProfileImageUrlFromUser)(serverUrl, user);
      }),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      pictureUrl = _useState2[0],
      setPictureUrl = _useState2[1];
    var styles = getStyleSheet(theme);
    (0, _did_update.default)(function () {
      var url = user.id ? (0, _$$_REQUIRE(_dependencyMap[11]).buildProfileImageUrlFromUser)(serverUrl, user) : undefined;
      if (url !== pictureUrl) {
        setPictureUrl(url);
      }
    }, [user]);
    var handleProfileImage = (0, _react.useCallback)(function (images) {
      var _images$;
      var isRemoved = true;
      var localPath;
      var pUrl = _$$_REQUIRE(_dependencyMap[12]).ACCOUNT_OUTLINE_IMAGE;
      var newImage = images == null ? undefined : (_images$ = images[0]) == null ? undefined : _images$.localPath;
      if (newImage) {
        isRemoved = false;
        localPath = newImage;
        pUrl = newImage;
      }
      setPictureUrl(pUrl);
      onUpdateProfilePicture({
        isRemoved: isRemoved,
        localPath: localPath
      });
    }, [onUpdateProfilePicture]);
    var pictureSource = (0, _react.useMemo)(function () {
      if (pictureUrl === _$$_REQUIRE(_dependencyMap[12]).ACCOUNT_OUTLINE_IMAGE) {
        return pictureUrl;
      } else if (pictureUrl) {
        var prefix = '';
        if (pictureUrl.includes('/api/')) {
          prefix = serverUrl;
        } else if (_reactNative.Platform.OS === 'android' && !pictureUrl.startsWith('content://') && !pictureUrl.startsWith('http://') && !pictureUrl.startsWith('https://') && !pictureUrl.startsWith('file://')) {
          prefix = 'file://';
        }
        return {
          uri: `${prefix}${pictureUrl}`
        };
      }
      return undefined;
    }, [pictureUrl]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      testID: `edit_profile.${user.id}.profile_picture`,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_profile_picture.default, {
        size: SIZE,
        source: pictureSource,
        author: user,
        showStatus: false
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.camera,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_profile_image_picker.default, {
          onRemoveProfileImage: handleProfileImage,
          uploadFiles: handleProfileImage,
          user: user
        })
      })]
    });
  };
  var _default = exports.default = EditProfilePicture;
