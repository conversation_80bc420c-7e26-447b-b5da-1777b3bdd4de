  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNativeDocumentPicker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _slide_up_panel_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var PanelItem = function PanelItem(_ref) {
    var pickerAction = _ref.pickerAction,
      pictureUtils = _ref.pictureUtils,
      onRemoveProfileImage = _ref.onRemoveProfileImage;
    var intl = (0, _$$_REQUIRE(_dependencyMap[6]).useIntl)();
    var panelTypes = (0, _react.useMemo)(function () {
      return {
        takePhoto: {
          icon: 'camera-outline',
          onPress: function () {
            var _onPress = (0, _asyncToGenerator2.default)(function* () {
              yield (0, _$$_REQUIRE(_dependencyMap[7]).dismissBottomSheet)();
              pictureUtils == null ? undefined : pictureUtils.attachFileFromCamera();
            });
            function onPress() {
              return _onPress.apply(this, arguments);
            }
            return onPress;
          }(),
          testID: 'attachment.takePhoto',
          text: {
            id: 'mobile.camera_type.photo.option',
            defaultMessage: 'Take Photo'
          }
        },
        browsePhotoLibrary: {
          icon: 'file-generic-outline',
          onPress: function () {
            var _onPress2 = (0, _asyncToGenerator2.default)(function* () {
              yield (0, _$$_REQUIRE(_dependencyMap[7]).dismissBottomSheet)();
              pictureUtils == null ? undefined : pictureUtils.attachFileFromPhotoGallery();
            });
            function onPress() {
              return _onPress2.apply(this, arguments);
            }
            return onPress;
          }(),
          testID: 'attachment.browsePhotoLibrary',
          text: {
            id: 'attachment.browsePhotoLibrary',
            defaultMessage: 'Photo Library'
          }
        },
        browseFiles: {
          icon: 'file-multiple-outline',
          onPress: function () {
            var _onPress3 = (0, _asyncToGenerator2.default)(function* () {
              yield (0, _$$_REQUIRE(_dependencyMap[7]).dismissBottomSheet)();
              pictureUtils == null ? undefined : pictureUtils.attachFileFromFiles(_reactNativeDocumentPicker.default.types.images);
            });
            function onPress() {
              return _onPress3.apply(this, arguments);
            }
            return onPress;
          }(),
          testID: 'attachment.browseFiles',
          text: {
            id: 'attachment.browseFiles',
            defaultMessage: 'Browse Files'
          }
        },
        removeProfilePicture: {
          icon: 'trash-can-outline',
          onPress: function () {
            var _onPress4 = (0, _asyncToGenerator2.default)(function* () {
              yield (0, _$$_REQUIRE(_dependencyMap[7]).dismissBottomSheet)();
              return onRemoveProfileImage && onRemoveProfileImage();
            });
            function onPress() {
              return _onPress4.apply(this, arguments);
            }
            return onPress;
          }(),
          testID: 'attachment.removeImage',
          text: {
            id: 'attachment.removeImage',
            defaultMessage: 'Remove Photo'
          }
        }
      };
    }, [pictureUtils, onRemoveProfileImage]);
    var item = panelTypes[pickerAction];
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_slide_up_panel_item.default, {
      leftIcon: item.icon,
      onPress: item.onPress,
      testID: item.testID,
      text: intl.formatMessage(item.text),
      destructive: pickerAction === 'removeProfilePicture'
    });
  };
  var _default = exports.default = PanelItem;
