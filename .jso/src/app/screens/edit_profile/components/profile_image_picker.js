  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _panel_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _file_picker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var hitSlop = {
    top: 100,
    bottom: 20,
    right: 20,
    left: 100
  };
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      touchable: {
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: theme.centerChannelBg,
        borderRadius: 18,
        height: 36,
        width: 36,
        position: 'absolute',
        bottom: 0,
        right: 0,
        backgroundColor: theme.centerChannelBg
      },
      title: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 600, 'SemiBold'), {
        color: theme.centerChannelColor,
        marginBottom: 8
      })
    };
  });
  var hasPictureUrl = function hasPictureUrl(user, serverUrl) {
    // Check if image url includes query string for timestamp. If so,
    // it means the image has been updated from the default, i.e. '.../image?_=1544159746868'
    return (0, _$$_REQUIRE(_dependencyMap[9]).buildProfileImageUrlFromUser)(serverUrl, user).includes('image?_');
  };
  var ProfileImagePicker = function ProfileImagePicker(_ref) {
    var onRemoveProfileImage = _ref.onRemoveProfileImage,
      uploadFiles = _ref.uploadFiles,
      user = _ref.user;
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[11]).useIntl)();
    var _useSafeAreaInsets = (0, _$$_REQUIRE(_dependencyMap[12]).useSafeAreaInsets)(),
      bottom = _useSafeAreaInsets.bottom;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[13]).useServerUrl)();
    var pictureUtils = (0, _react.useMemo)(function () {
      return new _file_picker.default(intl, uploadFiles);
    }, [uploadFiles, intl]);
    var canRemovePicture = hasPictureUrl(user, serverUrl);
    var styles = getStyleSheet(theme);
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[14]).useIsTablet)();
    var showFileAttachmentOptions = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[15]).preventDoubleTap)(function () {
      var renderContent = function renderContent() {
        return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [!isTablet && /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
            id: "user.edit_profile.profile_photo.change_photo",
            defaultMessage: "Change profile photo",
            style: styles.title
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_panel_item.default, {
            pickerAction: "takePhoto",
            pictureUtils: pictureUtils
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_panel_item.default, {
            pickerAction: "browsePhotoLibrary",
            pictureUtils: pictureUtils
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_panel_item.default, {
            pickerAction: "browseFiles",
            pictureUtils: pictureUtils
          }), canRemovePicture && /*#__PURE__*/(0, _jsxRuntime.jsx)(_panel_item.default, {
            pickerAction: "removeProfilePicture",
            onRemoveProfileImage: onRemoveProfileImage
          })]
        });
      };
      var snapPoint = (0, _$$_REQUIRE(_dependencyMap[16]).bottomSheetSnapPoint)(4, _$$_REQUIRE(_dependencyMap[17]).ITEM_HEIGHT, bottom) + _$$_REQUIRE(_dependencyMap[18]).TITLE_HEIGHT;
      return (0, _$$_REQUIRE(_dependencyMap[19]).bottomSheet)({
        closeButtonId: 'close-edit-profile',
        renderContent: renderContent,
        snapPoints: [1, snapPoint],
        title: 'Change profile photo',
        theme: theme
      });
    }), [canRemovePicture, onRemoveProfileImage, bottom, pictureUtils, theme]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: showFileAttachmentOptions,
      hitSlop: hitSlop,
      style: styles.touchable,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[20]).PencilSquareIcon, {
        size: 26,
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.sidebarText, 1)
      })
    });
  };
  var _default = exports.default = ProfileImagePicker;
