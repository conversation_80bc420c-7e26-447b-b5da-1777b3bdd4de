  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[2]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[3]).makeStyleSheetFromTheme)(function (theme) {
    return {
      text: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[4]).typography)('Heading', 75), {
        color: (0, _$$_REQUIRE(_dependencyMap[3]).changeOpacity)(theme.centerChannelColor, 0.9)
      })
    };
  });
  var DisabledFields = function DisabledFields(_ref) {
    var isTablet = _ref.isTablet;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[5]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var theme = (0, _$$_REQUIRE(_dependencyMap[6]).useTheme)();
    var styles = getStyleSheet(theme);
    var containerStyle = (0, _react.useMemo)(function () {
      return {
        paddingHorizontal: isTablet ? 42 : 20,
        marginBottom: 16
      };
    }, [isTablet]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: containerStyle,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: [styles.text, {
          textAlign: 'left'
        }],
        children: formatMessage({
          id: 'user.settings.general.field_handled_externally',
          defaultMessage: 'Some fields below are handled through your login provider. If you want to change them, you’ll need to do so through your login provider.'
        })
      })
    });
  };
  var _default = exports.default = DisabledFields;
