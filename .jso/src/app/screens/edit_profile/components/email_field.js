  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _field = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var services = {
    gitlab: 'GitLab',
    google: 'Google Apps',
    office365: 'Office 365',
    ldap: 'AD/LDAP',
    saml: 'SAML'
  };
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        marginTop: 2
      },
      text: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 75), {
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.5)
      })
    };
  });
  var EmailField = function EmailField(_ref) {
    var authService = _ref.authService,
      email = _ref.email,
      fieldRef = _ref.fieldRef,
      onChange = _ref.onChange,
      onFocusNextField = _ref.onFocusNextField,
      isDisabled = _ref.isDisabled,
      label = _ref.label,
      theme = _ref.theme,
      isTablet = _ref.isTablet;
    var intl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)();
    var service = services[authService];
    var style = getStyleSheet(theme);
    var fieldDescription;
    if (service) {
      fieldDescription = intl.formatMessage({
        id: 'user.edit_profile.email.auth_service',
        defaultMessage: 'Login occurs through {service}. Email cannot be updated. Email address used for notifications is {email}.'
      }, {
        email: email,
        service: service
      });
    } else {
      fieldDescription = intl.formatMessage({
        id: 'user.edit_profile.email.web_client',
        defaultMessage: 'Email must be updated using a web client or desktop application.'
      }, {
        email: email,
        service: service
      });
    }
    var descContainer = [style.container, {
      paddingHorizontal: isTablet ? 42 : 20
    }];
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_field.default, {
        blurOnSubmit: false,
        enablesReturnKeyAutomatically: true,
        fieldKey: "email",
        fieldRef: fieldRef,
        isDisabled: isDisabled,
        keyboardType: "email-address",
        label: label,
        onFocusNextField: onFocusNextField,
        onTextChange: onChange,
        returnKeyType: "next",
        testID: "edit_profile_form.email",
        value: email
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: descContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [style.text, {
            textAlign: 'left'
          }],
          testID: "edit_profile_form.email.input.description",
          children: fieldDescription
        })
      })]
    });
  };
  var _default = exports.default = EmailField;
