  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _disabled_fields = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _email_field = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _field = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var includesSsoService = function includesSsoService(sso) {
    return ['gitlab', 'google', 'office365'].includes(sso);
  };
  var isSAMLOrLDAP = function isSAMLOrLDAP(protocol) {
    return ['ldap', 'saml'].includes(protocol);
  };
  var FIELDS = {
    firstName: {
      id: (0, _$$_REQUIRE(_dependencyMap[7]).t)('user.settings.general.firstName'),
      defaultMessage: 'First Name'
    },
    lastName: {
      id: (0, _$$_REQUIRE(_dependencyMap[7]).t)('user.settings.general.lastName'),
      defaultMessage: 'Last Name'
    },
    username: {
      id: (0, _$$_REQUIRE(_dependencyMap[7]).t)('user.settings.general.username'),
      defaultMessage: 'Username'
    },
    nickname: {
      id: (0, _$$_REQUIRE(_dependencyMap[7]).t)('user.settings.general.nickname'),
      defaultMessage: 'Nickname'
    },
    position: {
      id: (0, _$$_REQUIRE(_dependencyMap[7]).t)('user.settings.general.position'),
      defaultMessage: 'Position'
    },
    email: {
      id: (0, _$$_REQUIRE(_dependencyMap[7]).t)('user.settings.general.email'),
      defaultMessage: 'Email'
    }
  };
  var styles = _reactNative.StyleSheet.create({
    footer: {
      height: 40,
      width: '100%'
    },
    separator: {
      height: 15
    }
  });
  var ProfileForm = function ProfileForm(_ref) {
    var canSave = _ref.canSave,
      currentUser = _ref.currentUser,
      isTablet = _ref.isTablet,
      lockedFirstName = _ref.lockedFirstName,
      lockedLastName = _ref.lockedLastName,
      lockedNickname = _ref.lockedNickname,
      lockedPosition = _ref.lockedPosition,
      onUpdateField = _ref.onUpdateField,
      userInfo = _ref.userInfo,
      submitUser = _ref.submitUser,
      error = _ref.error;
    var theme = (0, _$$_REQUIRE(_dependencyMap[8]).useTheme)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[9]).useIntl)();
    var firstNameRef = (0, _react.useRef)(null);
    var lastNameRef = (0, _react.useRef)(null);
    var usernameRef = (0, _react.useRef)(null);
    var emailRef = (0, _react.useRef)(null);
    var nicknameRef = (0, _react.useRef)(null);
    var positionRef = (0, _react.useRef)(null);
    var formatMessage = intl.formatMessage;
    var errorMessage = error == null ? undefined : (0, _$$_REQUIRE(_dependencyMap[10]).getErrorMessage)(error, intl);
    var userProfileFields = (0, _react.useMemo)(function () {
      var service = currentUser.authService;
      return {
        firstName: {
          ref: firstNameRef,
          isDisabled: isSAMLOrLDAP(service) && lockedFirstName || includesSsoService(service)
        },
        lastName: {
          ref: lastNameRef,
          isDisabled: isSAMLOrLDAP(service) && lockedLastName || includesSsoService(service)
        },
        username: {
          ref: usernameRef,
          isDisabled: service !== ''
        },
        email: {
          ref: emailRef,
          isDisabled: true
        },
        nickname: {
          ref: nicknameRef,
          isDisabled: isSAMLOrLDAP(service) && lockedNickname
        },
        position: {
          ref: positionRef,
          isDisabled: isSAMLOrLDAP(service) && lockedPosition
        }
      };
    }, [lockedFirstName, lockedLastName, lockedNickname, lockedPosition, currentUser.authService]);
    var onFocusNextField = (0, _react.useCallback)(function (fieldKey) {
      var findNextField = function findNextField() {
        var fields = Object.keys(userProfileFields);
        var curIndex = fields.indexOf(fieldKey);
        var searchIndex = curIndex + 1;
        if (curIndex === -1 || searchIndex > fields.length) {
          return undefined;
        }
        var remainingFields = fields.slice(searchIndex);
        var nextFieldIndex = remainingFields.findIndex(function (f) {
          var field = userProfileFields[f];
          return !field.isDisabled;
        });
        if (nextFieldIndex === -1) {
          return {
            isLastEnabledField: true,
            nextField: undefined
          };
        }
        var fieldName = remainingFields[nextFieldIndex];
        return {
          isLastEnabledField: false,
          nextField: userProfileFields[fieldName]
        };
      };
      var next = findNextField();
      if (next != null && next.isLastEnabledField && canSave) {
        // performs form submission
        _reactNative.Keyboard.dismiss();
        submitUser();
      } else if (next != null && next.nextField) {
        var _next$nextField, _next$nextField$ref, _next$nextField$ref$c;
        next == null ? undefined : (_next$nextField = next.nextField) == null ? undefined : (_next$nextField$ref = _next$nextField.ref) == null ? undefined : (_next$nextField$ref$c = _next$nextField$ref.current) == null ? undefined : _next$nextField$ref$c.focus();
      } else {
        _reactNative.Keyboard.dismiss();
      }
    }, [canSave, userProfileFields]);
    var hasDisabledFields = Object.values(userProfileFields).filter(function (field) {
      return field.isDisabled;
    }).length > 0;
    var fieldConfig = {
      blurOnSubmit: false,
      enablesReturnKeyAutomatically: true,
      onFocusNextField: onFocusNextField,
      onTextChange: onUpdateField,
      returnKeyType: 'next'
    };
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [hasDisabledFields && /*#__PURE__*/(0, _jsxRuntime.jsx)(_disabled_fields.default, {
        isTablet: isTablet
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_field.default, Object.assign({
        fieldKey: "firstName",
        fieldRef: firstNameRef
        // isDisabled={userProfileFields.firstName.isDisabled}
        ,
        isDisabled: true,
        label: formatMessage(FIELDS.firstName),
        testID: "edit_profile_form.first_name",
        value: userInfo.firstName
      }, fieldConfig)), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.separator
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_field.default, Object.assign({
        fieldKey: "lastName",
        fieldRef: lastNameRef
        //isDisabled={userProfileFields.lastName.isDisabled}
        ,
        isDisabled: true,
        label: formatMessage(FIELDS.lastName),
        testID: "edit_profile_form.last_name",
        value: userInfo.lastName
      }, fieldConfig)), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.separator
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_field.default, Object.assign({
        fieldKey: "username",
        fieldRef: usernameRef,
        error: errorMessage,
        isDisabled: userProfileFields.username.isDisabled,
        label: formatMessage(FIELDS.username),
        maxLength: 22,
        testID: "edit_profile_form.username",
        value: userInfo.username
      }, fieldConfig)), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.separator
      }), userInfo.email && /*#__PURE__*/(0, _jsxRuntime.jsx)(_email_field.default, {
        authService: currentUser.authService
        // isDisabled={userProfileFields.email.isDisabled}
        ,
        isDisabled: true,
        email: userInfo.email,
        label: formatMessage(FIELDS.email),
        fieldRef: emailRef,
        onChange: onUpdateField,
        onFocusNextField: onFocusNextField,
        theme: theme,
        isTablet: Boolean(isTablet)
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.separator
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_field.default, Object.assign({
        fieldKey: "nickname",
        fieldRef: nicknameRef,
        isDisabled: userProfileFields.nickname.isDisabled
        //isDisabled={true}
        ,
        label: formatMessage(FIELDS.nickname),
        maxLength: 64,
        testID: "edit_profile_form.nickname",
        value: userInfo.nickname
      }, fieldConfig)), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.separator
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_field.default, Object.assign({
        fieldKey: "position",
        fieldRef: positionRef,
        isDisabled: userProfileFields.position.isDisabled,
        isOptional: true,
        label: formatMessage(FIELDS.position),
        maxLength: 128
      }, fieldConfig, {
        returnKeyType: "done",
        testID: "edit_profile_form.position",
        value: userInfo.position
      })), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.footer
      })]
    });
  };
  var _default = exports.default = ProfileForm;
