  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _error_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      errorContainer: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.errorTextColor, 0.08),
        width: '100%',
        maxHeight: 48,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row'
      },
      text: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 100), {
        color: theme.centerChannelColor
      }),
      icon: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.dndIndicator, 0.64)
      }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 300), {
        marginRight: 9
      })
    };
  });
  var ProfileError = function ProfileError(_ref) {
    var error = _ref.error;
    var theme = (0, _$$_REQUIRE(_dependencyMap[8]).useTheme)();
    var style = getStyleSheet(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: style.errorContainer,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        style: style.icon,
        size: 18,
        name: "alert-outline"
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_error_text.default, {
        testID: "edit_profile.error.text",
        error: error,
        textStyle: style.text
      })]
    });
  };
  var _default = exports.default = ProfileError;
