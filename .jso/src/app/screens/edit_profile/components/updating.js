  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    spinner: {
      position: 'absolute',
      width: '100%',
      height: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }
  });
  var Updating = function Updating() {
    var theme = (0, _$$_REQUIRE(_dependencyMap[5]).useTheme)();
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.spinner,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
        color: theme.buttonBg
      })
    });
  };
  var _default = exports.default = Updating;
