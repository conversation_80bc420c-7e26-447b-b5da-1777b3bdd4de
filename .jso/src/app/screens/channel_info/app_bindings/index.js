  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react2 = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _apps_manager = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var ChannelInfoAppBindings = function ChannelInfoAppBindings(_ref) {
    var channelId = _ref.channelId,
      teamId = _ref.teamId,
      dismissChannelInfo = _ref.dismissChannelInfo,
      serverUrl = _ref.serverUrl,
      bindings = _ref.bindings;
    var onCallResponse = (0, _react2.useCallback)(function (callResp, message) {
      (0, _$$_REQUIRE(_dependencyMap[6]).postEphemeralCallResponseForChannel)(serverUrl, callResp, message, channelId);
    }, [serverUrl, channelId]);
    var context = (0, _react2.useMemo)(function () {
      return {
        channel_id: channelId,
        team_id: teamId
      };
    }, [channelId, teamId]);
    var config = (0, _react2.useMemo)(function () {
      return {
        onSuccess: onCallResponse,
        onError: onCallResponse
      };
    }, [onCallResponse]);
    var handleBindingSubmit = (0, _$$_REQUIRE(_dependencyMap[7]).useAppBinding)(context, config);
    var onPress = (0, _react2.useCallback)((0, _$$_REQUIRE(_dependencyMap[8]).preventDoubleTap)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (binding) {
        var submitPromise = handleBindingSubmit(binding);
        yield dismissChannelInfo();
        var finish = yield submitPromise;
        yield finish();
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }()), [handleBindingSubmit]);
    var options = bindings.map(function (binding) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(BindingOptionItem, {
        binding: binding,
        onPress: onPress
      }, binding.app_id + binding.location);
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: options
    });
  };
  var BindingOptionItem = function BindingOptionItem(_ref3) {
    var binding = _ref3.binding,
      onPress = _ref3.onPress;
    var handlePress = (0, _react2.useCallback)((0, _$$_REQUIRE(_dependencyMap[8]).preventDoubleTap)(function () {
      onPress(binding);
    }), [binding, onPress]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      label: binding.label,
      icon: binding.icon,
      action: handlePress,
      type: "default",
      testID: `channel_info.options.app_binding.option.${binding.location}`
    });
  };
  var enhanced = (0, _$$_REQUIRE(_dependencyMap[9]).withObservables)([], function (ownProps) {
    var database = ownProps.database;
    var teamId = (0, _$$_REQUIRE(_dependencyMap[10]).observeCurrentTeamId)(database);
    var bindings = _apps_manager.default.observeBindings(ownProps.serverUrl, _$$_REQUIRE(_dependencyMap[11]).AppBindingLocations.CHANNEL_HEADER_ICON);
    return {
      teamId: teamId,
      bindings: bindings
    };
  });
  var _default = exports.default = _react2.default.memo((0, _$$_REQUIRE(_dependencyMap[9]).withDatabase)(enhanced(ChannelInfoAppBindings)));
