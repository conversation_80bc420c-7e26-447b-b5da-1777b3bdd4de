  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _channel_info_enable_calls = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _channel_actions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _convert_to_channel_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _channel_bookmarks = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _app_bindings = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _destructive_options = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _extra = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _options = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _title = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var edges = ['bottom', 'left', 'right'];
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[15]).makeStyleSheetFromTheme)(function (theme) {
    return {
      content: {
        paddingHorizontal: 20,
        paddingBottom: 16
      },
      flex: {
        flex: 1
      },
      separator: {
        height: 1,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[15]).changeOpacity)(theme.centerChannelColor, 0.08),
        marginVertical: 8
      }
    };
  });
  var ChannelInfo = function ChannelInfo(_ref) {
    var canAddBookmarks = _ref.canAddBookmarks,
      canEnableDisableCalls = _ref.canEnableDisableCalls,
      canManageMembers = _ref.canManageMembers,
      canManageSettings = _ref.canManageSettings,
      channelId = _ref.channelId,
      closeButtonId = _ref.closeButtonId,
      componentId = _ref.componentId,
      isBookmarksEnabled = _ref.isBookmarksEnabled,
      isCallsEnabledInChannel = _ref.isCallsEnabledInChannel,
      groupCallsAllowed = _ref.groupCallsAllowed,
      isConvertGMFeatureAvailable = _ref.isConvertGMFeatureAvailable,
      isCRTEnabled = _ref.isCRTEnabled,
      isGuestUser = _ref.isGuestUser,
      type = _ref.type;
    var theme = (0, _$$_REQUIRE(_dependencyMap[16]).useTheme)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[17]).useServerUrl)();
    var styles = getStyleSheet(theme);

    // NOTE: isCallsEnabledInChannel will be true/false (not undefined) based on explicit state + the DefaultEnabled system setting
    //   which comes from observeIsCallsEnabledInChannel
    var callsAvailable = isCallsEnabledInChannel;
    if (!groupCallsAllowed && type !== _$$_REQUIRE(_dependencyMap[18]).General.DM_CHANNEL) {
      callsAvailable = false;
    }
    var onPressed = (0, _react.useCallback)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[19]).dismissModal)({
        componentId: componentId
      });
    }, [componentId]);
    (0, _navigation_button_pressed.default)(closeButtonId, componentId, onPressed, [onPressed]);
    (0, _android_back_handler.default)(componentId, onPressed);
    var convertGMOptionAvailable = isConvertGMFeatureAvailable && type === _$$_REQUIRE(_dependencyMap[18]).General.GM_CHANNEL && !isGuestUser;
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[20]).SafeAreaView, {
      edges: edges,
      style: styles.flex,
      testID: "channel_info.screen",
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
        bounces: true,
        alwaysBounceVertical: false,
        contentContainerStyle: styles.content,
        testID: "channel_info.scroll_view",
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_title.default, {
          channelId: channelId,
          type: type
        }), isBookmarksEnabled && /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_bookmarks.default, {
          channelId: channelId,
          canAddBookmarks: canAddBookmarks,
          showInInfo: true
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_actions.default, {
          channelId: channelId,
          inModal: true,
          dismissChannelInfo: onPressed,
          callsEnabled: callsAvailable,
          testID: "channel_info.channel_actions",
          isFromHome: false
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_extra.default, {
          channelId: channelId
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.separator
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_options.default, {
          channelId: channelId,
          type: type,
          callsEnabled: callsAvailable,
          canManageMembers: canManageMembers,
          isCRTEnabled: isCRTEnabled,
          canManageSettings: canManageSettings
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.separator
        }), convertGMOptionAvailable && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_convert_to_channel_label.default, {
            channelId: channelId
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.separator
          })]
        }), canEnableDisableCalls && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_info_enable_calls.default, {
            channelId: channelId,
            enabled: isCallsEnabledInChannel
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.separator
          })]
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_app_bindings.default, {
          channelId: channelId,
          serverUrl: serverUrl,
          dismissChannelInfo: onPressed
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_destructive_options.default, {
          channelId: channelId,
          componentId: componentId,
          type: type
        })]
      })
    });
  };
  var _default = exports.default = ChannelInfo;
