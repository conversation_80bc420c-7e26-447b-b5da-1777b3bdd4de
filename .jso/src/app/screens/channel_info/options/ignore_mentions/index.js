  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _ignore_mentions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var isChannelMentionsIgnored = function isChannelMentionsIgnored(channelNotifyProps, userNotifyProps) {
    var ignoreChannelMentionsDefault = _$$_REQUIRE(_dependencyMap[3]).Channel.IGNORE_CHANNEL_MENTIONS_OFF;
    if (userNotifyProps != null && userNotifyProps.channel && userNotifyProps.channel === 'false') {
      ignoreChannelMentionsDefault = _$$_REQUIRE(_dependencyMap[3]).Channel.IGNORE_CHANNEL_MENTIONS_ON;
    }
    var ignoreChannelMentions = channelNotifyProps == null ? undefined : channelNotifyProps.ignore_channel_mentions;
    if (!ignoreChannelMentions || ignoreChannelMentions === _$$_REQUIRE(_dependencyMap[3]).Channel.IGNORE_CHANNEL_MENTIONS_DEFAULT) {
      ignoreChannelMentions = ignoreChannelMentionsDefault;
    }
    return ignoreChannelMentions !== _$$_REQUIRE(_dependencyMap[3]).Channel.IGNORE_CHANNEL_MENTIONS_OFF;
  };
  var enhanced = (0, _$$_REQUIRE(_dependencyMap[4]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var channel = (0, _$$_REQUIRE(_dependencyMap[5]).observeChannel)(database, channelId);
    var currentUser = (0, _$$_REQUIRE(_dependencyMap[6]).observeCurrentUser)(database);
    var settings = (0, _$$_REQUIRE(_dependencyMap[5]).observeChannelSettings)(database, channelId);
    var ignoring = currentUser.pipe((0, _$$_REQUIRE(_dependencyMap[7]).combineLatestWith)(settings), (0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        u = _ref3[0],
        s = _ref3[1];
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(isChannelMentionsIgnored(s == null ? undefined : s.notifyProps, u == null ? undefined : u.notifyProps));
    }));
    return {
      ignoring: ignoring,
      displayName: channel.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (c) {
        return (0, _$$_REQUIRE(_dependencyMap[8]).of)(c == null ? undefined : c.displayName);
      }))
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[4]).withDatabase)(enhanced(_ignore_mentions.default));
