  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _englishNumberToArabic = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var Members = function Members(_ref) {
    var displayName = _ref.displayName,
      channelId = _ref.channelId,
      count = _ref.count;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[6]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var title = formatMessage({
      id: 'channel_info.members',
      defaultMessage: 'Members'
    });
    var goToChannelMembers = (0, _$$_REQUIRE(_dependencyMap[8]).preventDoubleTap)(function () {
      var options = {
        topBar: {
          subtitle: {
            color: (0, _$$_REQUIRE(_dependencyMap[9]).changeOpacity)(theme.sidebarHeaderTextColor, 0.72),
            text: displayName === "Off-Topic" ? "المجموعة الفرعية" : displayName === "Town Square" ? "المجموعة الرئيسية" : displayName,
            fontFamily: 'IBMPlexSansArabic-Light'
          }
        }
      };
      (0, _$$_REQUIRE(_dependencyMap[10]).goToScreen)(_$$_REQUIRE(_dependencyMap[11]).Screens.MANAGE_CHANNEL_MEMBERS, title, {
        channelId: channelId
      }, options);
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      action: goToChannelMembers,
      label: title,
      icon: "account-multiple-outline",
      type: _reactNative.Platform.select({
        ios: 'arrow',
        default: 'default'
      }),
      info: (0, _englishNumberToArabic.default)(count),
      testID: "channel_info.options.members.option"
    });
  };
  var _default = exports.default = Members;
