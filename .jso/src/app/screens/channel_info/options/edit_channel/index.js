  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var EditChannel = function EditChannel(_ref) {
    var channelId = _ref.channelId;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[6]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var title = formatMessage({
      id: 'screens.channel_edit',
      defaultMessage: 'Edit Channel'
    });
    var goToEditChannel = (0, _$$_REQUIRE(_dependencyMap[7]).preventDoubleTap)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      (0, _$$_REQUIRE(_dependencyMap[8]).goToScreen)(_$$_REQUIRE(_dependencyMap[9]).Screens.CREATE_OR_EDIT_CHANNEL, title, {
        channelId: channelId
      }, {
        topBar: {
          visible: true
        }
      });
    }));
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      action: goToEditChannel,
      label: title,
      icon: "pencil-outline",
      type: _reactNative.Platform.select({
        ios: 'arrow',
        default: 'default'
      }),
      testID: "channel_info.options.edit_channel.option"
    });
  };
  var _default = exports.default = EditChannel;
