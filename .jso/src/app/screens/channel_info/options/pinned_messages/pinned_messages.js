  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _englishNumberToArabic = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var PinnedMessages = function PinnedMessages(_ref) {
    var channelId = _ref.channelId,
      count = _ref.count,
      displayName = _ref.displayName;
    var theme = (0, _$$_REQUIRE(_dependencyMap[6]).useTheme)();
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var title = formatMessage({
      id: 'channel_info.pinned_messages',
      defaultMessage: 'Pinned Messages'
    });
    var goToPinnedMessages = (0, _$$_REQUIRE(_dependencyMap[8]).preventDoubleTap)(function () {
      var options = {
        topBar: {
          title: {
            text: title,
            fontFamily: 'IBMPlexSansArabic-Light'
          },
          subtitle: {
            color: (0, _$$_REQUIRE(_dependencyMap[9]).changeOpacity)(theme.sidebarHeaderTextColor, 0.72),
            text: displayName === "Off-Topic" ? "المجموعة الفرعية" : displayName === "Town Square" ? "المجموعة الرئيسية" : displayName,
            fontFamily: 'IBMPlexSansArabic-Bold'
          }
        },
        layout: {
          componentBackgroundColor: theme.centerChannelBg
        },
        statusBar: {
          visible: true,
          backgroundColor: theme.sidebarBg
        }
      };
      (0, _$$_REQUIRE(_dependencyMap[10]).goToScreen)(_$$_REQUIRE(_dependencyMap[11]).Screens.PINNED_MESSAGES, title, {
        channelId: channelId
      }, options);
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      action: goToPinnedMessages,
      label: title,
      icon: "pin-outline",
      type: _reactNative.Platform.select({
        ios: 'arrow',
        default: 'default'
      }),
      info: (0, _englishNumberToArabic.default)(count),
      testID: "channel_info.options.pinned_messages.option"
    });
  };
  var _default = exports.default = PinnedMessages;
