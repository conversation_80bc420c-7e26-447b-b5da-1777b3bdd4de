  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _auto_follow_threads = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var channel = (0, _$$_REQUIRE(_dependencyMap[3]).observeChannel)(database, channelId);
    var settings = (0, _$$_REQUIRE(_dependencyMap[3]).observeChannelSettings)(database, channelId);
    var followedStatus = settings.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (s) {
      var _s$notifyProps;
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)((s == null ? undefined : (_s$notifyProps = s.notifyProps) == null ? undefined : _s$notifyProps.channel_auto_follow_threads) === _$$_REQUIRE(_dependencyMap[6]).Channel.CHANNEL_AUTO_FOLLOW_THREADS_TRUE);
    }));
    return {
      followedStatus: followedStatus,
      displayName: channel.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (c) {
        return (0, _$$_REQUIRE(_dependencyMap[5]).of)(c == null ? undefined : c.displayName);
      }))
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_auto_follow_threads.default));
