  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var AutoFollowThreads = function AutoFollowThreads(_ref) {
    var channelId = _ref.channelId,
      displayName = _ref.displayName,
      followedStatus = _ref.followedStatus;
    var _useState = (0, _react.useState)(followedStatus),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      autoFollow = _useState2[0],
      setAutoFollow = _useState2[1];
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[6]).useServerUrl)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)();
    var toggleFollow = (0, _$$_REQUIRE(_dependencyMap[8]).preventDoubleTap)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var props = {
        channel_auto_follow_threads: followedStatus ? _$$_REQUIRE(_dependencyMap[9]).CHANNEL_AUTO_FOLLOW_THREADS_FALSE : _$$_REQUIRE(_dependencyMap[9]).CHANNEL_AUTO_FOLLOW_THREADS_TRUE
      };
      setAutoFollow(function (v) {
        return !v;
      });
      var result = yield (0, _$$_REQUIRE(_dependencyMap[10]).updateChannelNotifyProps)(serverUrl, channelId, props);
      if (result != null && result.error) {
        (0, _$$_REQUIRE(_dependencyMap[11]).alertErrorWithFallback)(intl, result.error, (0, _$$_REQUIRE(_dependencyMap[7]).defineMessage)({
          id: 'channel_info.channel_auto_follow_threads_failed',
          defaultMessage: 'An error occurred trying to auto follow all threads in channel {displayName}'
        }), {
          displayName: displayName
        });
        setAutoFollow(function (v) {
          return !v;
        });
      }
    }));
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      action: toggleFollow,
      label: intl.formatMessage({
        id: 'channel_info.channel_auto_follow_threads',
        defaultMessage: 'Follow all threads in this channel'
      }),
      icon: "message-plus-outline",
      type: "toggle",
      selected: autoFollow,
      testID: `channel_info.options.channel_auto_follow_threads.option.toggled.${autoFollow}`
    });
  };
  var _default = exports.default = AutoFollowThreads;
