  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var AddMembers = function AddMembers(_ref) {
    var displayName = _ref.displayName,
      channelId = _ref.channelId;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[6]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var title = formatMessage({
      id: 'channel_info.add_members',
      defaultMessage: 'Add members'
    });
    var goToAddMembers = (0, _$$_REQUIRE(_dependencyMap[8]).preventDoubleTap)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      (0, _$$_REQUIRE(_dependencyMap[9]).goToScreen)(_$$_REQUIRE(_dependencyMap[10]).Screens.CHANNEL_ADD_MEMBERS, displayName === "Off-Topic" ? "المجموعة الفرعية" : displayName === "Town Square" ? "المجموعة الرئيسية" : displayName, {
        channelId: channelId,
        inModal: true
      }, {
        topBar: {
          visible: true,
          subtitle: {
            text: ''
          }
        }
      });
    }));
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      action: goToAddMembers,
      label: title,
      icon: "account-plus-outline",
      type: _reactNative.Platform.select({
        ios: 'arrow',
        default: 'default'
      }),
      testID: "channel_info.options.add_members.option"
    });
  };
  var _default = exports.default = AddMembers;
