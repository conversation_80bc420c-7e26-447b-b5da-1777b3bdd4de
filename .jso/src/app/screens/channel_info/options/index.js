  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _add_members = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _auto_follow_threads = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _channel_files = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _edit_channel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _ignore_mentions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _members = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _notification_preference = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _pinned_messages = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _copy_channel_link_optionss = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var Options = function Options(_ref) {
    var channelId = _ref.channelId,
      type = _ref.type,
      callsEnabled = _ref.callsEnabled,
      canManageMembers = _ref.canManageMembers,
      isCRTEnabled = _ref.isCRTEnabled,
      canManageSettings = _ref.canManageSettings;
    var isDMorGM = (0, _$$_REQUIRE(_dependencyMap[12]).isTypeDMorGM)(type);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [type !== _$$_REQUIRE(_dependencyMap[13]).General.DM_CHANNEL && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [isCRTEnabled && /*#__PURE__*/(0, _jsxRuntime.jsx)(_auto_follow_threads.default, {
          channelId: channelId
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_ignore_mentions.default, {
          channelId: channelId
        })]
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_notification_preference.default, {
        channelId: channelId
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_pinned_messages.default, {
        channelId: channelId
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_files.default, {
        channelId: channelId
      }), type !== _$$_REQUIRE(_dependencyMap[13]).General.DM_CHANNEL && /*#__PURE__*/(0, _jsxRuntime.jsx)(_members.default, {
        channelId: channelId
      }), canManageMembers && /*#__PURE__*/(0, _jsxRuntime.jsx)(_add_members.default, {
        channelId: channelId
      }), callsEnabled && !isDMorGM &&
      /*#__PURE__*/
      // if calls is not enabled, copy link will show in the channel actions
      (0, _jsxRuntime.jsx)(_copy_channel_link_optionss.default, {
        channelId: channelId,
        testID: "channel_info.options.copy_channel_link.option"
      }), canManageSettings && /*#__PURE__*/(0, _jsxRuntime.jsx)(_edit_channel.default, {
        channelId: channelId
      })]
    });
  };
  var _default = exports.default = Options;
