  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _channel_files = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var channel = (0, _$$_REQUIRE(_dependencyMap[3]).observeChannel)(database, channelId);
    var info = (0, _$$_REQUIRE(_dependencyMap[3]).observeChannelInfo)(database, channelId);
    var count = info.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (i) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)((i == null ? undefined : i.filesCount) || 0);
    }));
    var displayName = channel.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)(c == null ? undefined : c.displayName);
    }));
    return {
      count: count,
      displayName: displayName
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_channel_files.default));
