  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _notification_preference = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var channel = (0, _$$_REQUIRE(_dependencyMap[3]).observeChannel)(database, channelId);
    var channelType = channel.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)(c == null ? undefined : c.type);
    }));
    var displayName = channel.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)(c == null ? undefined : c.displayName);
    }));
    var settings = (0, _$$_REQUIRE(_dependencyMap[3]).observeChannelSettings)(database, channelId);
    var userNotifyLevel = (0, _$$_REQUIRE(_dependencyMap[6]).observeCurrentUser)(database).pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (u) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)((0, _$$_REQUIRE(_dependencyMap[7]).getNotificationProps)(u).push);
    }));
    var notifyLevel = settings.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (s) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)((s == null ? undefined : s.notifyProps.push) || _$$_REQUIRE(_dependencyMap[8]).NotificationLevel.DEFAULT);
    }));
    var hasGMasDMFeature = (0, _$$_REQUIRE(_dependencyMap[9]).observeHasGMasDMFeature)(database);
    return {
      displayName: displayName,
      notifyLevel: notifyLevel,
      userNotifyLevel: userNotifyLevel,
      channelType: channelType,
      hasGMasDMFeature: hasGMasDMFeature
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_notification_preference.default));
