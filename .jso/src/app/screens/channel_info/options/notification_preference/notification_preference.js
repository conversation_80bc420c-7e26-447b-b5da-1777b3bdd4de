  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var notificationLevel = function notificationLevel(notifyLevel) {
    var id = '';
    var defaultMessage = '';
    switch (notifyLevel) {
      case _$$_REQUIRE(_dependencyMap[5]).NotificationLevel.ALL:
        {
          id = (0, _$$_REQUIRE(_dependencyMap[6]).t)('channel_info.notification.all');
          defaultMessage = 'All';
          break;
        }
      case _$$_REQUIRE(_dependencyMap[5]).NotificationLevel.MENTION:
        {
          id = (0, _$$_REQUIRE(_dependencyMap[6]).t)('channel_info.notification.mention');
          defaultMessage = 'Mentions';
          break;
        }
      case _$$_REQUIRE(_dependencyMap[5]).NotificationLevel.NONE:
        {
          id = (0, _$$_REQUIRE(_dependencyMap[6]).t)('channel_info.notification.none');
          defaultMessage = 'Never';
          break;
        }
      default:
        id = (0, _$$_REQUIRE(_dependencyMap[6]).t)('channel_info.notification.default');
        defaultMessage = 'Default';
        break;
    }
    return {
      id: id,
      defaultMessage: defaultMessage
    };
  };
  var NotificationPreference = function NotificationPreference(_ref) {
    var channelId = _ref.channelId,
      displayName = _ref.displayName,
      notifyLevel = _ref.notifyLevel,
      userNotifyLevel = _ref.userNotifyLevel,
      channelType = _ref.channelType,
      hasGMasDMFeature = _ref.hasGMasDMFeature;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var theme = (0, _$$_REQUIRE(_dependencyMap[8]).useTheme)();
    var title = formatMessage({
      id: 'channel_info.mobile_notifications',
      defaultMessage: 'Mobile Notifications'
    });
    var goToChannelNotificationPreferences = (0, _$$_REQUIRE(_dependencyMap[9]).preventDoubleTap)(function () {
      var options = {
        topBar: {
          title: {
            text: title,
            fontFamily: 'IBMPlexSansArabic-Light'
          },
          subtitle: {
            color: (0, _$$_REQUIRE(_dependencyMap[10]).changeOpacity)(theme.sidebarHeaderTextColor, 0.72),
            text: displayName === "Off-Topic" ? "المجموعة الفرعية" : displayName === "Town Square" ? "المجموعة الرئيسية" : displayName,
            fontFamily: 'IBMPlexSansArabic-Light'
          },
          backButton: {
            popStackOnPress: false
          }
        },
        layout: {
          componentBackgroundColor: theme.centerChannelBg
        },
        statusBar: {
          visible: true,
          backgroundColor: theme.sidebarBg
        }
      };
      (0, _$$_REQUIRE(_dependencyMap[11]).goToScreen)(_$$_REQUIRE(_dependencyMap[5]).Screens.CHANNEL_NOTIFICATION_PREFERENCES, title, {
        channelId: channelId
      }, options);
    });
    var notificationLevelToText = function notificationLevelToText() {
      var notifyLevelToUse = notifyLevel;
      if (notifyLevelToUse === _$$_REQUIRE(_dependencyMap[5]).NotificationLevel.DEFAULT) {
        notifyLevelToUse = userNotifyLevel;
      }
      if (hasGMasDMFeature) {
        if (notifyLevel === _$$_REQUIRE(_dependencyMap[5]).NotificationLevel.DEFAULT && notifyLevelToUse === _$$_REQUIRE(_dependencyMap[5]).NotificationLevel.MENTION && (0, _$$_REQUIRE(_dependencyMap[12]).isTypeDMorGM)(channelType)) {
          notifyLevelToUse = _$$_REQUIRE(_dependencyMap[5]).NotificationLevel.ALL;
        }
      }
      var messageDescriptor = notificationLevel(notifyLevelToUse);
      return formatMessage(messageDescriptor);
    };
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      action: goToChannelNotificationPreferences,
      label: title,
      icon: "cellphone",
      type: _reactNative.Platform.select({
        ios: 'arrow',
        default: 'default'
      }),
      info: notificationLevelToText(),
      testID: "channel_info.options.notification_preference.option"
    });
  };
  var _default = exports.default = NotificationPreference;
