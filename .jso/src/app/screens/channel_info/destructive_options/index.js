  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _leave_channel_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _archive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _convert_private = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var DestructiveOptions = function DestructiveOptions(_ref) {
    var channelId = _ref.channelId,
      componentId = _ref.componentId,
      type = _ref.type;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [type === _$$_REQUIRE(_dependencyMap[6]).General.OPEN_CHANNEL && /*#__PURE__*/(0, _jsxRuntime.jsx)(_convert_private.default, {
        channelId: channelId
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_leave_channel_label.default, {
        channelId: channelId,
        isOptionItem: true,
        testID: "channel_info.options.leave_channel.option"
      }), type !== _$$_REQUIRE(_dependencyMap[6]).General.DM_CHANNEL && type !== _$$_REQUIRE(_dependencyMap[6]).General.GM_CHANNEL && /*#__PURE__*/(0, _jsxRuntime.jsx)(_archive.default, {
        channelId: channelId,
        componentId: componentId,
        type: type
      })]
    });
  };
  var _default = exports.default = DestructiveOptions;
