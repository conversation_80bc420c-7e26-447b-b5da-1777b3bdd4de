  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var Archive = function Archive(_ref) {
    var canArchive = _ref.canArchive,
      canUnarchive = _ref.canUnarchive,
      canViewArchivedChannels = _ref.canViewArchivedChannels,
      channelId = _ref.channelId,
      componentId = _ref.componentId,
      displayName = _ref.displayName,
      type = _ref.type;
    var intl = (0, _$$_REQUIRE(_dependencyMap[5]).useIntl)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[6]).useServerUrl)();
    var _useAlert = (0, _$$_REQUIRE(_dependencyMap[7]).useAlert)(),
      showAlert = _useAlert.showAlert;
    var close = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (pop) {
        yield (0, _$$_REQUIRE(_dependencyMap[8]).dismissModal)({
          componentId: componentId
        });
        if (pop) {
          (0, _$$_REQUIRE(_dependencyMap[8]).popToRoot)();
        }
      });
      return function close(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    var alertAndHandleYesAction = function alertAndHandleYesAction(title, message, onPressAction) {
      var formatMessage = intl.formatMessage;
      var term;
      if (type === _$$_REQUIRE(_dependencyMap[9]).General.OPEN_CHANNEL) {
        term = formatMessage({
          id: 'channel_info.public_channel',
          defaultMessage: 'Public Channel'
        });
      } else {
        term = formatMessage({
          id: 'channel_info.private_channel',
          defaultMessage: 'Private Channel'
        });
      }
      showAlert(formatMessage(title, {
        term: term
      }), formatMessage(message, {
        term: term.toLowerCase(),
        name: displayName
      }), [{
        text: formatMessage({
          id: 'channel_info.alertNo',
          defaultMessage: 'No'
        })
      }, {
        text: formatMessage({
          id: 'channel_info.alertYes',
          defaultMessage: 'Yes'
        }),
        onPress: onPressAction
      }]);
    };
    var onArchive = (0, _$$_REQUIRE(_dependencyMap[10]).preventDoubleTap)(function () {
      var title = {
        id: (0, _$$_REQUIRE(_dependencyMap[11]).t)('channel_info.archive_title'),
        defaultMessage: 'Archive {term}'
      };
      var message = canViewArchivedChannels ? {
        id: (0, _$$_REQUIRE(_dependencyMap[11]).t)('channel_info.archive_description.can_view_archived'),
        defaultMessage: 'This will archive the channel from the team. Channel contents will still be accessible by channel members.\n\nAre you sure you wish to archive the {term} {name}?'
      } : {
        id: (0, _$$_REQUIRE(_dependencyMap[11]).t)('channel_info.archive_description.cannot_view_archived'),
        defaultMessage: 'This will archive the channel from the team and remove it from the user interface. Archived channels can be unarchived if needed again.\n\nAre you sure you wish to archive the {term} {name}?'
      };
      var onPressAction = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var result = yield (0, _$$_REQUIRE(_dependencyMap[12]).archiveChannel)(serverUrl, channelId);
          if (result.error) {
            (0, _$$_REQUIRE(_dependencyMap[13]).alertErrorWithFallback)(intl, result.error, {
              id: (0, _$$_REQUIRE(_dependencyMap[11]).t)('channel_info.archive_failed'),
              defaultMessage: 'An error occurred trying to archive the channel {displayName}'
            }, {
              displayName: displayName
            });
          } else {
            close(!canViewArchivedChannels);
          }
        });
        return function onPressAction() {
          return _ref3.apply(this, arguments);
        };
      }();
      alertAndHandleYesAction(title, message, onPressAction);
    });
    var onUnarchive = (0, _$$_REQUIRE(_dependencyMap[10]).preventDoubleTap)(function () {
      var title = {
        id: (0, _$$_REQUIRE(_dependencyMap[11]).t)('channel_info.unarchive_title'),
        defaultMessage: 'Unarchive {term}'
      };
      var message = {
        id: (0, _$$_REQUIRE(_dependencyMap[11]).t)('channel_info.unarchive_description'),
        defaultMessage: 'Are you sure you want to unarchive the {term} {name}?'
      };
      var onPressAction = /*#__PURE__*/function () {
        var _ref4 = (0, _asyncToGenerator2.default)(function* () {
          var result = yield (0, _$$_REQUIRE(_dependencyMap[12]).unarchiveChannel)(serverUrl, channelId);
          if (result.error) {
            (0, _$$_REQUIRE(_dependencyMap[13]).alertErrorWithFallback)(intl, result.error, {
              id: (0, _$$_REQUIRE(_dependencyMap[11]).t)('channel_info.unarchive_failed'),
              defaultMessage: 'An error occurred trying to unarchive the channel {displayName}'
            }, {
              displayName: displayName
            });
          } else {
            close(false);
          }
        });
        return function onPressAction() {
          return _ref4.apply(this, arguments);
        };
      }();
      alertAndHandleYesAction(title, message, onPressAction);
    });
    if (!canArchive && !canUnarchive) {
      return null;
    }
    if (canUnarchive) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
        action: onUnarchive,
        label: intl.formatMessage({
          id: 'channel_info.unarchive',
          defaultMessage: 'Unarchive Channel'
        }),
        icon: "archive-arrow-up-outline",
        destructive: true,
        type: "default",
        testID: "channel_info.options.unarchive_channel.option"
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      action: onArchive,
      label: intl.formatMessage({
        id: 'channel_info.archive',
        defaultMessage: 'Archive Channel'
      }),
      icon: "archive-outline",
      destructive: true,
      type: "default",
      testID: "channel_info.options.archive_channel.option"
    });
  };
  var _default = exports.default = Archive;
