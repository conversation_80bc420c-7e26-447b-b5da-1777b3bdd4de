  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _archive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)(['channelId', 'type'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database,
      type = _ref.type;
    var team = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentTeam)(database);
    var currentUser = (0, _$$_REQUIRE(_dependencyMap[5]).observeCurrentUser)(database);
    var channel = (0, _$$_REQUIRE(_dependencyMap[6]).observeChannel)(database, channelId);
    var canViewArchivedChannels = (0, _$$_REQUIRE(_dependencyMap[7]).observeConfigBooleanValue)(database, 'ExperimentalViewArchivedChannels');
    var isArchived = channel.pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[9]).of)(((c == null ? undefined : c.deleteAt) || 0) > 0);
    }));
    var canLeave = channel.pipe((0, _$$_REQUIRE(_dependencyMap[8]).combineLatestWith)(currentUser), (0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        ch = _ref3[0],
        u = _ref3[1];
      var isDefaultChannel = (ch == null ? undefined : ch.name) === _$$_REQUIRE(_dependencyMap[10]).General.DEFAULT_CHANNEL;
      return (0, _$$_REQUIRE(_dependencyMap[9]).of)(!isDefaultChannel || isDefaultChannel && (u == null ? undefined : u.isGuest));
    }));
    var canArchive = channel.pipe((0, _$$_REQUIRE(_dependencyMap[8]).combineLatestWith)(currentUser, canLeave, isArchived), (0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (_ref4) {
      var _ref5 = (0, _slicedToArray2.default)(_ref4, 4),
        ch = _ref5[0],
        u = _ref5[1],
        leave = _ref5[2],
        archived = _ref5[3];
      if (type === _$$_REQUIRE(_dependencyMap[10]).General.DM_CHANNEL || type === _$$_REQUIRE(_dependencyMap[10]).General.GM_CHANNEL || !ch || !u || !leave || archived) {
        return (0, _$$_REQUIRE(_dependencyMap[9]).of)(false);
      }
      if (type === _$$_REQUIRE(_dependencyMap[10]).General.OPEN_CHANNEL) {
        return (0, _$$_REQUIRE(_dependencyMap[11]).observePermissionForChannel)(database, ch, u, _$$_REQUIRE(_dependencyMap[10]).Permissions.DELETE_PUBLIC_CHANNEL, true);
      }
      return (0, _$$_REQUIRE(_dependencyMap[11]).observePermissionForChannel)(database, ch, u, _$$_REQUIRE(_dependencyMap[10]).Permissions.DELETE_PRIVATE_CHANNEL, true);
    }));
    var canUnarchive = team.pipe((0, _$$_REQUIRE(_dependencyMap[8]).combineLatestWith)(currentUser, isArchived), (0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (_ref6) {
      var _ref7 = (0, _slicedToArray2.default)(_ref6, 3),
        t = _ref7[0],
        u = _ref7[1],
        archived = _ref7[2];
      if (type === _$$_REQUIRE(_dependencyMap[10]).General.DM_CHANNEL || type === _$$_REQUIRE(_dependencyMap[10]).General.GM_CHANNEL || !t || !u || !archived) {
        return (0, _$$_REQUIRE(_dependencyMap[9]).of)(false);
      }
      return (0, _$$_REQUIRE(_dependencyMap[11]).observePermissionForTeam)(database, t, u, _$$_REQUIRE(_dependencyMap[10]).Permissions.MANAGE_TEAM, false);
    }));
    return {
      canArchive: canArchive,
      canUnarchive: canUnarchive,
      canViewArchivedChannels: canViewArchivedChannels,
      displayName: channel.pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (c) {
        return (0, _$$_REQUIRE(_dependencyMap[9]).of)(c == null ? undefined : c.displayName);
      }))
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_archive.default));
