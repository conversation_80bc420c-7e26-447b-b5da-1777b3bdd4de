  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _convert_private = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var currentUser = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentUser)(database);
    var channel = (0, _$$_REQUIRE(_dependencyMap[5]).observeChannel)(database, channelId);
    var canConvert = channel.pipe((0, _$$_REQUIRE(_dependencyMap[6]).combineLatestWith)(currentUser), (0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        ch = _ref3[0],
        u = _ref3[1];
      if (!ch || !u || ch.name === _$$_REQUIRE(_dependencyMap[7]).General.DEFAULT_CHANNEL) {
        return (0, _$$_REQUIRE(_dependencyMap[8]).of)(false);
      }
      return (0, _$$_REQUIRE(_dependencyMap[9]).observePermissionForChannel)(database, ch, u, _$$_REQUIRE(_dependencyMap[7]).Permissions.CONVERT_PUBLIC_CHANNEL_TO_PRIVATE, false);
    }));
    return {
      canConvert: canConvert,
      displayName: channel.pipe((0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (c) {
        return (0, _$$_REQUIRE(_dependencyMap[8]).of)(c == null ? undefined : c.displayName);
      }))
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_convert_private.default));
