  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var ConvertPrivate = function ConvertPrivate(_ref) {
    var canConvert = _ref.canConvert,
      channelId = _ref.channelId,
      displayName = _ref.displayName;
    var intl = (0, _$$_REQUIRE(_dependencyMap[5]).useIntl)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[6]).useServerUrl)();
    var _useAlert = (0, _$$_REQUIRE(_dependencyMap[7]).useAlert)(),
      showAlert = _useAlert.showAlert;
    var onConfirmConvertToPrivate = function onConfirmConvertToPrivate() {
      var formatMessage = intl.formatMessage;
      var title = {
        id: (0, _$$_REQUIRE(_dependencyMap[8]).t)('channel_info.convert_private_title'),
        defaultMessage: 'Convert {displayName} to a private channel?'
      };
      var message = {
        id: (0, _$$_REQUIRE(_dependencyMap[8]).t)('channel_info.convert_private_description'),
        defaultMessage: 'When you convert {displayName} to a private channel, history and membership are preserved. Publicly shared files remain accessible to anyone with the link. Membership in a private channel is by invitation only.\n\nThe change is permanent and cannot be undone.\n\nAre you sure you want to convert {displayName} to a private channel?'
      };
      showAlert(formatMessage(title, {
        displayName: displayName
      }), formatMessage(message, {
        displayName: displayName
      }), [{
        text: formatMessage({
          id: 'channel_info.alertNo',
          defaultMessage: 'No'
        })
      }, {
        text: formatMessage({
          id: 'channel_info.alertYes',
          defaultMessage: 'Yes'
        }),
        onPress: convertToPrivate
      }]);
    };
    var convertToPrivate = (0, _$$_REQUIRE(_dependencyMap[9]).preventDoubleTap)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var result = yield (0, _$$_REQUIRE(_dependencyMap[10]).convertChannelToPrivate)(serverUrl, channelId);
      var formatMessage = intl.formatMessage;
      if (result.error) {
        (0, _$$_REQUIRE(_dependencyMap[11]).alertErrorWithFallback)(intl, result.error, {
          id: (0, _$$_REQUIRE(_dependencyMap[8]).t)('channel_info.convert_failed'),
          defaultMessage: 'We were unable to convert {displayName} to a private channel.'
        }, {
          displayName: displayName
        }, [{
          text: formatMessage({
            id: 'channel_info.error_close',
            defaultMessage: 'Close'
          })
        }, {
          text: formatMessage({
            id: 'channel_info.alert_retry',
            defaultMessage: 'Try Again'
          }),
          onPress: convertToPrivate
        }]);
      } else {
        showAlert('', formatMessage({
          id: (0, _$$_REQUIRE(_dependencyMap[8]).t)('channel_info.convert_private_success'),
          defaultMessage: '{displayName} is now a private channel.'
        }, {
          displayName: displayName
        }));
      }
    }));
    if (!canConvert) {
      return null;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      action: onConfirmConvertToPrivate,
      label: intl.formatMessage({
        id: 'channel_info.convert_private',
        defaultMessage: 'Convert to private channel'
      }),
      icon: "lock-outline",
      type: "default",
      testID: "channel_info.options.convert_private.option"
    });
  };
  var _default = exports.default = ConvertPrivate;
