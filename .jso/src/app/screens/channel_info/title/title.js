  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _direct_message = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _group_message = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _public_private = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    container: {
      marginBottom: 16,
      marginTop: 24
    }
  });
  var Title = function Title(_ref) {
    var channelId = _ref.channelId,
      displayName = _ref.displayName,
      type = _ref.type;
    var displayHolder = displayName === undefined ? undefined : displayName === "Off-Topic" ? "المجموعة الفرعية" : displayName === "Town Square" ? "المجموعة الرئيسية" : displayName;
    var component;
    switch (type) {
      case _$$_REQUIRE(_dependencyMap[7]).General.DM_CHANNEL:
        component = /*#__PURE__*/(0, _jsxRuntime.jsx)(_direct_message.default, {
          channelId: channelId,
          displayName: displayHolder
        });
        break;
      case _$$_REQUIRE(_dependencyMap[7]).General.GM_CHANNEL:
        component = /*#__PURE__*/(0, _jsxRuntime.jsx)(_group_message.default, {
          channelId: channelId,
          displayName: displayHolder
        });
        break;
      default:
        component = /*#__PURE__*/(0, _jsxRuntime.jsx)(_public_private.default, {
          channelId: channelId,
          displayName: displayHolder
        });
        break;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: component
    });
  };
  var _default = exports.default = Title;
