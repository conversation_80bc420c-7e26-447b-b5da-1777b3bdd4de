  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _group_message = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var currentUserId = (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentUserId)(database);
    var channel = (0, _$$_REQUIRE(_dependencyMap[4]).observeChannel)(database, channelId);
    var members = channel.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (c) {
      return c ? (0, _$$_REQUIRE(_dependencyMap[4]).observeChannelMembers)(database, channelId) : (0, _$$_REQUIRE(_dependencyMap[6]).of)([]);
    }));
    return {
      currentUserId: currentUserId,
      members: members
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_group_message.default));
