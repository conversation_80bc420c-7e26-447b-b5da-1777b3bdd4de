  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[4]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        alignItems: 'center',
        flexDirection: 'row',
        marginBottom: 8,
        flexWrap: 'wrap'
      },
      profile: {
        borderColor: theme.centerChannelBg,
        borderRadius: 24,
        borderWidth: 2,
        height: 48,
        width: 48,
        marginEnd: 5
      }
    };
  });
  var GroupAvatars = function GroupAvatars(_ref) {
    var users = _ref.users;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[5]).useServerUrl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[6]).useTheme)();
    var styles = getStyleSheet(theme);
    var group = users.map(function (u, i) {
      var pictureUrl = (0, _$$_REQUIRE(_dependencyMap[7]).buildProfileImageUrlFromUser)(serverUrl, u);
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[8]).Image, {
        style: [styles.profile
        //  {transform: [{translateX: -(i * 12)}]}
        ],
        source: {
          uri: (0, _$$_REQUIRE(_dependencyMap[9]).buildAbsoluteUrl)(serverUrl, pictureUrl)
        }
      }, pictureUrl + i.toString());
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.container],
      testID: "channel_info.title.group_message.group_avatars",
      children: group
    });
  };
  var _default = exports.default = GroupAvatars;
