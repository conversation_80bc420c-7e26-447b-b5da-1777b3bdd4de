  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _profile_picture = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'flex-start'
      },
      displayName: {
        flexDirection: 'row'
      },
      position: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.72)
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200)),
      tagContainer: {
        marginLeft: 12
      },
      tag: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 100, 'SemiBold')),
      titleContainer: {
        //flex: 1,
        marginStart: 16,
        justifyContent: 'flex-end'
      },
      title: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 700, 'SemiBold'), {
        flexShrink: 1
      })
    };
  });
  var DirectMessage = function DirectMessage(_ref) {
    var _user$props, _user$props2;
    var displayName = _ref.displayName,
      user = _ref.user,
      hideGuestTags = _ref.hideGuestTags;
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var styles = getStyleSheet(theme);
    var directMessageUserTestId = `channel_info.title.direct_message.${user == null ? undefined : user.id}`;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      testID: directMessageUserTestId,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_profile_picture.default, {
        author: user,
        size: 64,
        iconSize: 64,
        showStatus: true,
        statusSize: 24,
        testID: `${directMessageUserTestId}.profile_picture`
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.titleContainer,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.displayName,
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            numberOfLines: 1,
            style: styles.title,
            testID: `${directMessageUserTestId}.display_name`,
            children: displayName
          }), (user == null ? undefined : user.isGuest) && !hideGuestTags && /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[8]).GuestTag, {
            textStyle: styles.tag,
            style: styles.tagContainer,
            testID: `${directMessageUserTestId}.guest.tag`
          }), (user == null ? undefined : user.isBot) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[8]).BotTag, {
            textStyle: styles.tag,
            style: styles.tagContainer,
            testID: `${directMessageUserTestId}.bot.tag`
          })]
        }), Boolean(user == null ? undefined : user.position) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.position,
          testID: `${directMessageUserTestId}.position`,
          children: user == null ? undefined : user.position
        }), Boolean((user == null ? undefined : user.isBot) && ((_user$props = user.props) == null ? undefined : _user$props.bot_description)) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.position,
          testID: `${directMessageUserTestId}.bot_description`,
          children: user == null ? undefined : (_user$props2 = user.props) == null ? undefined : _user$props2.bot_description
        })]
      })]
    });
  };
  var _default = exports.default = DirectMessage;
