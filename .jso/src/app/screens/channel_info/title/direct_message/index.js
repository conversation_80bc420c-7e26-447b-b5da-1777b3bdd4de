  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _direct_message = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var currentUserId = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentUserId)(database);
    var channel = (0, _$$_REQUIRE(_dependencyMap[5]).observeChannel)(database, channelId);
    var user = currentUserId.pipe((0, _$$_REQUIRE(_dependencyMap[6]).combineLatestWith)(channel), (0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        uId = _ref3[0],
        ch = _ref3[1];
      if (!ch) {
        return (0, _$$_REQUIRE(_dependencyMap[7]).of)(undefined);
      }
      var otherUserId = (0, _$$_REQUIRE(_dependencyMap[8]).getUserIdFromChannelName)(uId, ch.name);
      return (0, _$$_REQUIRE(_dependencyMap[9]).observeUser)(database, otherUserId);
    }));
    return {
      currentUserId: currentUserId,
      user: user,
      hideGuestTags: (0, _$$_REQUIRE(_dependencyMap[4]).observeConfigBooleanValue)(database, 'HideGuestTags')
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_direct_message.default));
