  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _clipboard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _slide_up_panel_item = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      title: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 700, 'SemiBold')),
      purpose: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.72),
        marginTop: 8
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 200))
    };
  });
  var style = _reactNative.StyleSheet.create({
    bottomsheet: {
      flex: 1
    }
  });
  var PublicPrivate = function PublicPrivate(_ref) {
    var displayName = _ref.displayName,
      purpose = _ref.purpose;
    var intl = (0, _$$_REQUIRE(_dependencyMap[9]).useIntl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var managedConfig = (0, _$$_REQUIRE(_dependencyMap[11]).useManagedConfig)();
    var _useSafeAreaInsets = (0, _$$_REQUIRE(_dependencyMap[12]).useSafeAreaInsets)(),
      bottom = _useSafeAreaInsets.bottom;
    var styles = getStyleSheet(theme);
    var publicPrivateTestId = 'channel_info.title.public_private';
    var onCopy = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      _clipboard.default.setString(purpose);
      yield (0, _$$_REQUIRE(_dependencyMap[13]).dismissBottomSheet)();
      if (_reactNative.Platform.OS === _$$_REQUIRE(_dependencyMap[14]).OS_VERSION.ANDROID && Number(_reactNative.Platform.Version) < _$$_REQUIRE(_dependencyMap[14]).ANDROID_33 || _reactNative.Platform.OS === _$$_REQUIRE(_dependencyMap[14]).OS_VERSION.IOS) {
        (0, _$$_REQUIRE(_dependencyMap[15]).showSnackBar)({
          barType: _$$_REQUIRE(_dependencyMap[16]).SNACK_BAR_TYPE.TEXT_COPIED
        });
      }
    }), [purpose]);
    var handleLongPress = (0, _react.useCallback)(function () {
      if ((managedConfig == null ? undefined : managedConfig.copyAndPasteProtection) !== 'true') {
        var renderContent = function renderContent() {
          return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: style.bottomsheet,
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_slide_up_panel_item.default, {
              leftIcon: "content-copy",
              onPress: onCopy,
              testID: `${publicPrivateTestId}.bottom_sheet.copy_purpose`,
              text: intl.formatMessage({
                id: 'channel_info.copy_purpose_text',
                defaultMessage: 'Copy Purpose Text'
              })
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_slide_up_panel_item.default, {
              destructive: true,
              leftIcon: "cancel",
              onPress: function onPress() {
                (0, _$$_REQUIRE(_dependencyMap[13]).dismissBottomSheet)();
              },
              testID: `${publicPrivateTestId}.bottom_sheet.cancel`,
              text: intl.formatMessage({
                id: 'mobile.post.cancel',
                defaultMessage: 'Cancel'
              })
            })]
          });
        };
        (0, _$$_REQUIRE(_dependencyMap[13]).bottomSheet)({
          closeButtonId: 'close-mardown-link',
          renderContent: renderContent,
          snapPoints: [1, (0, _$$_REQUIRE(_dependencyMap[17]).bottomSheetSnapPoint)(2, _slide_up_panel_item.ITEM_HEIGHT, bottom)],
          title: intl.formatMessage({
            id: 'post.options.title',
            defaultMessage: 'Options'
          }),
          theme: theme
        });
      }
    }, [bottom, theme, onCopy, intl.formatMessage, managedConfig == null ? undefined : managedConfig.copyAndPasteProtection]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.title,
        testID: `${publicPrivateTestId}.display_name`,
        children: displayName
      }), Boolean(purpose) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        onLongPress: handleLongPress,
        style: styles.purpose,
        testID: `${publicPrivateTestId}.purpose`,
        children: purpose
      })]
    });
  };
  var _default = exports.default = PublicPrivate;
