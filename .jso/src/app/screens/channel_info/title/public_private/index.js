  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _public_private = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var channelInfo = (0, _$$_REQUIRE(_dependencyMap[3]).observeChannelInfo)(database, channelId);
    var purpose = channelInfo.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (ci) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)(ci == null ? undefined : ci.purpose);
    }));
    return {
      purpose: purpose
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_public_private.default));
