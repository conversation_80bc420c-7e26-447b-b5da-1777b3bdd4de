  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _channel_info = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var serverUrl = _ref.serverUrl,
      database = _ref.database;
    var channel = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentChannel)(database);
    var type = channel.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(c == null ? undefined : c.type);
    }));
    var channelId = channel.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)((c == null ? undefined : c.id) || '');
    }));
    var teamId = channel.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (c) {
      return c != null && c.teamId ? (0, _$$_REQUIRE(_dependencyMap[6]).of)(c.teamId) : (0, _$$_REQUIRE(_dependencyMap[7]).observeCurrentTeamId)(database);
    }));
    var userId = (0, _$$_REQUIRE(_dependencyMap[7]).observeCurrentUserId)(database);
    var currentUser = (0, _$$_REQUIRE(_dependencyMap[8]).observeCurrentUser)(database);
    var isTeamAdmin = (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([teamId, userId]).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        tId = _ref3[0],
        uId = _ref3[1];
      return (0, _$$_REQUIRE(_dependencyMap[8]).observeUserIsTeamAdmin)(database, uId, tId);
    }));
    var callsPluginEnabled = (0, _$$_REQUIRE(_dependencyMap[9]).observeCallsConfig)(serverUrl).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (config) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(config.pluginEnabled);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());

    // callsDefaultEnabled means "live mode" post 7.6
    var callsDefaultEnabled = (0, _$$_REQUIRE(_dependencyMap[9]).observeCallsConfig)(serverUrl).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (config) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(config.DefaultEnabled);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());
    var allowEnableCalls = (0, _$$_REQUIRE(_dependencyMap[9]).observeCallsConfig)(serverUrl).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (config) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(config.AllowEnableCalls);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());
    var systemAdmin = currentUser.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (u) {
      return u ? (0, _$$_REQUIRE(_dependencyMap[6]).of)(u.roles) : (0, _$$_REQUIRE(_dependencyMap[6]).of)('');
    }), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (roles) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)((0, _$$_REQUIRE(_dependencyMap[10]).isSystemAdmin)(roles || ''));
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());
    var channelAdmin = (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([userId, channelId]).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (_ref4) {
      var _ref5 = (0, _slicedToArray2.default)(_ref4, 2),
        uId = _ref5[0],
        chId = _ref5[1];
      return (0, _$$_REQUIRE(_dependencyMap[8]).observeUserIsChannelAdmin)(database, uId, chId);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());
    var serverVersion = (0, _$$_REQUIRE(_dependencyMap[7]).observeConfigValue)(database, 'Version');
    var callsGAServer = serverVersion.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (v) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)((0, _$$_REQUIRE(_dependencyMap[11]).isMinimumServerVersion)(v || '', 7, 6));
    }));
    var dmOrGM = type.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (t) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)((0, _$$_REQUIRE(_dependencyMap[12]).isTypeDMorGM)(t));
    }));
    var canEnableDisableCalls = (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([callsPluginEnabled, callsDefaultEnabled, allowEnableCalls, systemAdmin, channelAdmin, callsGAServer, dmOrGM, isTeamAdmin]).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (_ref6) {
      var _ref7 = (0, _slicedToArray2.default)(_ref6, 8),
        pluginEnabled = _ref7[0],
        liveMode = _ref7[1],
        allow = _ref7[2],
        sysAdmin = _ref7[3],
        chAdmin = _ref7[4],
        gaServer = _ref7[5],
        dmGM = _ref7[6],
        tAdmin = _ref7[7];
      // Always false if the plugin is not enabled.
      // if GA 7.6:
      //   allow (will always be true) and !liveMode = system admins can enable/disable
      //   allow (will always be true) and liveMode = channel, team, system admins, DM/GM participants can enable/disable
      // if pre GA 7.6:
      //   allow and !liveMode  = channel, system admins, DM/GM participants can enable/disable
      //   allow and liveMode   = channel, system admins, DM/GM participants can enable/disable
      //   !allow and !liveMode = system admins can enable/disable -- can combine with below
      //   !allow and liveMode  = system admins can enable/disable -- can combine with above
      // Note: There are ways to 'simplify' the conditions below. Here we're preferring clarity.

      if (!pluginEnabled) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(false);
      }
      if (gaServer) {
        if (allow && !liveMode) {
          return (0, _$$_REQUIRE(_dependencyMap[6]).of)(Boolean(sysAdmin));
        }
        if (allow && liveMode) {
          return (0, _$$_REQUIRE(_dependencyMap[6]).of)(Boolean(chAdmin || tAdmin || sysAdmin || dmGM));
        }
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(false);
      }

      // now we're pre GA 7.6
      if (allow && liveMode) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(Boolean(chAdmin || sysAdmin || dmGM));
      }
      if (allow && !liveMode) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(Boolean(sysAdmin || chAdmin || dmGM));
      }
      if (!allow) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(Boolean(sysAdmin));
      }
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(false);
    }));
    var isCallsEnabledInChannel = (0, _$$_REQUIRE(_dependencyMap[13]).observeIsCallsEnabledInChannel)(database, serverUrl, (0, _$$_REQUIRE(_dependencyMap[7]).observeCurrentChannelId)(database));
    var groupCallsAllowed = (0, _$$_REQUIRE(_dependencyMap[9]).observeCallsConfig)(serverUrl).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (config) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(config.GroupCallsAllowed);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());
    var canManageMembers = currentUser.pipe((0, _$$_REQUIRE(_dependencyMap[5]).combineLatestWith)(channelId), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (_ref8) {
      var _ref9 = (0, _slicedToArray2.default)(_ref8, 2),
        u = _ref9[0],
        cId = _ref9[1];
      return u ? (0, _$$_REQUIRE(_dependencyMap[14]).observeCanManageChannelMembers)(database, cId, u) : (0, _$$_REQUIRE(_dependencyMap[6]).of)(false);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());
    var canManageSettings = currentUser.pipe((0, _$$_REQUIRE(_dependencyMap[5]).combineLatestWith)(channelId), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (_ref10) {
      var _ref11 = (0, _slicedToArray2.default)(_ref10, 2),
        u = _ref11[0],
        cId = _ref11[1];
      return u ? (0, _$$_REQUIRE(_dependencyMap[14]).observeCanManageChannelSettings)(database, cId, u) : (0, _$$_REQUIRE(_dependencyMap[6]).of)(false);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());
    var isGuestUser = currentUser.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (u) {
      return u ? (0, _$$_REQUIRE(_dependencyMap[6]).of)(u.isGuest) : (0, _$$_REQUIRE(_dependencyMap[6]).of)(false);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).distinctUntilChanged)());
    var isConvertGMFeatureAvailable = serverVersion.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (version) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)((0, _$$_REQUIRE(_dependencyMap[11]).isMinimumServerVersion)(version || '', 9, 1));
    }));
    var isBookmarksEnabled = (0, _$$_REQUIRE(_dependencyMap[7]).observeConfigBooleanValue)(database, 'FeatureFlagChannelBookmarks');
    var canAddBookmarks = channelId.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (cId) {
      return (0, _$$_REQUIRE(_dependencyMap[15]).observeCanAddBookmarks)(database, cId);
    }));
    return {
      type: type,
      canEnableDisableCalls: canEnableDisableCalls,
      isCallsEnabledInChannel: isCallsEnabledInChannel,
      groupCallsAllowed: groupCallsAllowed,
      canAddBookmarks: canAddBookmarks,
      canManageMembers: canManageMembers,
      canManageSettings: canManageSettings,
      isBookmarksEnabled: isBookmarksEnabled,
      isCRTEnabled: (0, _$$_REQUIRE(_dependencyMap[16]).observeIsCRTEnabled)(database),
      isGuestUser: isGuestUser,
      isConvertGMFeatureAvailable: isConvertGMFeatureAvailable
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)((0, _$$_REQUIRE(_dependencyMap[17]).withServerUrl)(enhanced(_channel_info.default)));
