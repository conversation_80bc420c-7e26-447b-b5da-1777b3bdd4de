  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _clipboard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _custom_status_expiry = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _emoji = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _formatted_date = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _markdown = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _slide_up_panel_item = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var headerMetadata = {
    header: {
      width: 1,
      height: 1
    }
  };
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[14]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        marginBottom: 20
      },
      item: {
        marginTop: 16
      },
      extraHeading: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.centerChannelColor, 0.56),
        marginBottom: 8
      }, (0, _$$_REQUIRE(_dependencyMap[15]).typography)('Heading', 75)),
      header: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[15]).typography)('Heading', 200), {
        fontWeight: undefined
      }),
      created: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.centerChannelColor, 0.48)
      }, (0, _$$_REQUIRE(_dependencyMap[15]).typography)('Heading', 75)),
      customStatus: {
        alignItems: 'center',
        flexDirection: 'row'
      },
      customStatusEmoji: {
        marginRight: 10
      },
      customStatusLabel: Object.assign({
        color: theme.centerChannelColor,
        marginRight: 8
      }, (0, _$$_REQUIRE(_dependencyMap[15]).typography)('Heading', 200)),
      customStatusExpiry: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.centerChannelColor, 0.64)
      }, (0, _$$_REQUIRE(_dependencyMap[15]).typography)('Heading', 75))
    };
  });
  var style = _reactNative.StyleSheet.create({
    bottomSheet: {
      flex: 1
    }
  });
  var headerTestId = 'channel_info.extra.header';
  var onCopy = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (text, isLink) {
      _clipboard.default.setString(text);
      yield (0, _$$_REQUIRE(_dependencyMap[16]).dismissBottomSheet)();
      if (_reactNative.Platform.OS === _$$_REQUIRE(_dependencyMap[17]).OS_VERSION.ANDROID && Number(_reactNative.Platform.Version) < _$$_REQUIRE(_dependencyMap[17]).ANDROID_33 || _reactNative.Platform.OS === _$$_REQUIRE(_dependencyMap[17]).OS_VERSION.IOS) {
        (0, _$$_REQUIRE(_dependencyMap[18]).showSnackBar)({
          barType: isLink ? _$$_REQUIRE(_dependencyMap[19]).SNACK_BAR_TYPE.LINK_COPIED : _$$_REQUIRE(_dependencyMap[19]).SNACK_BAR_TYPE.TEXT_COPIED
        });
      }
    });
    return function onCopy(_x, _x2) {
      return _ref.apply(this, arguments);
    };
  }();
  var Extra = function Extra(_ref2) {
    var channelId = _ref2.channelId,
      createdAt = _ref2.createdAt,
      createdBy = _ref2.createdBy,
      customStatus = _ref2.customStatus,
      header = _ref2.header,
      isCustomStatusEnabled = _ref2.isCustomStatusEnabled;
    var intl = (0, _$$_REQUIRE(_dependencyMap[20]).useIntl)();
    var _useSafeAreaInsets = (0, _$$_REQUIRE(_dependencyMap[21]).useSafeAreaInsets)(),
      bottom = _useSafeAreaInsets.bottom;
    var theme = (0, _$$_REQUIRE(_dependencyMap[22]).useTheme)();
    var managedConfig = (0, _$$_REQUIRE(_dependencyMap[23]).useManagedConfig)();
    var styles = getStyleSheet(theme);
    var blockStyles = (0, _$$_REQUIRE(_dependencyMap[24]).getMarkdownBlockStyles)(theme);
    var textStyles = (0, _$$_REQUIRE(_dependencyMap[24]).getMarkdownTextStyles)(theme);
    var created = (0, _react.useMemo)(function () {
      return {
        user: createdBy,
        date: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_date.default, {
          style: styles.created,
          value: createdAt
        })
      };
    }, [createdAt, createdBy, theme]);
    var handleLongPress = (0, _react.useCallback)(function (url) {
      if ((managedConfig == null ? undefined : managedConfig.copyAndPasteProtection) !== 'true') {
        var renderContent = function renderContent() {
          return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            testID: `${headerTestId}.bottom_sheet`,
            style: style.bottomSheet,
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_slide_up_panel_item.default, {
              leftIcon: "content-copy",
              onPress: function onPress() {
                onCopy(header);
              },
              testID: `${headerTestId}.bottom_sheet.copy_header_text`,
              text: intl.formatMessage({
                id: 'mobile.markdown.copy_header',
                defaultMessage: 'Copy header text'
              })
            }), Boolean(url) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_slide_up_panel_item.default, {
              leftIcon: "link-variant",
              onPress: function onPress() {
                onCopy(url, true);
              },
              testID: `${headerTestId}.bottom_sheet.copy_url`,
              text: intl.formatMessage({
                id: 'mobile.markdown.link.copy_url',
                defaultMessage: 'Copy URL'
              })
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_slide_up_panel_item.default, {
              destructive: true,
              leftIcon: "cancel",
              onPress: function onPress() {
                (0, _$$_REQUIRE(_dependencyMap[16]).dismissBottomSheet)();
              },
              testID: `${headerTestId}.bottom_sheet.cancel`,
              text: intl.formatMessage({
                id: 'mobile.post.cancel',
                defaultMessage: 'Cancel'
              })
            })]
          });
        };
        (0, _$$_REQUIRE(_dependencyMap[16]).bottomSheet)({
          closeButtonId: 'close-markdown-link',
          renderContent: renderContent,
          snapPoints: [1, (0, _$$_REQUIRE(_dependencyMap[25]).bottomSheetSnapPoint)(url ? 3 : 2, _slide_up_panel_item.ITEM_HEIGHT, bottom)],
          title: intl.formatMessage({
            id: 'post.options.title',
            defaultMessage: 'Options'
          }),
          theme: theme
        });
      }
    }, [header, bottom, theme, intl.formatMessage, managedConfig == null ? undefined : managedConfig.copyAndPasteProtection]);
    var touchableHandleLongPress = (0, _react.useCallback)(function () {
      return handleLongPress();
    }, [handleLongPress]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [isCustomStatusEnabled && Boolean(customStatus) && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.item,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "channel_info.custom_status",
          defaultMessage: "Custom status:",
          style: styles.extraHeading
        }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.customStatus,
          children: [Boolean(customStatus == null ? undefined : customStatus.emoji) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.customStatusEmoji,
            testID: `channel_info.custom_status.custom_status_emoji.${customStatus == null ? undefined : customStatus.emoji}`,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_emoji.default, {
              emojiName: customStatus.emoji,
              size: 24
            })
          }), Boolean(customStatus == null ? undefined : customStatus.text) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.customStatusLabel,
            testID: "channel_info.custom_status.custom_status_text",
            children: customStatus == null ? undefined : customStatus.text
          }), Boolean(customStatus == null ? undefined : customStatus.duration) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_expiry.default, {
            time: (0, _moment.default)(customStatus == null ? undefined : customStatus.expires_at),
            theme: theme,
            textStyles: styles.customStatusExpiry,
            withinBrackets: false,
            showPrefix: true,
            showToday: true,
            showTimeCompulsory: false,
            testID: `channel_info.custom_status.custom_status_duration.${customStatus == null ? undefined : customStatus.duration}.custom_status_expiry`
          })]
        })]
      }), Boolean(header) && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.item,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "channel_info.header",
          defaultMessage: "Header:",
          style: styles.extraHeading,
          testID: headerTestId
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
          type: "opacity",
          activeOpacity: 0.8,
          onLongPress: touchableHandleLongPress,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_markdown.default, {
            channelId: channelId,
            baseTextStyle: styles.header,
            blockStyles: blockStyles,
            disableBlockQuote: true,
            disableCodeBlock: true,
            disableGallery: true,
            disableHeading: true,
            disableTables: true,
            location: _$$_REQUIRE(_dependencyMap[26]).Screens.CHANNEL_INFO,
            textStyles: textStyles,
            layoutHeight: 48,
            layoutWidth: 100,
            theme: theme,
            imagesMetadata: headerMetadata,
            value: header,
            onLinkLongPress: handleLongPress
          })
        })]
      }), Boolean(createdAt && createdBy) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.item,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "channel_intro.createdBy",
          defaultMessage: "Created by {user} on {date}",
          style: styles.created,
          values: created,
          testID: "channel_info.extra.created_by"
        })
      }), Boolean(createdAt && !createdBy) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.item,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "channel_intro.createdOn",
          defaultMessage: "Created on {date}",
          style: styles.created,
          values: created,
          testID: "channel_info.extra.created_on"
        })
      })]
    });
  };
  var _default = exports.default = Extra;
