  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _extra = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)(['channelId'], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var currentUser = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentUser)(database);
    var teammateNameDisplay = (0, _$$_REQUIRE(_dependencyMap[4]).observeTeammateNameDisplay)(database);
    var channel = (0, _$$_REQUIRE(_dependencyMap[5]).observeChannel)(database, channelId);
    var channelInfo = (0, _$$_REQUIRE(_dependencyMap[5]).observeChannelInfo)(database, channelId);
    var createdAt = channel.pipe((0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[7]).of)((c == null ? undefined : c.type) === _$$_REQUIRE(_dependencyMap[8]).General.DM_CHANNEL ? 0 : c == null ? undefined : c.createAt);
    }));
    var header = channelInfo.pipe((0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (ci) {
      return (0, _$$_REQUIRE(_dependencyMap[7]).of)(ci == null ? undefined : ci.header);
    }));
    var dmUser = currentUser.pipe((0, _$$_REQUIRE(_dependencyMap[6]).combineLatestWith)(channel), (0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        user = _ref3[0],
        c = _ref3[1];
      if ((c == null ? undefined : c.type) === _$$_REQUIRE(_dependencyMap[8]).General.DM_CHANNEL && user) {
        var teammateId = (0, _$$_REQUIRE(_dependencyMap[9]).getUserIdFromChannelName)(user.id, c.name);
        return (0, _$$_REQUIRE(_dependencyMap[4]).observeUser)(database, teammateId);
      }
      return (0, _$$_REQUIRE(_dependencyMap[7]).of)(undefined);
    }));
    var createdBy = channel.pipe((0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (ch) {
      return ch != null && ch.creatorId ? (0, _$$_REQUIRE(_dependencyMap[4]).observeUser)(database, ch.creatorId) : (0, _$$_REQUIRE(_dependencyMap[7]).of)(undefined);
    }), (0, _$$_REQUIRE(_dependencyMap[6]).combineLatestWith)(currentUser, teammateNameDisplay), (0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (_ref4) {
      var _ref5 = (0, _slicedToArray2.default)(_ref4, 3),
        creator = _ref5[0],
        me = _ref5[1],
        disaplySetting = _ref5[2];
      return (0, _$$_REQUIRE(_dependencyMap[7]).of)((0, _$$_REQUIRE(_dependencyMap[9]).displayUsername)(creator, me == null ? undefined : me.locale, disaplySetting, false));
    }));
    var customStatus = dmUser.pipe((0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (dm) {
      return (0, _$$_REQUIRE(_dependencyMap[7]).of)((0, _$$_REQUIRE(_dependencyMap[9]).isCustomStatusExpired)(dm) ? undefined : (0, _$$_REQUIRE(_dependencyMap[9]).getUserCustomStatus)(dm));
    }));
    var isCustomStatusEnabled = (0, _$$_REQUIRE(_dependencyMap[10]).observeConfigBooleanValue)(database, 'EnableCustomUserStatuses');
    return {
      createdAt: createdAt,
      createdBy: createdBy,
      customStatus: customStatus,
      header: header,
      isCustomStatusEnabled: isCustomStatusEnabled
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_extra.default));
