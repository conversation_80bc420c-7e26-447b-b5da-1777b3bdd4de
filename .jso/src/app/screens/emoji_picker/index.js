  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _bottom_sheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _picker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _footer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var style = _reactNative.StyleSheet.create({
    contentStyle: {
      paddingTop: 14
    }
  });
  var EmojiPickerScreen = function EmojiPickerScreen(_ref) {
    var closeButtonId = _ref.closeButtonId,
      componentId = _ref.componentId,
      file = _ref.file,
      imageUrl = _ref.imageUrl,
      onEmojiPress = _ref.onEmojiPress,
      _ref$isSelectingMulti = _ref.isSelectingMultiple,
      isSelectingMultiple = _ref$isSelectingMulti === undefined ? false : _ref$isSelectingMulti,
      onClose = _ref.onClose;
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[9]).useIsTablet)();
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      selectedEmojis = _useState2[0],
      setSelectedEmojis = _useState2[1];
    var _useState3 = (0, _react.useState)(0),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      cursorPosition = _useState4[0],
      setCursorPosition = _useState4[1];
    var handleEmojiPress = (0, _react.useCallback)(function (emoji) {
      console.log('😀 handleEmojiPress CALLED in EmojiPickerScreen!');
      console.log('😀 Emoji:', emoji, 'isSelectingMultiple:', isSelectingMultiple);
      if (isSelectingMultiple) {
        // For multiple selection, add to selected emojis instead of calling onEmojiPress immediately
        var emojiDataMew = (0, _$$_REQUIRE(_dependencyMap[10]).getEmojiByName)(emoji, []);
        var emojiCharacter = `:${emoji}:`;
        if (emojiDataMew != null && emojiDataMew.image && emojiDataMew.category !== 'custom') {
          var codeArray = emojiDataMew.image.split('-');
          var code = codeArray.reduce(function (acc, c) {
            return acc + String.fromCodePoint(parseInt(c, 16));
          }, '');
          emojiCharacter = code;
        }
        var newEmoji = {
          id: `${emoji}_${Date.now()}`,
          character: emojiCharacter,
          name: emoji
        };
        console.log('😀 Adding new emoji:', newEmoji);
        setSelectedEmojis(function (prev) {
          var newArray = [].concat((0, _toConsumableArray2.default)(prev), [newEmoji]);
          console.log('😀 New selectedEmojis array:', newArray.map(function (e) {
            return {
              id: e.id,
              character: e.character
            };
          }));
          return newArray;
        });
      } else {
        // For single selection, use original behavior
        console.log('😀 Single selection mode, calling onEmojiPress and closing sheet');
        onEmojiPress(emoji);
        _reactNative.DeviceEventEmitter.emit(_$$_REQUIRE(_dependencyMap[11]).Events.CLOSE_BOTTOM_SHEET);
      }
    }, [isSelectingMultiple, onEmojiPress]);
    var handleRemoveEmoji = (0, _react.useCallback)(function (id) {
      setSelectedEmojis(function (prev) {
        return prev.filter(function (emoji) {
          return emoji.id !== id;
        });
      });
    }, []);
    var handleRemoveEmojiAtPosition = (0, _react.useCallback)(function (position) {
      console.log('🎯 Removing emoji at position:', position);
      setSelectedEmojis(function (prev) {
        var newEmojis = (0, _toConsumableArray2.default)(prev);
        if (position >= 0 && position < newEmojis.length) {
          // Calculate new cursor position after deletion
          var newCursorPosition = 0;
          for (var i = 0; i < position; i++) {
            newCursorPosition += newEmojis[i].character.length;
          }
          newEmojis.splice(position, 1);
          setCursorPosition(newCursorPosition);
        }
        return newEmojis;
      });
    }, []);
    var handleCursorPositionChange = (0, _react.useCallback)(function (position) {
      console.log('📍 handleCursorPositionChange CALLED in EmojiPickerScreen!');
      console.log('📍 New cursor position:', position);
      setCursorPosition(position);
    }, []);
    var handleRemoveAllEmojis = (0, _react.useCallback)(function () {
      console.log('🧹 Clearing all emojis');
      setSelectedEmojis([]);
      setCursorPosition(0);
    }, []);
    var handleDone = (0, _react.useCallback)(function () {
      onClose == null ? undefined : onClose(selectedEmojis);
      _reactNative.DeviceEventEmitter.emit(_$$_REQUIRE(_dependencyMap[11]).Events.CLOSE_BOTTOM_SHEET);
    }, [selectedEmojis, onClose]);
    var renderContent = (0, _react.useCallback)(function () {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_picker.default, {
        onEmojiPress: handleEmojiPress,
        imageUrl: imageUrl,
        file: file,
        testID: "emoji_picker",
        selectedEmojis: selectedEmojis,
        onRemoveEmoji: handleRemoveEmoji,
        onRemoveEmojiAtPosition: handleRemoveEmojiAtPosition,
        cursorPosition: cursorPosition,
        onCursorPositionChange: handleCursorPositionChange,
        onDone: handleDone,
        showPreview: isSelectingMultiple
      });
    }, [handleEmojiPress, imageUrl, file, selectedEmojis, handleRemoveEmoji, handleRemoveEmojiAtPosition, cursorPosition, handleCursorPositionChange, handleDone, isSelectingMultiple]);
    var renderFooter = (0, _react.useCallback)(function (props) {
      console.log('🦶 renderFooter CALLED in EmojiPickerScreen!');
      console.log('🦶 Props being passed to PickerFooter:', {
        selectedEmojisCount: selectedEmojis.length,
        cursorPosition: cursorPosition,
        hasHandleRemoveEmojiAtPosition: !!handleRemoveEmojiAtPosition,
        hasHandleRemoveAllEmojis: !!handleRemoveAllEmojis,
        isTablet: isTablet
      });
      if (isTablet) {
        console.log('🦶 isTablet=true, returning undefined');
        return undefined;
      }
      console.log('🦶 Rendering PickerFooter with props');
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_footer.default, Object.assign({
        selectedEmojis: selectedEmojis,
        cursorPosition: cursorPosition,
        onRemoveEmojiAtPosition: handleRemoveEmojiAtPosition,
        onRemoveAllEmojis: handleRemoveAllEmojis
      }, props));
    }, [isTablet, selectedEmojis, cursorPosition, handleRemoveEmojiAtPosition, handleRemoveAllEmojis]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_bottom_sheet.default, {
      renderContent: renderContent,
      closeButtonId: closeButtonId,
      componentId: componentId,
      contentStyle: style.contentStyle,
      initialSnapIndex: 1,
      footerComponent: renderFooter,
      testID: "post_options"
    });
  };
  var _default = exports.default = EmojiPickerScreen;
