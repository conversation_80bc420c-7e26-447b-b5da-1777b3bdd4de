  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return _reactNative.StyleSheet.create({
      container: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.04),
        borderRadius: 8,
        borderWidth: 1,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.16),
        paddingHorizontal: 8,
        paddingVertical: 5,
        maxHeight: 100,
        minHeight: 100,
        marginHorizontal: 8,
        marginVertical: 8
      },
      header: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 12
      },
      title: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 100, 'SemiBold')),
      doneButton: Object.assign({
        backgroundColor: theme.buttonBg,
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
        minWidth: 64,
        height: 32,
        alignItems: "center",
        justifyContent: "center"
      }, _reactNative.Platform.select({
        ios: {
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1
          },
          shadowOpacity: 0.2,
          shadowRadius: 3
        },
        android: {
          elevation: 2
        }
      })),
      doneButtonText: Object.assign({
        color: theme.buttonColor
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Body', 75, 'SemiBold')),
      textInputContainer: {
        flex: 1,
        borderRadius: 8,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.02)
      },
      textInput: {
        fontSize: 24,
        color: theme.centerChannelColor,
        paddingVertical: 8,
        paddingHorizontal: 8,
        minHeight: 50,
        textAlignVertical: 'top',
        lineHeight: 32
      },
      emptyText: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.56),
        textAlign: "center",
        flex: 1,
        paddingVertical: 16
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Body', 75, 'Regular')),
      emojiItem: {
        position: "relative",
        marginRight: 8,
        minWidth: 40,
        height: 40,
        flexShrink: 0,
        alignItems: "center",
        justifyContent: "center"
      },
      emojiCharacter: {
        fontSize: 24,
        textAlign: "center"
      }
    });
  });
  var EmojiPickerPreview = function EmojiPickerPreview(_ref) {
    var selectedEmojis = _ref.selectedEmojis,
      onRemoveEmoji = _ref.onRemoveEmoji,
      onRemoveEmojiAtPosition = _ref.onRemoveEmojiAtPosition,
      onCursorPositionChange = _ref.onCursorPositionChange,
      onDone = _ref.onDone,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? "emoji_picker_preview" : _ref$testID;
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var styles = getStyleSheet(theme);
    var textInputRef = (0, _react.useRef)(null);

    // State for cursor position and text content
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      cursorPosition = _useState2[0],
      setCursorPosition = _useState2[1];
    var _useState3 = (0, _react.useState)(''),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      textValue = _useState4[0],
      setTextValue = _useState4[1];

    // Helper function to convert cursor position to emoji index
    var getEmojiIndexFromCursorPosition = (0, _react.useCallback)(function (cursor, emojis) {
      if (emojis.length === 0) return -1;
      var charCount = 0;
      for (var i = 0; i < emojis.length; i++) {
        var emojiLength = emojis[i].character.length;
        // If cursor is within this emoji's character range
        if (cursor <= charCount + emojiLength) {
          return i;
        }
        charCount += emojiLength;
      }

      // If cursor is at the end or beyond, return the last emoji index
      return emojis.length - 1;
    }, []);

    // Animation values
    var fadeAnim = (0, _react.useRef)(new _reactNative.Animated.Value(0)).current;
    var scaleAnim = (0, _react.useRef)(new _reactNative.Animated.Value(0.95)).current;

    // Update text value when emojis change
    (0, _react.useEffect)(function () {
      var emojiText = selectedEmojis.map(function (emoji) {
        return emoji.character;
      }).join('');
      setTextValue(emojiText);
    }, [selectedEmojis]);

    // Update TextInput cursor position when cursor position changes externally
    (0, _react.useEffect)(function () {
      if (textInputRef.current) {
        textInputRef.current.setNativeProps({
          selection: {
            start: cursorPosition,
            end: cursorPosition
          }
        });
      }
    }, [cursorPosition]);

    // Animate in when component mounts
    (0, _react.useEffect)(function () {
      _reactNative.Animated.parallel([_reactNative.Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }), _reactNative.Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true
      })]).start();
    }, [fadeAnim, scaleAnim]);
    var handleDone = (0, _react.useCallback)(function () {
      // Add a subtle press animation
      _reactNative.Animated.sequence([_reactNative.Animated.timing(scaleAnim, {
        toValue: 0.96,
        duration: 100,
        useNativeDriver: true
      }), _reactNative.Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true
      })]).start(function () {
        onDone();
      });
    }, [onDone, scaleAnim]);

    // Handle text input changes (mainly for backspace/delete functionality)
    var handleTextChange = (0, _react.useCallback)(function (text) {
      // If text is shorter than current text, user pressed backspace
      if (text.length < textValue.length && onRemoveEmojiAtPosition) {
        var emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition, selectedEmojis);

        // For backspace behavior, if cursor is at the beginning of an emoji, delete the previous one
        if (cursorPosition > 0) {
          emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition - 1, selectedEmojis);
        }
        if (emojiIndex >= 0 && emojiIndex < selectedEmojis.length) {
          onRemoveEmojiAtPosition(emojiIndex);
        }
      }
    }, [textValue, cursorPosition, selectedEmojis, onRemoveEmojiAtPosition, getEmojiIndexFromCursorPosition]);

    // Handle cursor position changes
    var handleSelectionChange = (0, _react.useCallback)(function (event) {
      var start = event.nativeEvent.selection.start;
      console.log('📝 EmojiPickerPreview cursor position changed:', start);
      console.log('📝 Text value length:', textValue.length);
      setCursorPosition(start);
      onCursorPositionChange == null ? undefined : onCursorPositionChange(start);
    }, [onCursorPositionChange, textValue]);
    if (selectedEmojis.length === 0) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
        style: [styles.container, {
          opacity: fadeAnim,
          transform: [{
            scale: scaleAnim
          }]
        }],
        testID: `${testID}.container`
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {
      style: [styles.container, {
        opacity: fadeAnim,
        transform: [{
          scale: scaleAnim
        }]
      }],
      testID: `${testID}.container`,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.header,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.doneButton,
          onPress: handleDone,
          testID: `${testID}.done_button`,
          activeOpacity: 0.8,
          delayPressIn: 0,
          delayPressOut: 50,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.doneButtonText,
            children: "\u062A\u0645"
          })
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.textInputContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TextInput, {
          ref: textInputRef,
          style: styles.textInput,
          value: textValue,
          onChangeText: handleTextChange,
          onSelectionChange: handleSelectionChange,
          multiline: true,
          editable: true,
          showSoftInputOnFocus: false,
          testID: `${testID}.text_input`,
          selectionColor: theme.buttonBg
        })
      })]
    });
  };
  var _default = exports.default = EmojiPickerPreview;
