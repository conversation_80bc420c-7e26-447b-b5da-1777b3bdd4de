  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Ionicons = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        justifyContent: 'space-between',
        backgroundColor: theme.centerChannelBg,
        height: 55,
        paddingHorizontal: 12,
        paddingTop: 11,
        borderTopColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.08),
        borderTopWidth: 1,
        flexDirection: 'row',
        alignItems: 'center',
        minHeight: 55 // Ensure consistent height
      },
      categoryIconsContainer: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center'
      },
      backspaceButton: {
        width: 36,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: -2,
        right: 8,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.errorTextColor, 0.15),
        borderRadius: 8,
        borderWidth: 1.5,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.errorTextColor, 0.3)
      }
    };
  });
  var EmojiCategoryBar = function EmojiCategoryBar(_ref) {
    var onSelect = _ref.onSelect,
      _ref$selectedEmojis = _ref.selectedEmojis,
      selectedEmojis = _ref$selectedEmojis === undefined ? [] : _ref$selectedEmojis,
      _ref$cursorPosition = _ref.cursorPosition,
      cursorPosition = _ref$cursorPosition === undefined ? 0 : _ref$cursorPosition,
      onRemoveEmojiAtPosition = _ref.onRemoveEmojiAtPosition,
      onRemoveAllEmojis = _ref.onRemoveAllEmojis;
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var styles = getStyleSheet(theme);
    var _useEmojiCategoryBar = (0, _$$_REQUIRE(_dependencyMap[8]).useEmojiCategoryBar)(),
      currentIndex = _useEmojiCategoryBar.currentIndex,
      icons = _useEmojiCategoryBar.icons;

    // Preserve icons to prevent them from disappearing
    var preservedIcons = (0, _react.useRef)(icons);
    (0, _react.useEffect)(function () {
      if (icons && icons.length > 0) {
        preservedIcons.current = icons;
      }
    }, [icons]);

    // Use preserved icons if current icons are undefined
    var displayIcons = icons || preservedIcons.current;

    // Log component render
    console.log('🔧 EmojiCategoryBar: always visible - emojis =', selectedEmojis.length, 'icons =', !!displayIcons);

    // Helper function to convert cursor position to emoji index
    var getEmojiIndexFromCursorPosition = (0, _react.useCallback)(function (cursor, emojis) {
      if (emojis.length === 0) return -1;
      var charCount = 0;
      for (var i = 0; i < emojis.length; i++) {
        var emojiLength = emojis[i].character.length;

        // If cursor is within this emoji's character range
        if (cursor <= charCount + emojiLength) {
          return i;
        }
        charCount += emojiLength;
      }

      // If cursor is at the end or beyond, return the last emoji index
      return emojis.length - 1;
    }, []);
    var scrollToIndex = (0, _react.useCallback)(function (index) {
      if (onSelect) {
        onSelect(index);
        return;
      }
      (0, _$$_REQUIRE(_dependencyMap[8]).selectEmojiCategoryBarSection)(index);
    }, []);

    // Handle single emoji deletion at cursor position - always executes
    var handleBackspacePress = (0, _react.useCallback)(function () {
      console.log('🚀 handleBackspacePress - emojis:', selectedEmojis.length);
      if (selectedEmojis.length > 0 && onRemoveEmojiAtPosition) {
        var emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition, selectedEmojis);

        // For backspace behavior, if cursor is at the beginning of an emoji, delete the previous one
        if (cursorPosition > 0) {
          emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition - 1, selectedEmojis);
        }
        if (emojiIndex >= 0 && emojiIndex < selectedEmojis.length) {
          console.log('🚀 Deleting emoji at index:', emojiIndex);
          onRemoveEmojiAtPosition(emojiIndex);
        }
      } else {
        console.log('🚀 No emojis to delete or callback not available');
      }
    }, [cursorPosition, selectedEmojis, onRemoveEmojiAtPosition, getEmojiIndexFromCursorPosition]);

    // Handle multiple emoji deletion (long press) - always executes
    var handleBackspaceLongPress = (0, _react.useCallback)(function () {
      console.log('🔥 handleBackspaceLongPress - emojis:', selectedEmojis.length);
      if (selectedEmojis.length > 0 && onRemoveAllEmojis) {
        _reactNative.Alert.alert('Clear All Emojis', `Are you sure you want to remove all ${selectedEmojis.length} emojis?`, [{
          text: 'Cancel',
          style: 'cancel'
        }, {
          text: 'Clear All',
          style: 'destructive',
          onPress: function onPress() {
            console.log('🔥 Clearing all emojis');
            onRemoveAllEmojis();
          }
        }]);
      } else {
        console.log('🔥 No emojis to clear or callback not available');
      }
    }, [selectedEmojis, onRemoveAllEmojis]);

    // Category bar is ALWAYS rendered - no conditional logic

    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      testID: "emoji_picker.category_bar",
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.backspaceButton,
        hitSlop: {
          top: 8,
          bottom: 8,
          left: 8,
          right: 8
        },
        onPress: function onPress() {
          console.log('👆 Backspace button pressed - emojis:', selectedEmojis.length);
          handleBackspacePress();
        },
        onLongPress: function onLongPress() {
          console.log('👆 Backspace button long pressed - emojis:', selectedEmojis.length);
          handleBackspaceLongPress();
        },
        delayLongPress: 500,
        testID: "emoji_picker.category_bar.backspace_button",
        activeOpacity: 0.6,
        accessibilityLabel: "Delete emoji",
        accessibilityRole: "button",
        accessibilityHint: "Tap to delete emoji at cursor position, long press to delete all emojis",
        children: _react.default.createElement(_Ionicons.default, {
          name: "backspace-outline",
          size: 24,
          color: "red"
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.categoryIconsContainer,
        children: displayIcons == null ? undefined : displayIcons.map(function (icon, index) {
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_icon.default, {
            currentIndex: currentIndex,
            icon: icon.icon,
            index: index,
            scrollToIndex: scrollToIndex,
            theme: theme
          }, icon.key);
        })
      })]
    });
  };
  var _default = exports.default = EmojiCategoryBar;
