  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var hitSlop = {
    top: 10,
    bottom: 10,
    left: 10,
    right: 10
  };
  var CloseButton = function CloseButton(_ref) {
    var collapse = _ref.collapse;
    var theme = (0, _$$_REQUIRE(_dependencyMap[5]).useTheme)();
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      hitSlop: hitSlop,
      onPress: collapse,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        name: "close",
        size: 24,
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.56)
      })
    });
  };
  var _default = exports.default = CloseButton;
