  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNativeWalkthroughTooltip = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _touchable_emoji = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _close_button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _skin_selector = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _tooltip = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
      // backgroundColor:'green',
      height: 35
    },
    expanded: {
      alignItems: 'center',
      flexDirection: 'row',
      width: '100%',
      zIndex: 2
    },
    tooltipStyle: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2
      },
      shadowRadius: 2,
      shadowOpacity: 0.16
    }
  });
  var skins = Object.keys(_$$_REQUIRE(_dependencyMap[11]).skinCodes).reduce(function (result, value) {
    var skin = _$$_REQUIRE(_dependencyMap[11]).skinCodes[value];
    if (value === 'default') {
      result[value] = 'hand';
    } else {
      result[value] = `hand_${skin}`;
    }
    return result;
  }, {});
  var _worklet_12007473673340_init_data = {
    code: "function skintone_selectorTsx1(values){const{withTiming,containerWidth}=this.__closure;const animations={originX:withTiming(containerWidth.value,{duration:250}),opacity:withTiming(0,{duration:250})};const initialValues={originX:values.currentOriginX,opacity:1};return{initialValues:initialValues,animations:animations};}"
  };
  var _worklet_10693178941663_init_data = {
    code: "function skintone_selectorTsx2(values){const{withTiming,containerWidth}=this.__closure;const animations={originX:withTiming(values.targetOriginX,{duration:250}),opacity:withTiming(1,{duration:300})};const initialValues={originX:containerWidth.value-122,opacity:0};return{initialValues:initialValues,animations:animations};}"
  };
  var _worklet_16881077069733_init_data = {
    code: "function skintone_selectorTsx3(){const{withDelay,isSearching,withTiming,Platform}=this.__closure;return{width:withDelay(isSearching.value?0:700,withTiming(isSearching.value?0:32,{duration:isSearching.value?50:300})),marginLeft:Platform.OS==='android'?10:undefined,height:34};}"
  };
  var _worklet_13824696563411_init_data = {
    code: "function skintone_selectorTsx4(){const{withDelay,isSearching,withTiming}=this.__closure;return{opacity:withDelay(isSearching.value?0:700,withTiming(isSearching.value?0:1,{duration:isSearching.value?50:350}))};}"
  };
  var SkinToneSelector = function SkinToneSelector(_ref) {
    var _ref$skinTone = _ref.skinTone,
      skinTone = _ref$skinTone === undefined ? 'default' : _ref$skinTone,
      containerWidth = _ref.containerWidth,
      isSearching = _ref.isSearching,
      tutorialWatched = _ref.tutorialWatched;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      expanded = _useState2[0],
      setExpanded = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      tooltipVisible = _useState4[0],
      setTooltipVisible = _useState4[1];
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[12]).useIsTablet)();
    var tooltipContentStyle = (0, _react.useMemo)(function () {
      return {
        borderRadius: 8,
        maxWidth: isTablet ? 352 : undefined,
        padding: 0
      };
    }, [isTablet]);
    var exiting = (0, _react.useCallback)(function () {
      var skintone_selectorTsx1 = function skintone_selectorTsx1(values) {
        var animations = {
          originX: (0, _reactNativeReanimated.withTiming)(containerWidth.value, {
            duration: 250
          }),
          opacity: (0, _reactNativeReanimated.withTiming)(0, {
            duration: 250
          })
        };
        var initialValues = {
          originX: values.currentOriginX,
          opacity: 1
        };
        return {
          initialValues: initialValues,
          animations: animations
        };
      };
      skintone_selectorTsx1.__closure = {
        withTiming: _reactNativeReanimated.withTiming,
        containerWidth: containerWidth
      };
      skintone_selectorTsx1.__workletHash = 12007473673340;
      skintone_selectorTsx1.__initData = _worklet_12007473673340_init_data;
      return skintone_selectorTsx1;
    }(), [containerWidth.value]);
    var entering = (0, _react.useCallback)(function () {
      var skintone_selectorTsx2 = function skintone_selectorTsx2(values) {
        var animations = {
          originX: (0, _reactNativeReanimated.withTiming)(values.targetOriginX, {
            duration: 250
          }),
          opacity: (0, _reactNativeReanimated.withTiming)(1, {
            duration: 300
          })
        };
        var initialValues = {
          originX: containerWidth.value - 122,
          opacity: 0
        };
        return {
          initialValues: initialValues,
          animations: animations
        };
      };
      skintone_selectorTsx2.__closure = {
        withTiming: _reactNativeReanimated.withTiming,
        containerWidth: containerWidth
      };
      skintone_selectorTsx2.__workletHash = 10693178941663;
      skintone_selectorTsx2.__initData = _worklet_10693178941663_init_data;
      return skintone_selectorTsx2;
    }(), [containerWidth.value]);
    var collapse = (0, _react.useCallback)(function () {
      setExpanded(false);
    }, []);
    var expand = (0, _react.useCallback)(function () {
      setExpanded(true);
    }, []);
    var close = (0, _react.useCallback)(function () {
      setTooltipVisible(false);
      (0, _$$_REQUIRE(_dependencyMap[13]).storeSkinEmojiSelectorTutorial)();
    }, []);
    var widthAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var skintone_selectorTsx3 = function skintone_selectorTsx3() {
        return {
          width: (0, _reactNativeReanimated.withDelay)(isSearching.value ? 0 : 700, (0, _reactNativeReanimated.withTiming)(isSearching.value ? 0 : 32, {
            duration: isSearching.value ? 50 : 300
          })),
          marginLeft: _reactNative.Platform.OS === 'android' ? 10 : undefined,
          height: 34
        };
      };
      skintone_selectorTsx3.__closure = {
        withDelay: _reactNativeReanimated.withDelay,
        isSearching: isSearching,
        withTiming: _reactNativeReanimated.withTiming,
        Platform: _reactNative.Platform
      };
      skintone_selectorTsx3.__workletHash = 16881077069733;
      skintone_selectorTsx3.__initData = _worklet_16881077069733_init_data;
      return skintone_selectorTsx3;
    }(), []);
    var opacityStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var skintone_selectorTsx4 = function skintone_selectorTsx4() {
        return {
          opacity: (0, _reactNativeReanimated.withDelay)(isSearching.value ? 0 : 700, (0, _reactNativeReanimated.withTiming)(isSearching.value ? 0 : 1, {
            duration: isSearching.value ? 50 : 350
          }))
        };
      };
      skintone_selectorTsx4.__closure = {
        withDelay: _reactNativeReanimated.withDelay,
        isSearching: isSearching,
        withTiming: _reactNativeReanimated.withTiming
      };
      skintone_selectorTsx4.__workletHash = 13824696563411;
      skintone_selectorTsx4.__initData = _worklet_13824696563411_init_data;
      return skintone_selectorTsx4;
    }(), []);
    (0, _react.useEffect)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        if (!tutorialWatched) {
          setTooltipVisible(true);
        }
      });
    }, [tutorialWatched]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [!expanded && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeWalkthroughTooltip.default, {
        isVisible: tooltipVisible,
        useInteractionManager: true,
        contentStyle: tooltipContentStyle,
        content: /*#__PURE__*/(0, _jsxRuntime.jsx)(_tooltip.default, {
          onClose: close
        }),
        placement: isTablet ? 'left' : 'top',
        onClose: close,
        tooltipStyle: styles.tooltipStyle,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: widthAnimatedStyle,
          exiting: _reactNativeReanimated.FadeOut,
          entering: _reactNativeReanimated.FadeIn,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
            style: [styles.container, opacityStyle],
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_emoji.default, {
              name: skins[skinTone],
              onEmojiPress: expand,
              size: 28
            })
          })
        })
      }), expanded && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: styles.expanded,
        entering: entering,
        exiting: exiting,
        children: [!isTablet && /*#__PURE__*/(0, _jsxRuntime.jsx)(_close_button.default, {
          collapse: collapse
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_skin_selector.default, {
          selected: skinTone,
          skins: skins,
          onSelectSkin: collapse
        }), isTablet && /*#__PURE__*/(0, _jsxRuntime.jsx)(_close_button.default, {
          collapse: collapse
        })]
      })]
    });
  };
  var _default = exports.default = SkinToneSelector;
