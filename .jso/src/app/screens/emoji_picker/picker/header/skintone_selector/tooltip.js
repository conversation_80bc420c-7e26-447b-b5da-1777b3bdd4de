  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var hitSlop = {
    top: 10,
    bottom: 10,
    left: 10,
    right: 10
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginHorizontal: 24
    },
    close: {
      flex: 1,
      alignItems: 'flex-end',
      marginLeft: 11
    },
    descriptionContainer: {
      marginBottom: 24,
      marginTop: 12
    },
    description: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200, 'Light')),
    titleContainer: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: 22
    },
    title: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200, 'SemiBold'))
  });
  var SkinSelectorTooltip = function SkinSelectorTooltip(_ref) {
    var onClose = _ref.onClose;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.titleContainer,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "skintone_selector.tooltip.title",
          defaultMessage: "Choose your default skin tone",
          style: [styles.title, {
            color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)('black', 0.66)
          }],
          testID: "skin_selector.tooltip.title"
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.close,
          hitSlop: hitSlop,
          onPress: onClose,
          testID: "skin_selector.tooltip.close.button",
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
            color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(_$$_REQUIRE(_dependencyMap[8]).Preferences.THEMES.denim.centerChannelColor, 0.56),
            name: "close",
            size: 18
          })
        })]
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.descriptionContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "skintone_selector.tooltip.description",
          defaultMessage: "You can now choose the skin tone you prefer to use for your emojis.",
          style: [styles.description, {
            color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)('black', 0.56)
          }],
          testID: "skin_selector.tooltip.description"
        })
      })]
    });
  };
  var _default = exports.default = SkinSelectorTooltip;
