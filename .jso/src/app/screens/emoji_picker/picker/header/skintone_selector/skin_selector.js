  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _touchable_emoji = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        width: 42,
        alignItems: 'center',
        justifyContent: 'center'
      },
      selected: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.buttonBg, 0.08),
        borderRadius: 4
      },
      skins: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between'
      },
      textContainer: {
        marginHorizontal: 16,
        maxWidth: 57
      },
      text: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 75, 'SemiBold'))
    };
  });
  var SkinSelector = function SkinSelector(_ref) {
    var onSelectSkin = _ref.onSelectSkin,
      selected = _ref.selected,
      skins = _ref.skins;
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[9]).useIsTablet)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[11]).useServerUrl)();
    var styles = getStyleSheet(theme);
    var handleSelectSkin = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (emoji) {
        var skin = emoji.split('hand_')[1] || 'default';
        var code = Object.keys(_$$_REQUIRE(_dependencyMap[12]).skinCodes).find(function (key) {
          return _$$_REQUIRE(_dependencyMap[12]).skinCodes[key] === skin;
        }) || 'default';
        yield (0, _$$_REQUIRE(_dependencyMap[13]).savePreferredSkinTone)(serverUrl, code);
        onSelectSkin();
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }(), [serverUrl]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.textContainer, isTablet && {
          marginLeft: 0
        }],
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "default_skin_tone",
          defaultMessage: "Default Skin Tone",
          style: styles.text
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.skins, isTablet && {
          marginRight: 10
        }],
        children: Object.keys(skins).map(function (key) {
          var name = skins[key];
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: [styles.container, selected === key && styles.selected],
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_emoji.default, {
              name: name,
              size: 28,
              onEmojiPress: handleSelectSkin
            })
          }, name);
        })
      })]
    });
  };
  var _default = exports.default = SkinSelector;
