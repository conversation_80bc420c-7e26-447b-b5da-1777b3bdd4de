  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _bottom_sheet_search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _skintone_selector = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var _excluded = ["skinTone"]; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    },
    row: {
      flexDirection: 'row'
    }
  });
  var PickerHeader = function PickerHeader(_ref) {
    var skinTone = _ref.skinTone,
      props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[8]).useIsTablet)();
    var containerWidth = (0, _$$_REQUIRE(_dependencyMap[9]).useSharedValue)(0);
    var isSearching = (0, _$$_REQUIRE(_dependencyMap[9]).useSharedValue)(false);
    (0, _react.useEffect)(function () {
      var req = requestAnimationFrame(function () {
        (0, _$$_REQUIRE(_dependencyMap[10]).setEmojiSkinTone)(skinTone);
      });
      return function () {
        return cancelAnimationFrame(req);
      };
    }, [skinTone]);
    var onBlur = (0, _react.useCallback)(function () {
      isSearching.value = false;
    }, []);
    var onFocus = (0, _react.useCallback)(function () {
      isSearching.value = true;
    }, []);
    var onLayout = (0, _react.useCallback)(function (e) {
      containerWidth.value = e.nativeEvent.layout.width;
    }, []);
    var search;
    if (isTablet) {
      search = /*#__PURE__*/(0, _jsxRuntime.jsx)(_search.default, Object.assign({}, props, {
        onBlur: onBlur,
        onFocus: onFocus
      }));
    } else {
      search = /*#__PURE__*/(0, _jsxRuntime.jsx)(_bottom_sheet_search.default, Object.assign({}, props, {
        onBlur: onBlur,
        onFocus: onFocus
      }));
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      onLayout: onLayout,
      style: styles.row,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.flex,
        children: search
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_skintone_selector.default, {
        skinTone: skinTone,
        containerWidth: containerWidth,
        isSearching: isSearching
      })]
    });
  };
  var _default = exports.default = PickerHeader;
