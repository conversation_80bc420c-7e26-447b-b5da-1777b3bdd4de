  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _header = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)([], function (_ref) {
    var database = _ref.database;
    return {
      skinTone: (0, _$$_REQUIRE(_dependencyMap[3]).queryEmojiPreferences)(database, _$$_REQUIRE(_dependencyMap[4]).Preferences.EMOJI_SKINTONE).observeWithColumns(['value']).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (prefs) {
        var _prefs$0$value, _prefs$;
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)((_prefs$0$value = prefs == null ? undefined : (_prefs$ = prefs[0]) == null ? undefined : _prefs$.value) != null ? _prefs$0$value : 'default');
      }))
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_header.default));
