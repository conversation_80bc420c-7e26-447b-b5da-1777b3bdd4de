  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var _excluded = ["onFocus"]; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  var BottomSheetSearch = function BottomSheetSearch(_ref) {
    var onFocus = _ref.onFocus,
      props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
    var _useBottomSheet = (0, _$$_REQUIRE(_dependencyMap[5]).useBottomSheet)(),
      expand = _useBottomSheet.expand;
    var handleOnFocus = (0, _react.useCallback)(function (event) {
      expand();
      onFocus == null ? undefined : onFocus(event);
    }, [onFocus, expand]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_search.default, Object.assign({
      onFocus: handleOnFocus
    }, props));
  };
  var _default = exports.default = BottomSheetSearch;
