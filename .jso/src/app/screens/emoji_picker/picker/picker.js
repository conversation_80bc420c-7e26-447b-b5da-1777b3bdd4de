  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.SCROLLVIEW_NATIVE_ID = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _filtered = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _header = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _sections = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _preview = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var SCROLLVIEW_NATIVE_ID = exports.SCROLLVIEW_NATIVE_ID = 'emojiSelector';
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    },
    searchBar: {
      paddingBottom: 5
    },
    emojiListContainer: {
      flex: 1
    }
  });
  var Picker = function Picker(_ref) {
    var customEmojis = _ref.customEmojis,
      customEmojisEnabled = _ref.customEmojisEnabled,
      file = _ref.file,
      imageUrl = _ref.imageUrl,
      onEmojiPress = _ref.onEmojiPress,
      recentEmojis = _ref.recentEmojis,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? '' : _ref$testID,
      _ref$selectedEmojis = _ref.selectedEmojis,
      selectedEmojis = _ref$selectedEmojis === undefined ? [] : _ref$selectedEmojis,
      onRemoveEmoji = _ref.onRemoveEmoji,
      onRemoveEmojiAtPosition = _ref.onRemoveEmojiAtPosition,
      cursorPosition = _ref.cursorPosition,
      onCursorPositionChange = _ref.onCursorPositionChange,
      onDone = _ref.onDone,
      _ref$showPreview = _ref.showPreview,
      showPreview = _ref$showPreview === undefined ? false : _ref$showPreview;
    var theme = (0, _$$_REQUIRE(_dependencyMap[9]).useTheme)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[10]).useServerUrl)();
    var _useState = (0, _react.useState)(),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      searchTerm = _useState2[0],
      setSearchTerm = _useState2[1];
    var onCancelSearch = (0, _react.useCallback)(function () {
      return setSearchTerm(undefined);
    }, []);
    var onChangeSearchTerm = (0, _react.useCallback)(function (text) {
      setSearchTerm(text);
      searchCustom(text.replace(/^:|:$/g, '').trim());
    }, []);
    var searchCustom = (0, _$$_REQUIRE(_dependencyMap[11]).debounce)(function (text) {
      if (text && text.length > 1) {
        (0, _$$_REQUIRE(_dependencyMap[12]).searchCustomEmojis)(serverUrl, text);
      }
    }, 500);
    var EmojiList = null;
    var term = searchTerm == null ? undefined : searchTerm.replace(/^:|:$/g, '').trim();
    if (term) {
      EmojiList = /*#__PURE__*/(0, _jsxRuntime.jsx)(_filtered.default, {
        customEmojis: customEmojis,
        searchTerm: term,
        onEmojiPress: onEmojiPress
      });
    } else {
      EmojiList = /*#__PURE__*/(0, _jsxRuntime.jsx)(_sections.default, {
        customEmojis: customEmojis,
        customEmojisEnabled: customEmojisEnabled,
        imageUrl: imageUrl,
        file: file,
        onEmojiPress: onEmojiPress,
        recentEmojis: recentEmojis
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.flex,
      testID: `${testID}.screen`,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.searchBar,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_header.default, {
          autoCapitalize: "none",
          keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[13]).getKeyboardAppearanceFromTheme)(theme),
          onCancel: onCancelSearch,
          onChangeText: onChangeSearchTerm,
          testID: `${testID}.search_bar`,
          value: searchTerm
        })
      }), showPreview && /*#__PURE__*/(0, _jsxRuntime.jsx)(_preview.default, {
        selectedEmojis: selectedEmojis,
        onRemoveEmoji: onRemoveEmoji,
        onRemoveEmojiAtPosition: onRemoveEmojiAtPosition,
        onCursorPositionChange: onCursorPositionChange,
        onDone: onDone,
        testID: `${testID}.preview`
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.emojiListContainer,
        children: EmojiList
      })]
    });
  };
  var _default = exports.default = Picker;
