  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _emoji = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheetFromTheme = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        borderBottomWidth: 1,
        borderBottomColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.2),
        overflow: 'hidden'
      },
      emojiContainer: {
        marginRight: 5
      },
      emoji: {
        color: '#000'
      },
      emojiText: {
        fontSize: 13,
        color: theme.centerChannelColor
      }
    };
  });
  var EmojiTouchable = function EmojiTouchable(_ref) {
    var name = _ref.name,
      onEmojiPress = _ref.onEmojiPress;
    var theme = (0, _$$_REQUIRE(_dependencyMap[6]).useTheme)();
    var style = getStyleSheetFromTheme(theme);
    var onPress = (0, _react.useCallback)(function () {
      return onEmojiPress(name);
    }, []);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: onPress,
      style: style.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.emojiContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_emoji.default, {
          emojiName: name,
          textStyle: style.emoji,
          size: 20
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: style.emojiText,
        children: `:${name}:`
      })]
    });
  };
  var _default = exports.default = (0, _react.memo)(EmojiTouchable);
