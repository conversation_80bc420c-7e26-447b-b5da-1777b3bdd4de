  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _fuse = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _no_results_with_term = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _emoji_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var style = _reactNative.StyleSheet.create({
    noResultContainer: {
      flexGrow: 1,
      alignItems: 'center',
      justifyContent: 'center'
    }
  });
  var EmojiFiltered = function EmojiFiltered(_ref) {
    var customEmojis = _ref.customEmojis,
      skinTone = _ref.skinTone,
      searchTerm = _ref.searchTerm,
      onEmojiPress = _ref.onEmojiPress;
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[7]).useIsTablet)();
    var emojis = (0, _react.useMemo)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[8]).getEmojis)(skinTone, customEmojis);
    }, [skinTone, customEmojis]);
    var fuse = (0, _react.useMemo)(function () {
      var options = {
        findAllMatches: true,
        ignoreLocation: true,
        includeMatches: true,
        shouldSort: false,
        includeScore: true
      };
      return new _fuse.default(emojis, options);
    }, [emojis]);
    var data = (0, _react.useMemo)(function () {
      if (!searchTerm) {
        return [];
      }
      return (0, _$$_REQUIRE(_dependencyMap[8]).searchEmojis)(fuse, searchTerm);
    }, [fuse, searchTerm]);
    var List = (0, _react.useMemo)(function () {
      return isTablet ? _reactNative.FlatList : _$$_REQUIRE(_dependencyMap[9]).BottomSheetFlatList;
    }, [isTablet]);
    var keyExtractor = (0, _react.useCallback)(function (item) {
      return item;
    }, []);
    var renderEmpty = (0, _react.useCallback)(function () {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.noResultContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_no_results_with_term.default, {
          term: searchTerm
        })
      });
    }, [searchTerm]);
    var renderItem = (0, _react.useCallback)(function (_ref2) {
      var item = _ref2.item;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_emoji_item.default, {
        onEmojiPress: onEmojiPress,
        name: item
      });
    }, []);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(List, {
      data: data,
      initialNumToRender: 30,
      keyboardDismissMode: "interactive",
      keyboardShouldPersistTaps: "always",
      keyExtractor: keyExtractor,
      ListEmptyComponent: renderEmpty,
      renderItem: renderItem,
      removeClippedSubviews: false
    });
  };
  var _default = exports.default = EmojiFiltered;
