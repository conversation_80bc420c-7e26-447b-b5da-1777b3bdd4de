  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _emoji_category_bar2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var _excluded = ["selectedEmojis", "cursorPosition", "onRemoveEmojiAtPosition", "onRemoveAllEmojis"]; // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  var _worklet_17380111277517_init_data = {
    code: "function indexTsx1(){const{withTiming,Platform,theme}=this.__closure;const paddingBottom=withTiming(Platform.OS==='ios'?20:0,{duration:250});return{backgroundColor:theme.centerChannelBg,paddingBottom:paddingBottom};}"
  };
  var _worklet_1628239681771_init_data = {
    code: "function indexTsx2(){const{keyboardHeight,Platform}=this.__closure;let height=55;if(keyboardHeight===0&&Platform.OS==='ios'){height+=20;}else if(keyboardHeight){height=0;}return{height:height};}"
  };
  var PickerFooter = function PickerFooter(_ref) {
    var _ref$selectedEmojis = _ref.selectedEmojis,
      selectedEmojis = _ref$selectedEmojis === undefined ? [] : _ref$selectedEmojis,
      _ref$cursorPosition = _ref.cursorPosition,
      cursorPosition = _ref$cursorPosition === undefined ? 0 : _ref$cursorPosition,
      onRemoveEmojiAtPosition = _ref.onRemoveEmojiAtPosition,
      onRemoveAllEmojis = _ref.onRemoveAllEmojis,
      props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var keyboardHeight = (0, _$$_REQUIRE(_dependencyMap[8]).useKeyboardHeight)();
    var _useBottomSheetIntern = (0, _$$_REQUIRE(_dependencyMap[9]).useBottomSheetInternal)(),
      animatedSheetState = _useBottomSheetIntern.animatedSheetState;
    var _useBottomSheet = (0, _$$_REQUIRE(_dependencyMap[9]).useBottomSheet)(),
      expand = _useBottomSheet.expand;

    // Log props received by PickerFooter (simplified)
    console.log('📋 PickerFooter render - emojis:', selectedEmojis.length, 'callbacks available:', !!(onRemoveEmojiAtPosition && onRemoveAllEmojis));
    var scrollToIndex = (0, _react.useCallback)(function (index) {
      if (animatedSheetState.value === _$$_REQUIRE(_dependencyMap[9]).SHEET_STATE.EXTENDED) {
        (0, _$$_REQUIRE(_dependencyMap[10]).selectEmojiCategoryBarSection)(index);
        return;
      }
      expand();

      // @ts-expect-error wait until the bottom sheet is epanded
      while (animatedSheetState.value !== _$$_REQUIRE(_dependencyMap[9]).SHEET_STATE.EXTENDED) {
        // do nothing
      }
      (0, _$$_REQUIRE(_dependencyMap[10]).selectEmojiCategoryBarSection)(index);
    }, []);
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        var paddingBottom = (0, _reactNativeReanimated.withTiming)(_reactNative.Platform.OS === 'ios' ? 20 : 0, {
          duration: 250
        });
        return {
          backgroundColor: theme.centerChannelBg,
          paddingBottom: paddingBottom
        };
      };
      indexTsx1.__closure = {
        withTiming: _reactNativeReanimated.withTiming,
        Platform: _reactNative.Platform,
        theme: theme
      };
      indexTsx1.__workletHash = 17380111277517;
      indexTsx1.__initData = _worklet_17380111277517_init_data;
      return indexTsx1;
    }(), [theme]);
    var heightAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx2 = function indexTsx2() {
        var height = 55;
        if (keyboardHeight === 0 && _reactNative.Platform.OS === 'ios') {
          height += 20;
        } else if (keyboardHeight) {
          height = 0;
        }
        return {
          height: height
        };
      };
      indexTsx2.__closure = {
        keyboardHeight: keyboardHeight,
        Platform: _reactNative.Platform
      };
      indexTsx2.__workletHash = 1628239681771;
      indexTsx2.__initData = _worklet_1628239681771_init_data;
      return indexTsx2;
    }(), [keyboardHeight]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[9]).BottomSheetFooter, Object.assign({
      style: heightAnimatedStyle
    }, props, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: [animatedStyle],
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_emoji_category_bar2.default, {
          onSelect: scrollToIndex,
          selectedEmojis: selectedEmojis,
          cursorPosition: cursorPosition,
          onRemoveEmojiAtPosition: onRemoveEmojiAtPosition,
          onRemoveAllEmojis: onRemoveAllEmojis
        })
      })
    }));
  };
  var _default = exports.default = PickerFooter;
