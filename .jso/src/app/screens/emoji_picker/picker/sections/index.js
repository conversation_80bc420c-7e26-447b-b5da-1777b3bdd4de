  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeSectionListGetItemLayout = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _file_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _touchable_emoji = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _emoji_category_bar2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _section_footer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _section_header = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var EMOJI_SIZE = 34;
  var EMOJIS_PER_ROW = 7;
  var EMOJIS_PER_ROW_TABLET = 9;
  var EMOJI_ROW_MARGIN = 12;
  var ICONS = {
    recent: 'clock-outline',
    'smileys-emotion': 'emoticon-happy-outline',
    'people-body': 'account-outline',
    'animals-nature': 'leaf-outline',
    'food-drink': 'food-apple',
    'travel-places': 'airplane-variant',
    activities: 'basketball',
    objects: 'lightbulb-outline',
    symbols: 'heart-outline',
    flags: 'flag-outline',
    custom: 'emoticon-custom-outline'
  };
  var categoryToI18n = {};
  var emojiSectionsByOffset = [];
  var getItemLayout = (0, _reactNativeSectionListGetItemLayout.default)({
    getItemHeight: function getItemHeight() {
      return 46;
    },
    getSectionHeaderHeight: function getSectionHeaderHeight() {
      return _section_header.SECTION_HEADER_HEIGHT;
    },
    sectionOffsetsCallback: function sectionOffsetsCallback(offsetsById) {
      emojiSectionsByOffset = offsetsById;
    }
  });
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    },
    contentContainerStyle: {
      paddingBottom: 50
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: EMOJI_ROW_MARGIN
    },
    emoji: {
      height: EMOJI_SIZE,
      width: EMOJI_SIZE
    },
    imageEmoji: {
      width: 28,
      height: 28
    }
  });
  _$$_REQUIRE(_dependencyMap[14]).CategoryNames.forEach(function (name) {
    if (_$$_REQUIRE(_dependencyMap[14]).CategoryTranslations.has(name) && _$$_REQUIRE(_dependencyMap[14]).CategoryMessage.has(name)) {
      categoryToI18n[name] = {
        id: _$$_REQUIRE(_dependencyMap[14]).CategoryTranslations.get(name),
        defaultMessage: _$$_REQUIRE(_dependencyMap[14]).CategoryMessage.get(name),
        icon: ICONS[name]
      };
    }
  });
  var emptyEmoji = {
    name: '',
    short_name: '',
    aliases: []
  };
  var ImageEmoji = function ImageEmoji(_ref) {
    var file = _ref.file,
      imageUrl = _ref.imageUrl,
      onEmojiPress = _ref.onEmojiPress,
      path = _ref.path;
    var onPress = (0, _react.useCallback)(function () {
      onEmojiPress('');
    }, [onEmojiPress]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.row,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.emoji,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
          onPress: onPress,
          children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [Boolean(file) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_file_icon.default, {
              file: file,
              iconSize: 30
            }), Boolean(imageUrl) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[15]).Image, {
              source: {
                uri: path
              },
              style: styles.imageEmoji
            })]
          })
        })
      })
    });
  };
  var EmojiSections = function EmojiSections(_ref2) {
    var customEmojis = _ref2.customEmojis,
      customEmojisEnabled = _ref2.customEmojisEnabled,
      file = _ref2.file,
      imageUrl = _ref2.imageUrl,
      onEmojiPress = _ref2.onEmojiPress,
      recentEmojis = _ref2.recentEmojis;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[16]).useServerUrl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[17]).useIsTablet)();
    var _useEmojiCategoryBar = (0, _$$_REQUIRE(_dependencyMap[18]).useEmojiCategoryBar)(),
      currentIndex = _useEmojiCategoryBar.currentIndex,
      selectedIndex = _useEmojiCategoryBar.selectedIndex;
    var list = (0, _react.useRef)(null);
    var categoryIndex = (0, _react.useRef)(currentIndex);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      customEmojiPage = _useState2[0],
      setCustomEmojiPage = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      fetchingCustomEmojis = _useState4[0],
      setFetchingCustomEmojis = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      loadedAllCustomEmojis = _useState6[0],
      setLoadedAllCustomEmojis = _useState6[1];
    var offset = (0, _react.useRef)(0);
    var manualScroll = (0, _react.useRef)(false);
    var sections = (0, _react.useMemo)(function () {
      var emojisPerRow = isTablet ? EMOJIS_PER_ROW_TABLET : EMOJIS_PER_ROW;
      var sectionsArray = _$$_REQUIRE(_dependencyMap[14]).CategoryNames.map(function (category) {
        var _EmojiIndicesByCatego;
        var emojiIndices = (_EmojiIndicesByCatego = _$$_REQUIRE(_dependencyMap[14]).EmojiIndicesByCategory.get('default')) == null ? undefined : _EmojiIndicesByCatego.get(category);
        var data;
        switch (category) {
          case 'custom':
            {
              var builtInCustom = emojiIndices.map(_$$_REQUIRE(_dependencyMap[19]).fillEmoji.bind(null, 'custom'));

              // eslint-disable-next-line max-nested-callbacks
              var custom = customEmojisEnabled ? customEmojis.map(function (ce) {
                return {
                  aliases: [],
                  name: ce.name,
                  short_name: ''
                };
              }) : [];
              data = (0, _$$_REQUIRE(_dependencyMap[20]).chunk)(builtInCustom.concat(custom), emojisPerRow);
              break;
            }
          case 'recent':
            // eslint-disable-next-line max-nested-callbacks
            data = (0, _$$_REQUIRE(_dependencyMap[20]).chunk)(recentEmojis.map(function (emoji) {
              return {
                aliases: [],
                name: emoji,
                short_name: ''
              };
            }), EMOJIS_PER_ROW);
            break;
          default:
            data = (0, _$$_REQUIRE(_dependencyMap[20]).chunk)(emojiIndices.map(_$$_REQUIRE(_dependencyMap[19]).fillEmoji.bind(null, category)), emojisPerRow);
            break;
        }
        for (var d of data) {
          if (d.length < emojisPerRow) {
            d.push.apply(d, (0, _toConsumableArray2.default)(new Array(emojisPerRow - d.length).fill(emptyEmoji)));
          }
        }
        return Object.assign({}, categoryToI18n[category], {
          data: data,
          key: category
        });
      }).filter(function (s) {
        return s.data.length;
      });
      if (imageUrl || file) {
        sectionsArray.unshift({
          data: [[{
            aliases: [],
            name: imageUrl || (file == null ? undefined : file.name) || '',
            short_name: imageUrl || (file == null ? undefined : file.name) || '',
            category: 'image'
          }]],
          defaultMessage: 'Default',
          icon: 'bookmark-outline',
          id: 'emoji_picker.default',
          key: 'default',
          renderItem: function renderItem(_ref3) {
            var item = _ref3.item;
            return /*#__PURE__*/(0, _jsxRuntime.jsx)(ImageEmoji, {
              file: file,
              onEmojiPress: onEmojiPress,
              imageUrl: imageUrl,
              path: item[0].name
            });
          }
        });
      }
      return sectionsArray;
    }, [customEmojis, customEmojisEnabled, isTablet, imageUrl, file]);
    (0, _react.useEffect)(function () {
      var icons = sections.map(function (s) {
        return {
          key: s.key,
          icon: s.icon
        };
      });
      console.log('📊 EmojiSections setting category bar icons:', icons);
      (0, _$$_REQUIRE(_dependencyMap[18]).setEmojiCategoryBarIcons)(icons);
    }, [sections]);
    var onLoadMoreCustomEmojis = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!customEmojisEnabled || fetchingCustomEmojis || loadedAllCustomEmojis) {
        return;
      }
      setFetchingCustomEmojis(true);
      var _yield$fetchCustomEmo = yield (0, _$$_REQUIRE(_dependencyMap[21]).fetchCustomEmojis)(serverUrl, customEmojiPage, _$$_REQUIRE(_dependencyMap[22]).EMOJIS_PER_PAGE),
        data = _yield$fetchCustomEmo.data,
        error = _yield$fetchCustomEmo.error;
      if (data != null && data.length) {
        setCustomEmojiPage(customEmojiPage + 1);
      } else if (!error && data && data.length < _$$_REQUIRE(_dependencyMap[22]).EMOJIS_PER_PAGE) {
        setLoadedAllCustomEmojis(true);
      }
      setFetchingCustomEmojis(false);
    }), [customEmojiPage, customEmojisEnabled, loadedAllCustomEmojis, fetchingCustomEmojis]);
    var onScroll = (0, _react.useCallback)(function (e) {
      var contentOffset = e.nativeEvent.contentOffset;
      var direction = contentOffset.y > offset.current ? 'up' : 'down';
      offset.current = contentOffset.y;
      if (manualScroll.current) {
        return;
      }
      var nextIndex = contentOffset.y >= emojiSectionsByOffset[categoryIndex.current + 1] - _section_header.SECTION_HEADER_HEIGHT ? categoryIndex.current + 1 : categoryIndex.current;
      var prevIndex = Math.max(0, contentOffset.y <= emojiSectionsByOffset[categoryIndex.current] - _section_header.SECTION_HEADER_HEIGHT ? categoryIndex.current - 1 : categoryIndex.current);
      if (nextIndex > categoryIndex.current && direction === 'up') {
        categoryIndex.current = nextIndex;
        (0, _$$_REQUIRE(_dependencyMap[18]).setEmojiCategoryBarSection)(nextIndex);
      } else if (prevIndex < categoryIndex.current && direction === 'down') {
        categoryIndex.current = prevIndex;
        (0, _$$_REQUIRE(_dependencyMap[18]).setEmojiCategoryBarSection)(prevIndex);
      }
    }, []);
    var scrollToIndex = function scrollToIndex(index) {
      var _list$current;
      manualScroll.current = true;
      (_list$current = list.current) == null ? undefined : _list$current.scrollToLocation({
        sectionIndex: index,
        itemIndex: 0,
        animated: false,
        viewOffset: 0
      });
      (0, _$$_REQUIRE(_dependencyMap[18]).setEmojiCategoryBarSection)(index);
      setTimeout(function () {
        manualScroll.current = false;
      }, 350);
    };
    var renderSectionHeader = (0, _react.useCallback)(function (_ref5) {
      var section = _ref5.section;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_section_header.default, {
        section: section
      });
    }, []);
    var renderFooter = (0, _react.useMemo)(function () {
      return fetchingCustomEmojis ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_section_footer.default, {}) : null;
    }, [fetchingCustomEmojis]);
    var renderItem = (0, _react.useCallback)(function (_ref6) {
      var item = _ref6.item;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.row,
        children: item.map(function (emoji, index) {
          if (!emoji.name && !emoji.short_name) {
            return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.emoji
            }, `empty-${index.toString()}`);
          }
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_emoji.default, {
            name: emoji.name,
            onEmojiPress: onEmojiPress,
            category: emoji.category
          }, emoji.name);
        })
      });
    }, []);
    var List = (0, _react.useMemo)(function () {
      return isTablet ? _reactNative.SectionList : _$$_REQUIRE(_dependencyMap[23]).BottomSheetSectionList;
    }, [isTablet]);
    (0, _react.useEffect)(function () {
      if (selectedIndex != null) {
        scrollToIndex(selectedIndex);
      }
    }, [selectedIndex]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.flex,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(List

      // @ts-expect-error bottom sheet definition
      , {
        getItemLayout: getItemLayout,
        keyboardDismissMode: "interactive",
        keyboardShouldPersistTaps: "always",
        ListFooterComponent: renderFooter,
        onEndReached: onLoadMoreCustomEmojis,
        onEndReachedThreshold: 2,
        onScroll: onScroll,
        ref: list,
        renderItem: renderItem,
        renderSectionHeader: renderSectionHeader,
        sections: sections,
        contentContainerStyle: styles.contentContainerStyle,
        stickySectionHeadersEnabled: true,
        showsVerticalScrollIndicator: false,
        testID: "emoji_picker.emoji_sections.section_list"
        // Enhanced scrolling configuration for better interaction with horizontal preview
        ,
        scrollEventThrottle: 16,
        removeClippedSubviews: true,
        maxToRenderPerBatch: 10,
        updateCellsBatchingPeriod: 50,
        initialNumToRender: 10,
        windowSize: 10
      }), isTablet && /*#__PURE__*/(0, _jsxRuntime.jsx)(_emoji_category_bar2.default, {})]
    });
  };
  var _default = exports.default = EmojiSections;
