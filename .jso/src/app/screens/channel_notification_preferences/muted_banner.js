  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.MUTED_BANNER_HEIGHT = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var MUTED_BANNER_HEIGHT = exports.MUTED_BANNER_HEIGHT = 200;
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[8]).makeStyleSheetFromTheme)(function (theme) {
    return {
      button: {
        width: '55%'
      },
      container: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.sidebarTextActiveBorder, 0.16),
        borderRadius: 4,
        marginHorizontal: 20,
        marginVertical: 12,
        paddingHorizontal: 16,
        height: MUTED_BANNER_HEIGHT
      },
      contentText: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 200), {
        color: theme.centerChannelColor,
        marginTop: 12,
        marginBottom: 16
      }),
      titleContainer: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 16
      },
      title: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 200), {
        color: theme.centerChannelColor,
        marginLeft: 10,
        paddingTop: 5
      })
    };
  });
  var MutedBanner = function MutedBanner(_ref) {
    var channelId = _ref.channelId;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[10]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[11]).useServerUrl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[12]).useTheme)();
    var styles = getStyleSheet(theme);
    var onPress = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[13]).preventDoubleTap)(function () {
      (0, _$$_REQUIRE(_dependencyMap[14]).toggleMuteChannel)(serverUrl, channelId, false);
    }), [channelId, serverUrl]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
      exiting: _reactNativeReanimated.FlipOutXUp,
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.titleContainer,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          name: "bell-off-outline",
          size: 24,
          color: theme.linkColor
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "channel_notification_preferences.muted_title",
          defaultMessage: "This channel is muted",
          style: styles.title
        })]
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        id: "channel_notification_preferences.muted_content",
        defaultMessage: "You can change the notification settings, but you will not receive notifications until the channel is unmuted.",
        style: styles.contentText
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_button.default, {
        buttonType: "default",
        onPress: onPress,
        text: formatMessage({
          id: 'channel_notification_preferences.unmute_content',
          defaultMessage: 'Unmute channel'
        }),
        theme: theme,
        backgroundStyle: styles.button,
        iconName: "bell-outline",
        iconSize: 18
      })]
    });
  };
  var _default = exports.default = MutedBanner;
