  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _channel_notification_preferences = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var channelId = _ref.channelId,
      database = _ref.database;
    var settings = (0, _$$_REQUIRE(_dependencyMap[4]).observeChannelSettings)(database, channelId);
    var isCRTEnabled = (0, _$$_REQUIRE(_dependencyMap[5]).observeIsCRTEnabled)(database);
    var isMuted = (0, _$$_REQUIRE(_dependencyMap[4]).observeIsMutedSetting)(database, channelId);
    var notifyProps = (0, _$$_REQUIRE(_dependencyMap[6]).observeCurrentUser)(database).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (u) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)((0, _$$_REQUIRE(_dependencyMap[9]).getNotificationProps)(u));
    }));
    var channelType = (0, _$$_REQUIRE(_dependencyMap[4]).observeChannel)(database, channelId).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (c) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(c == null ? undefined : c.type);
    }));
    var hasGMasDMFeature = (0, _$$_REQUIRE(_dependencyMap[10]).observeHasGMasDMFeature)(database);
    var notifyLevel = settings.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (s) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)((s == null ? undefined : s.notifyProps.push) || _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.DEFAULT);
    }));
    var notifyThreadReplies = settings.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (s) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(s == null ? undefined : s.notifyProps.push_threads);
    }));
    var defaultLevel = notifyProps.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (n) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(n == null ? undefined : n.push);
    }), (0, _$$_REQUIRE(_dependencyMap[7]).combineLatestWith)(hasGMasDMFeature, channelType), (0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 3),
        v = _ref3[0],
        hasFeature = _ref3[1],
        cType = _ref3[2];
      var shouldShowwithGMasDMBehavior = hasFeature && (0, _$$_REQUIRE(_dependencyMap[12]).isTypeDMorGM)(cType);
      var defaultLevelToUse = v;
      if (shouldShowwithGMasDMBehavior) {
        if (v === _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.MENTION) {
          defaultLevelToUse = _$$_REQUIRE(_dependencyMap[11]).NotificationLevel.ALL;
        }
      }
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(defaultLevelToUse);
    }));
    var defaultThreadReplies = notifyProps.pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (n) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).of)(n == null ? undefined : n.push_threads);
    }));
    return {
      isCRTEnabled: isCRTEnabled,
      isMuted: isMuted,
      notifyLevel: notifyLevel,
      notifyThreadReplies: notifyThreadReplies,
      defaultLevel: defaultLevel,
      defaultThreadReplies: defaultThreadReplies,
      channelType: channelType,
      hasGMasDMFeature: hasGMasDMFeature
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_channel_notification_preferences.default));
