  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _container = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _did_update = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _navigate_back = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _muted_banner = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _notify_about = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _reset = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _thread_replies = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var ChannelNotificationPreferences = function ChannelNotificationPreferences(_ref) {
    var channelId = _ref.channelId,
      componentId = _ref.componentId,
      defaultLevel = _ref.defaultLevel,
      defaultThreadReplies = _ref.defaultThreadReplies,
      isCRTEnabled = _ref.isCRTEnabled,
      isMuted = _ref.isMuted,
      notifyLevel = _ref.notifyLevel,
      notifyThreadReplies = _ref.notifyThreadReplies,
      channelType = _ref.channelType,
      hasGMasDMFeature = _ref.hasGMasDMFeature;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[13]).useServerUrl)();
    var defaultNotificationReplies = defaultThreadReplies === 'all';
    var diffNotificationLevel = notifyLevel !== _$$_REQUIRE(_dependencyMap[14]).NotificationLevel.DEFAULT && notifyLevel !== defaultLevel;
    var notifyTitleTop = (0, _$$_REQUIRE(_dependencyMap[15]).useSharedValue)((isMuted ? _muted_banner.MUTED_BANNER_HEIGHT : 0) + _notify_about.BLOCK_TITLE_HEIGHT);
    var _useState = (0, _react.useState)(notifyLevel === _$$_REQUIRE(_dependencyMap[14]).NotificationLevel.DEFAULT ? defaultLevel : notifyLevel),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      notifyAbout = _useState2[0],
      setNotifyAbout = _useState2[1];
    var _useState3 = (0, _react.useState)((notifyThreadReplies || defaultThreadReplies) === 'all'),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      threadReplies = _useState4[0],
      setThreadReplies = _useState4[1];
    var _useState5 = (0, _react.useState)(diffNotificationLevel || defaultNotificationReplies !== threadReplies),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      resetDefaultVisible = _useState6[0],
      setResetDefaultVisible = _useState6[1];
    (0, _did_update.default)(function () {
      _reactNative.LayoutAnimation.configureNext(_reactNative.LayoutAnimation.Presets.easeInEaseOut);
    }, [isMuted]);
    var onResetPressed = (0, _react.useCallback)(function () {
      setResetDefaultVisible(false);
      setNotifyAbout(defaultLevel);
      setThreadReplies(defaultNotificationReplies);
    }, [defaultLevel, defaultNotificationReplies]);
    var onNotificationLevel = (0, _react.useCallback)(function (level) {
      setNotifyAbout(level);
      setResetDefaultVisible(level !== defaultLevel || defaultNotificationReplies !== threadReplies);
    }, [defaultLevel, defaultNotificationReplies, threadReplies]);
    var onSetThreadReplies = (0, _react.useCallback)(function (value) {
      setThreadReplies(value);
      setResetDefaultVisible(defaultNotificationReplies !== value || notifyAbout !== defaultLevel);
    }, [defaultLevel, defaultNotificationReplies, notifyAbout]);
    var save = (0, _react.useCallback)(function () {
      var pushThreads = threadReplies ? 'all' : 'mention';
      var notifyAboutToUse = notifyAbout;
      if (notifyAbout === defaultLevel) {
        notifyAboutToUse = _$$_REQUIRE(_dependencyMap[14]).NotificationLevel.DEFAULT;
      }
      if (notifyLevel !== notifyAboutToUse || isCRTEnabled && pushThreads !== notifyThreadReplies) {
        var props = {
          push: notifyAboutToUse
        };
        if (isCRTEnabled) {
          props.push_threads = pushThreads;
        }
        (0, _$$_REQUIRE(_dependencyMap[16]).updateChannelNotifyProps)(serverUrl, channelId, props);
      }
      (0, _$$_REQUIRE(_dependencyMap[17]).popTopScreen)(componentId);
    }, [defaultLevel, channelId, componentId, isCRTEnabled, notifyAbout, notifyLevel, notifyThreadReplies, serverUrl, threadReplies]);
    (0, _navigate_back.default)(save);
    (0, _android_back_handler.default)(componentId, save);
    var showThreadReplies = isCRTEnabled && (!hasGMasDMFeature || !(0, _$$_REQUIRE(_dependencyMap[18]).isTypeDMorGM)(channelType));
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_container.default, {
      testID: "push_notification_settings",
      children: [isMuted && /*#__PURE__*/(0, _jsxRuntime.jsx)(_muted_banner.default, {
        channelId: channelId
      }), resetDefaultVisible && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reset.default, {
        onPress: onResetPressed,
        topPosition: notifyTitleTop
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_notify_about.default, {
        defaultLevel: defaultLevel,
        isMuted: isMuted,
        notifyLevel: notifyAbout,
        notifyTitleTop: notifyTitleTop,
        onPress: onNotificationLevel
      }), showThreadReplies && /*#__PURE__*/(0, _jsxRuntime.jsx)(_thread_replies.default, {
        isSelected: threadReplies,
        onPress: onSetThreadReplies,
        notifyLevel: notifyAbout
      })]
    });
  };
  var _default = exports.default = ChannelNotificationPreferences;
