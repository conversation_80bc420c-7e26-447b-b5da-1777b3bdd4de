  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _block = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _option = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _separator = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var THREAD_REPLIES = {
    id: (0, _$$_REQUIRE(_dependencyMap[6]).t)('channel_notification_preferences.thread_replies'),
    defaultMessage: 'Thread replies'
  };
  var NOTIFY_OPTIONS_THREAD = {
    THREAD_REPLIES: {
      defaultMessage: 'Notify me about replies to threads I’m following in this channel',
      id: (0, _$$_REQUIRE(_dependencyMap[6]).t)('channel_notification_preferences.notification.thread_replies'),
      testID: 'channel_notification_preferences.notification.thread_replies',
      value: 'thread_replies'
    }
  };
  var NotifyAbout = function NotifyAbout(_ref) {
    var isSelected = _ref.isSelected,
      notifyLevel = _ref.notifyLevel,
      onPress = _ref.onPress;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var hiddenStates = [_$$_REQUIRE(_dependencyMap[8]).NotificationLevel.NONE, _$$_REQUIRE(_dependencyMap[8]).NotificationLevel.ALL];
    if (hiddenStates.includes(notifyLevel)) {
      return null;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_block.default, {
      headerText: THREAD_REPLIES,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_option.default, {
        action: onPress,
        label: formatMessage({
          id: NOTIFY_OPTIONS_THREAD.THREAD_REPLIES.id,
          defaultMessage: NOTIFY_OPTIONS_THREAD.THREAD_REPLIES.defaultMessage
        }),
        testID: NOTIFY_OPTIONS_THREAD.THREAD_REPLIES.testID,
        type: "toggle",
        selected: isSelected
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_separator.default, {})]
    });
  };
  var _default = exports.default = NotifyAbout;
