  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        position: 'absolute',
        right: 20,
        zIndex: 1
      },
      row: {
        flexDirection: 'row'
      },
      text: Object.assign({
        color: theme.linkColor,
        marginLeft: 7
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 100))
    };
  });
  var _worklet_4329330641588_init_data = {
    code: "function resetTsx1(){const{withTiming,topPosition}=this.__closure;return{top:withTiming(topPosition.value,{duration:100})};}"
  };
  var ResetToDefault = function ResetToDefault(_ref) {
    var onPress = _ref.onPress,
      topPosition = _ref.topPosition;
    var theme = (0, _$$_REQUIRE(_dependencyMap[9]).useTheme)();
    var styles = getStyleSheet(theme);
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var resetTsx1 = function resetTsx1() {
        return {
          top: (0, _reactNativeReanimated.withTiming)(topPosition.value, {
            duration: 100
          })
        };
      };
      resetTsx1.__closure = {
        withTiming: _reactNativeReanimated.withTiming,
        topPosition: topPosition
      };
      resetTsx1.__workletHash = 4329330641588;
      resetTsx1.__initData = _worklet_4329330641588_init_data;
      return resetTsx1;
    }());
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      style: [styles.container, animatedStyle],
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        onPress: onPress,
        style: styles.row,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          name: "refresh",
          size: 18,
          color: theme.linkColor
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "channel_notification_preferences.reset_default",
          defaultMessage: "Reset to default",
          style: styles.text
        })]
      })
    });
  };
  var _default = exports.default = ResetToDefault;
