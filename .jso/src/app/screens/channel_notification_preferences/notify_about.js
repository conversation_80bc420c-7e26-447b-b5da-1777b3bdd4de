  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.BLOCK_TITLE_HEIGHT = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _block = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _option = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _separator = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var BLOCK_TITLE_HEIGHT = exports.BLOCK_TITLE_HEIGHT = 13;
  var NOTIFY_ABOUT = {
    id: (0, _$$_REQUIRE(_dependencyMap[8]).t)('channel_notification_preferences.notify_about'),
    defaultMessage: 'Notify me about...'
  };
  var NOTIFY_OPTIONS = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _$$_REQUIRE(_dependencyMap[9]).NotificationLevel.ALL, {
    defaultMessage: 'All new messages',
    id: (0, _$$_REQUIRE(_dependencyMap[8]).t)('channel_notification_preferences.notification.all'),
    testID: 'channel_notification_preferences.notification.all',
    value: _$$_REQUIRE(_dependencyMap[9]).NotificationLevel.ALL
  }), _$$_REQUIRE(_dependencyMap[9]).NotificationLevel.MENTION, {
    defaultMessage: 'Mentions only',
    id: (0, _$$_REQUIRE(_dependencyMap[8]).t)('channel_notification_preferences.notification.mention'),
    testID: 'channel_notification_preferences.notification.mention',
    value: _$$_REQUIRE(_dependencyMap[9]).NotificationLevel.MENTION
  }), _$$_REQUIRE(_dependencyMap[9]).NotificationLevel.NONE, {
    defaultMessage: 'Nothing',
    id: (0, _$$_REQUIRE(_dependencyMap[8]).t)('channel_notification_preferences.notification.none'),
    testID: 'channel_notification_preferences.notification.none',
    value: _$$_REQUIRE(_dependencyMap[9]).NotificationLevel.NONE
  });
  var NotifyAbout = function NotifyAbout(_ref) {
    var defaultLevel = _ref.defaultLevel,
      isMuted = _ref.isMuted,
      notifyLevel = _ref.notifyLevel,
      notifyTitleTop = _ref.notifyTitleTop,
      onPress = _ref.onPress;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[10]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var onLayout = (0, _react.useCallback)(function (e) {
      var y = e.nativeEvent.layout.y;
      notifyTitleTop.value = y > 0 ? y + 10 : BLOCK_TITLE_HEIGHT;
    }, []);
    var notifyLevelToUse = notifyLevel;
    if (notifyLevel === _$$_REQUIRE(_dependencyMap[9]).NotificationLevel.DEFAULT) {
      notifyLevelToUse = defaultLevel;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_block.default, {
      headerText: NOTIFY_ABOUT,
      headerStyles: {
        marginTop: isMuted ? 8 : 12
      },
      onLayout: onLayout,
      children: Object.keys(NOTIFY_OPTIONS).map(function (key) {
        var _NOTIFY_OPTIONS$key = NOTIFY_OPTIONS[key],
          id = _NOTIFY_OPTIONS$key.id,
          defaultMessage = _NOTIFY_OPTIONS$key.defaultMessage,
          value = _NOTIFY_OPTIONS$key.value,
          testID = _NOTIFY_OPTIONS$key.testID;
        var defaultOption = key === defaultLevel ? formatMessage({
          id: 'channel_notification_preferences.default',
          defaultMessage: '(default)'
        }) : '';
        var label = `${formatMessage({
          id: id,
          defaultMessage: defaultMessage
        })} ${defaultOption}`;
        return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_option.default, {
            action: onPress,
            label: label,
            selected: notifyLevelToUse === key,
            testID: testID,
            type: "select",
            value: value
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_separator.default, {})]
        }, `notif_pref_option${key}`);
      })
    });
  };
  var _default = exports.default = NotifyAbout;
