  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _react2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _navigation_store = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _clear_after_menu_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); } // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.
  var CLEAR_AFTER = 'update-custom-status-clear-after';
  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[11]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1,
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[11]).changeOpacity)(theme.centerChannelColor, 0.03)
      },
      scrollView: {
        flex: 1,
        paddingTop: 32,
        paddingBottom: 32
      },
      block: {
        borderBottomColor: (0, _$$_REQUIRE(_dependencyMap[11]).changeOpacity)(theme.centerChannelColor, 0.1),
        borderBottomWidth: 1,
        borderTopColor: (0, _$$_REQUIRE(_dependencyMap[11]).changeOpacity)(theme.centerChannelColor, 0.1),
        borderTopWidth: 1
      }
    };
  });
  var ClearAfterModal = /*#__PURE__*/function (_NavigationComponent) {
    function ClearAfterModal(props) {
      var _this;
      (0, _classCallCheck2.default)(this, ClearAfterModal);
      _this = _callSuper(this, ClearAfterModal, [Object.assign({}, props, {
        componentName: 'ClearAfterModal'
      })]);
      _this.onBackPress = function () {
        var componentId = _this.props.componentId;
        if (_navigation_store.default.getVisibleScreen() === componentId) {
          if (_this.props.isModal) {
            (0, _$$_REQUIRE(_dependencyMap[12]).dismissModal)({
              componentId: componentId
            });
          } else {
            (0, _$$_REQUIRE(_dependencyMap[12]).popTopScreen)(componentId);
          }
          return true;
        }
        return false;
      };
      _this.onDone = function () {
        var _this$props = _this.props,
          componentId = _this$props.componentId,
          handleClearAfterClick = _this$props.handleClearAfterClick,
          isModal = _this$props.isModal;
        handleClearAfterClick(_this.state.duration, _this.state.expiresAt);
        if (isModal) {
          (0, _$$_REQUIRE(_dependencyMap[12]).dismissModal)({
            componentId: componentId
          });
          return;
        }
        (0, _$$_REQUIRE(_dependencyMap[12]).popTopScreen)(componentId);
      };
      _this.handleItemClick = function (duration, expiresAt) {
        return _this.setState({
          duration: duration,
          expiresAt: expiresAt,
          showExpiryTime: duration === 'date_and_time' && expiresAt !== ''
        });
      };
      _this.renderClearAfterMenu = function () {
        var _this$props2 = _this.props,
          currentUser = _this$props2.currentUser,
          theme = _this$props2.theme;
        var style = getStyleSheet(theme);
        var duration = _this.state.duration;
        var clearAfterMenu = Object.values(_$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum).map(function (item, index, arr) {
          if (index === arr.length - 1) {
            return null;
          }
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_clear_after_menu_item.default, {
            currentUser: currentUser,
            duration: item,
            handleItemClick: _this.handleItemClick,
            isSelected: duration === item,
            separator: index !== arr.length - 2
          }, item);
        });
        if (clearAfterMenu.length === 0) {
          return null;
        }
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          testID: "custom_status_clear_after.menu",
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: style.block,
            children: clearAfterMenu
          })
        });
      };
      var options = {
        topBar: {
          rightButtons: [{
            color: props.theme.sidebarHeaderTextColor,
            enabled: true,
            id: CLEAR_AFTER,
            showAsAction: 'always',
            testID: 'custom_status_clear_after.done.button',
            text: props.intl.formatMessage({
              id: 'mobile.custom_status.modal_confirm',
              defaultMessage: 'Done'
            })
          }]
        }
      };
      (0, _$$_REQUIRE(_dependencyMap[14]).mergeNavigationOptions)(props.componentId, options);
      _this.state = {
        duration: props.initialDuration,
        expiresAt: '',
        showExpiryTime: false
      };
      return _this;
    }
    (0, _inherits2.default)(ClearAfterModal, _NavigationComponent);
    return (0, _createClass2.default)(ClearAfterModal, [{
      key: "componentDidMount",
      value: function componentDidMount() {
        _$$_REQUIRE(_dependencyMap[15]).Navigation.events().bindComponent(this);
        this.backListener = _reactNative.BackHandler.addEventListener('hardwareBackPress', this.onBackPress);
      }
    }, {
      key: "componentWillUnmount",
      value: function componentWillUnmount() {
        var _this$backListener;
        (_this$backListener = this.backListener) == null ? undefined : _this$backListener.remove();
      }
    }, {
      key: "navigationButtonPressed",
      value: function navigationButtonPressed(_ref) {
        var buttonId = _ref.buttonId;
        switch (buttonId) {
          case CLEAR_AFTER:
            this.onDone();
            break;
        }
      }
    }, {
      key: "render",
      value: function render() {
        var _this$props3 = this.props,
          currentUser = _this$props3.currentUser,
          theme = _this$props3.theme;
        var style = getStyleSheet(theme);
        var _this$state = this.state,
          duration = _this$state.duration,
          expiresAt = _this$state.expiresAt,
          showExpiryTime = _this$state.showExpiryTime;
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.SafeAreaView, {
          style: style.container,
          testID: "custom_status_clear_after.screen",
          children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[16]).KeyboardAwareScrollView, {
            bounces: false,
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: style.scrollView,
              children: this.renderClearAfterMenu()
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: style.block,
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_clear_after_menu_item.default, {
                currentUser: currentUser,
                duration: 'date_and_time',
                expiryTime: expiresAt,
                handleItemClick: this.handleItemClick,
                isSelected: duration === 'date_and_time' && expiresAt === '',
                separator: false,
                showDateTimePicker: duration === 'date_and_time',
                showExpiryTime: showExpiryTime
              })
            })]
          })
        });
      }
    }]);
  }(_$$_REQUIRE(_dependencyMap[15]).NavigationComponent);
  var enhanced = (0, _$$_REQUIRE(_dependencyMap[17]).withObservables)([], function (_ref2) {
    var database = _ref2.database;
    return {
      currentUser: (0, _$$_REQUIRE(_dependencyMap[18]).observeCurrentUser)(database)
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[17]).withDatabase)(enhanced((0, _$$_REQUIRE(_dependencyMap[19]).injectIntl)(ClearAfterModal)));
