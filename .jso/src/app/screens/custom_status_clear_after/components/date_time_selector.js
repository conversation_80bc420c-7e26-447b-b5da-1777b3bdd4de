  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _datetimepicker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react2 = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1,
        paddingTop: 10,
        backgroundColor: theme.centerChannelBg
      },
      buttonContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        marginBottom: 10
      }
    };
  });
  var DateTimeSelector = function DateTimeSelector(_ref) {
    var timezone = _ref.timezone,
      handleChange = _ref.handleChange,
      isMilitaryTime = _ref.isMilitaryTime,
      theme = _ref.theme;
    var styles = getStyleSheet(theme);
    var currentTime = (0, _$$_REQUIRE(_dependencyMap[8]).getCurrentMomentForTimezone)(timezone);
    var timezoneOffSetInMinutes = timezone ? (0, _$$_REQUIRE(_dependencyMap[8]).getUtcOffsetForTimeZone)(timezone) : undefined;
    var minimumDate = (0, _$$_REQUIRE(_dependencyMap[8]).getRoundedTime)(currentTime);
    var _useState = (0, _react2.useState)(minimumDate),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      date = _useState2[0],
      setDate = _useState2[1];
    var _useState3 = (0, _react2.useState)('date'),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      mode = _useState4[0],
      setMode = _useState4[1];
    var _useState5 = (0, _react2.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      show = _useState6[0],
      setShow = _useState6[1];
    var onChange = function onChange(_, selectedDate) {
      var currentDate = selectedDate || date;
      setShow(_reactNative.Platform.OS === 'ios');
      if ((0, _momentTimezone.default)(currentDate).isAfter(minimumDate)) {
        setDate((0, _momentTimezone.default)(currentDate));
        handleChange((0, _momentTimezone.default)(currentDate));
      }
    };
    var showMode = function showMode(currentMode) {
      setShow(true);
      setMode(currentMode);
    };
    var showDatepicker = function showDatepicker() {
      showMode('date');
      handleChange((0, _momentTimezone.default)(date));
    };
    var showTimepicker = function showTimepicker() {
      showMode('time');
      handleChange((0, _momentTimezone.default)(date));
    };
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.buttonContainer,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          testID: 'custom_status_clear_after.menu_item.date_and_time.button.date',
          onPress: showDatepicker,
          style: {
            backgroundColor: theme.buttonBg,
            width: 120,
            alignItems: 'center',
            justifyContent: 'center',
            height: 35,
            borderRadius: 7
          },
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[9]).typography)("Heading", 100, "Light"), {
              color: "white"
            }),
            children: 'اختيار التاريخ'
          })
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          testID: 'custom_status_clear_after.menu_item.date_and_time.button.date',
          onPress: showTimepicker,
          style: {
            backgroundColor: theme.buttonBg,
            width: 120,
            alignItems: 'center',
            justifyContent: 'center',
            height: 35,
            borderRadius: 7
          },
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[9]).typography)("Heading", 100, "Light"), {
              color: "white"
            }),
            children: 'اختيار الوقت'
          })
        })]
      }), show && /*#__PURE__*/(0, _jsxRuntime.jsx)(_datetimepicker.default, {
        testID: "custom_status_clear_after.date_time_picker",
        value: date.toDate(),
        mode: mode,
        is24Hour: isMilitaryTime,
        display: _reactNative.Platform.OS === 'ios' ? 'spinner' : 'default',
        onChange: onChange,
        textColor: theme.centerChannelColor,
        minimumDate: minimumDate.toDate(),
        minuteInterval: _$$_REQUIRE(_dependencyMap[10]).CUSTOM_STATUS_TIME_PICKER_INTERVALS_IN_MINUTES,
        timeZoneOffsetInMinutes: timezoneOffSetInMinutes,
        positiveButton: {
          label: 'تحديد'
        },
        negativeButton: {
          label: 'الغاء'
        }
      })]
    });
  };
  var enhanced = (0, _$$_REQUIRE(_dependencyMap[11]).withObservables)([], function (_ref2) {
    var database = _ref2.database;
    return {
      isMilitaryTime: (0, _$$_REQUIRE(_dependencyMap[12]).queryDisplayNamePreferences)(database).observeWithColumns(['value']).pipe((0, _$$_REQUIRE(_dependencyMap[13]).switchMap)(function (preferences) {
        return (0, _$$_REQUIRE(_dependencyMap[14]).of)((0, _$$_REQUIRE(_dependencyMap[15]).getDisplayNamePreferenceAsBool)(preferences, _$$_REQUIRE(_dependencyMap[16]).Preferences.USE_MILITARY_TIME, false));
      }))
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[11]).withDatabase)(enhanced(DateTimeSelector));
