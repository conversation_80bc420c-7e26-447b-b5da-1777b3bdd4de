  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _custom_status_expiry = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _custom_status_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _date_time_selector = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[10]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        backgroundColor: theme.centerChannelBg,
        display: 'flex',
        flexDirection: 'row',
        padding: 10
      },
      textContainer: {
        marginLeft: 5,
        marginBottom: 2,
        alignItems: 'center',
        width: '70%',
        flex: 1,
        flexDirection: 'row',
        position: 'relative'
      },
      rightPosition: {
        position: 'absolute',
        right: 14
      },
      divider: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[10]).changeOpacity)(theme.centerChannelColor, 0.2),
        height: 1,
        marginHorizontal: 16
      },
      button: {
        borderRadius: 1000,
        color: theme.buttonBg
      },
      customStatusExpiry: {
        color: theme.linkColor
      }
    };
  });
  var ClearAfterMenuItem = function ClearAfterMenuItem(_ref) {
    var currentUser = _ref.currentUser,
      duration = _ref.duration,
      _ref$expiryTime = _ref.expiryTime,
      expiryTime = _ref$expiryTime === undefined ? '' : _ref$expiryTime,
      handleItemClick = _ref.handleItemClick,
      isSelected = _ref.isSelected,
      separator = _ref.separator,
      _ref$showDateTimePick = _ref.showDateTimePicker,
      showDateTimePicker = _ref$showDateTimePick === undefined ? false : _ref$showDateTimePick,
      _ref$showExpiryTime = _ref.showExpiryTime,
      showExpiryTime = _ref$showExpiryTime === undefined ? false : _ref$showExpiryTime;
    var theme = (0, _$$_REQUIRE(_dependencyMap[11]).useTheme)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[12]).useIntl)();
    var style = getStyleSheet(theme);
    var expiryMenuItems = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.DONT_CLEAR, intl.formatMessage(_$$_REQUIRE(_dependencyMap[13]).CST[_$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.DONT_CLEAR])), _$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.THIRTY_MINUTES, intl.formatMessage(_$$_REQUIRE(_dependencyMap[13]).CST[_$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.THIRTY_MINUTES])), _$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.ONE_HOUR, intl.formatMessage(_$$_REQUIRE(_dependencyMap[13]).CST[_$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.ONE_HOUR])), _$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.FOUR_HOURS, intl.formatMessage(_$$_REQUIRE(_dependencyMap[13]).CST[_$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.FOUR_HOURS])), _$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.TODAY, intl.formatMessage(_$$_REQUIRE(_dependencyMap[13]).CST[_$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.TODAY])), _$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.THIS_WEEK, intl.formatMessage(_$$_REQUIRE(_dependencyMap[13]).CST[_$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.THIS_WEEK])), _$$_REQUIRE(_dependencyMap[13]).CustomStatusDurationEnum.DATE_AND_TIME, intl.formatMessage({
      id: 'custom_status.expiry_dropdown.custom',
      defaultMessage: 'Custom'
    }));
    var handleClick = (0, _$$_REQUIRE(_dependencyMap[14]).preventDoubleTap)(function () {
      handleItemClick(duration, expiryTime);
    });
    var handleCustomExpiresAtChange = (0, _react.useCallback)(function (expiresAt) {
      handleItemClick(duration, expiresAt.toISOString());
    }, [handleItemClick, duration]);
    var clearAfterMenuItemTestId = `custom_status_clear_after.menu_item.${duration}`;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        onPress: handleClick,
        testID: clearAfterMenuItemTestId,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.container,
          children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: style.textContainer,
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_text.default, {
              text: expiryMenuItems[duration],
              theme: theme,
              textStyle: {
                color: theme.centerChannelColor,
                fontFamily: 'IBMPlexSansArabic-Regular'
              },
              testID: `${clearAfterMenuItemTestId}.custom_status_text`
            }), isSelected && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: style.rightPosition,
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
                name: 'check',
                size: 24,
                style: style.button
              })
            }), showExpiryTime && expiryTime !== '' && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: style.rightPosition,
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_custom_status_expiry.default, {
                theme: theme,
                time: (0, _moment.default)(expiryTime).toDate(),
                textStyles: style.customStatusExpiry,
                showTimeCompulsory: true,
                showToday: true,
                testID: `${clearAfterMenuItemTestId}.custom_status_expiry`
              })
            })]
          })
        }), separator && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.divider
        })]
      }), showDateTimePicker && /*#__PURE__*/(0, _jsxRuntime.jsx)(_date_time_selector.default, {
        handleChange: handleCustomExpiresAtChange,
        theme: theme,
        timezone: (0, _$$_REQUIRE(_dependencyMap[15]).getTimezone)(currentUser == null ? undefined : currentUser.timezone)
      })]
    });
  };
  var _default = exports.default = ClearAfterMenuItem;
