  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _edit_post = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhance = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)([], function (_ref) {
    var database = _ref.database,
      post = _ref.post;
    var maxPostSize = (0, _$$_REQUIRE(_dependencyMap[3]).observeConfigIntValue)(database, 'MaxPostSize', _$$_REQUIRE(_dependencyMap[4]).MAX_MESSAGE_LENGTH_FALLBACK);
    var hasFilesAttached = (0, _$$_REQUIRE(_dependencyMap[5]).observeFilesForPost)(database, post.id).pipe((0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (files) {
      return (0, _$$_REQUIRE(_dependencyMap[7]).of)((files == null ? undefined : files.length) > 0);
    }));
    return {
      maxPostSize: maxPostSize,
      hasFilesAttached: hasFilesAttached
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhance(_edit_post.default));
