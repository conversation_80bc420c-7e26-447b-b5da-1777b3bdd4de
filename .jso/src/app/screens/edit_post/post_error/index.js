  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _error_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    errorContainerSplit: {
      paddingHorizontal: 15,
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%'
    },
    errorContainer: {
      paddingHorizontal: 10,
      width: '100%'
    },
    errorWrap: {
      flexShrink: 1,
      paddingRight: 20
    },
    errorWrapper: {
      alignItems: 'center'
    }
  });
  var PostError = function PostError(_ref) {
    var errorLine = _ref.errorLine,
      errorExtra = _ref.errorExtra;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: errorExtra ? styles.errorContainerSplit : styles.errorContainer,
      children: [Boolean(errorLine) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_error_text.default, {
        testID: "edit_post.message.input.error",
        error: errorLine,
        textStyle: styles.errorWrap
      }), Boolean(errorExtra) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_error_text.default, {
        testID: "edit_post.message.input.error.extra",
        error: errorExtra,
        textStyle: !errorLine && styles.errorWrapper
      })]
    });
  };
  var _default = exports.default = PostError;
