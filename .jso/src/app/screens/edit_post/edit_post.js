  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _autocomplete = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _did_update = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _post_error = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _edit_post_input = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var AUTOCOMPLETE_SEPARATION = 8;
  var styles = _reactNative.StyleSheet.create({
    body: {
      flex: 1
    },
    container: {
      flex: 1
    },
    loader: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center'
    }
  });
  var RIGHT_BUTTON = (0, _$$_REQUIRE(_dependencyMap[13]).buildNavigationButton)('edit-post', 'edit_post.save.button');
  var EditPost = function EditPost(_ref) {
    var componentId = _ref.componentId,
      maxPostSize = _ref.maxPostSize,
      post = _ref.post,
      closeButtonId = _ref.closeButtonId,
      hasFilesAttached = _ref.hasFilesAttached,
      canDelete = _ref.canDelete;
    var editingMessage = post.messageSource || post.message;
    var _useState = (0, _react.useState)(editingMessage),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      postMessage = _useState2[0],
      setPostMessage = _useState2[1];
    var _useState3 = (0, _react.useState)(editingMessage.length),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      cursorPosition = _useState4[0],
      setCursorPosition = _useState4[1];
    var _useState5 = (0, _react.useState)(),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      errorLine = _useState6[0],
      setErrorLine = _useState6[1];
    var _useState7 = (0, _react.useState)(),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      errorExtra = _useState8[0],
      setErrorExtra = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
      isUpdating = _useState10[0],
      setIsUpdating = _useState10[1];
    var _useState11 = (0, _react.useState)(0),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      containerHeight = _useState12[0],
      setContainerHeight = _useState12[1];
    var _useInputPropagation = (0, _$$_REQUIRE(_dependencyMap[14]).useInputPropagation)(),
      _useInputPropagation2 = (0, _slicedToArray2.default)(_useInputPropagation, 2),
      propagateValue = _useInputPropagation2[0],
      shouldProcessEvent = _useInputPropagation2[1];
    var _useAlert = (0, _$$_REQUIRE(_dependencyMap[15]).useAlert)(),
      showAlert = _useAlert.showAlert;
    var mainView = (0, _react.useRef)(null);
    var postInputRef = (0, _react.useRef)(null);
    var theme = (0, _$$_REQUIRE(_dependencyMap[16]).useTheme)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[17]).useIntl)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[18]).useServerUrl)();
    (0, _react.useEffect)(function () {
      toggleSaveButton(false);
    }, []);
    (0, _react.useEffect)(function () {
      var t = setTimeout(function () {
        var _postInputRef$current;
        (_postInputRef$current = postInputRef.current) == null ? undefined : _postInputRef$current.focus();
      }, 320);
      return function () {
        clearTimeout(t);
      };
    }, []);
    (0, _did_update.default)(function () {
      // Workaround to avoid iOS emdash autocorrect in Code Blocks
      if (_reactNative.Platform.OS === 'ios') {
        onTextSelectionChange();
      }
    }, [postMessage]);
    var onClose = (0, _react.useCallback)(function () {
      _reactNative.Keyboard.dismiss();
      (0, _$$_REQUIRE(_dependencyMap[13]).dismissModal)({
        componentId: componentId
      });
    }, []);
    var onTextSelectionChange = (0, _react.useCallback)(function () {
      var curPos = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : cursorPosition;
      setCursorPosition(curPos);
    }, [cursorPosition, postMessage]);
    var toggleSaveButton = (0, _react.useCallback)(function () {
      var enabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
      (0, _$$_REQUIRE(_dependencyMap[13]).setButtons)(componentId, {
        rightButtons: [Object.assign({}, RIGHT_BUTTON, {
          color: theme.sidebarHeaderTextColor,
          disabledColor: (0, _$$_REQUIRE(_dependencyMap[19]).changeOpacity)(theme.sidebarHeaderTextColor, 0.32),
          text: intl.formatMessage({
            id: 'edit_post.save',
            defaultMessage: 'Save'
          }),
          enabled: enabled
        })]
      });
    }, [componentId, intl, theme]);
    var onChangeTextCommon = (0, _react.useCallback)(function (message) {
      var tooLong = message.trim().length > maxPostSize;
      setErrorLine(undefined);
      setErrorExtra(undefined);
      if (tooLong) {
        var line = intl.formatMessage({
          id: 'mobile.message_length.message_split_left',
          defaultMessage: 'Message exceeds the character limit'
        });
        var extra = `${message.trim().length} / ${maxPostSize}`;
        setErrorLine(line);
        setErrorExtra(extra);
      }
      toggleSaveButton(editingMessage !== message && !tooLong);
    }, [intl, maxPostSize, editingMessage, toggleSaveButton]);
    var onAutocompleteChangeText = (0, _react.useCallback)(function (message) {
      setPostMessage(message);
      propagateValue(message);
      onChangeTextCommon(message);
    }, [onChangeTextCommon]);
    var onInputChangeText = (0, _react.useCallback)(function (message) {
      if (!shouldProcessEvent(message)) {
        return;
      }
      setPostMessage(message);
      onChangeTextCommon(message);
    }, [onChangeTextCommon]);
    var handleUIUpdates = (0, _react.useCallback)(function (res) {
      if (res.error) {
        var _postInputRef$current2;
        setIsUpdating(false);
        var errorMessage = intl.formatMessage({
          id: 'mobile.edit_post.error',
          defaultMessage: 'There was a problem editing this message. Please try again.'
        });
        setErrorLine(errorMessage);
        postInputRef == null ? undefined : (_postInputRef$current2 = postInputRef.current) == null ? undefined : _postInputRef$current2.focus();
      } else {
        setIsUpdating(false);
        onClose();
      }
    }, []);
    var handleDeletePost = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      showAlert(intl.formatMessage({
        id: 'mobile.edit_post.delete_title',
        defaultMessage: 'Confirm Post Delete'
      }), intl.formatMessage({
        id: 'mobile.edit_post.delete_question',
        defaultMessage: 'Are you sure you want to delete this Post?'
      }), [{
        text: intl.formatMessage({
          id: 'mobile.post.cancel',
          defaultMessage: 'Cancel'
        }),
        style: 'cancel',
        onPress: function onPress() {
          setIsUpdating(false);
          toggleSaveButton();
          setPostMessage(editingMessage);
        }
      }, {
        text: intl.formatMessage({
          id: 'post_info.del',
          defaultMessage: 'Delete'
        }),
        style: 'destructive',
        onPress: function () {
          var _onPress = (0, _asyncToGenerator2.default)(function* () {
            var res = yield (0, _$$_REQUIRE(_dependencyMap[20]).deletePost)(serverUrl, post);
            handleUIUpdates(res);
          });
          function onPress() {
            return _onPress.apply(this, arguments);
          }
          return onPress;
        }()
      }]);
    }), [serverUrl, editingMessage]);
    var onSavePostMessage = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      setIsUpdating(true);
      setErrorLine(undefined);
      setErrorExtra(undefined);
      toggleSaveButton(false);
      if (!postMessage && canDelete && !hasFilesAttached) {
        handleDeletePost();
        return;
      }
      var res = yield (0, _$$_REQUIRE(_dependencyMap[20]).editPost)(serverUrl, post.id, postMessage);
      handleUIUpdates(res);
    }), [toggleSaveButton, serverUrl, post.id, postMessage, onClose]);
    var onLayout = (0, _react.useCallback)(function (e) {
      setContainerHeight(e.nativeEvent.layout.height);
    }, []);
    (0, _navigation_button_pressed.default)(RIGHT_BUTTON.id, componentId, onSavePostMessage, [postMessage]);
    (0, _navigation_button_pressed.default)(closeButtonId, componentId, onClose, []);
    (0, _android_back_handler.default)(componentId, onClose);
    var overlap = (0, _$$_REQUIRE(_dependencyMap[21]).useKeyboardOverlap)(mainView, containerHeight);
    var autocompletePosition = overlap + AUTOCOMPLETE_SEPARATION;
    var autocompleteAvailableSpace = containerHeight - autocompletePosition;
    var inputHeight = containerHeight - overlap;
    var _useAutocompleteDefau = (0, _$$_REQUIRE(_dependencyMap[22]).useAutocompleteDefaultAnimatedValues)(autocompletePosition, autocompleteAvailableSpace),
      _useAutocompleteDefau2 = (0, _slicedToArray2.default)(_useAutocompleteDefau, 2),
      animatedAutocompletePosition = _useAutocompleteDefau2[0],
      animatedAutocompleteAvailableSpace = _useAutocompleteDefau2[1];
    if (isUpdating) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.loader,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
          color: theme.buttonBg
        })
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.SafeAreaView, {
        testID: "edit_post.screen",
        style: styles.container,
        onLayout: onLayout,
        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.body,
          ref: mainView,
          children: [Boolean(errorLine || errorExtra) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_post_error.default, {
            errorExtra: errorExtra,
            errorLine: errorLine
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_edit_post_input.default, {
            inputHeight: inputHeight,
            hasError: Boolean(errorLine),
            message: postMessage,
            onChangeText: onInputChangeText,
            onTextSelectionChange: onTextSelectionChange,
            ref: postInputRef
          })]
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_autocomplete.default, {
        channelId: post.channelId,
        hasFilesAttached: hasFilesAttached,
        nestedScrollEnabled: true,
        rootId: post.rootId,
        updateValue: onAutocompleteChangeText,
        value: postMessage,
        cursorPosition: cursorPosition,
        position: animatedAutocompletePosition,
        availableSpace: animatedAutocompleteAvailableSpace,
        inPost: false,
        serverUrl: serverUrl
      })]
    });
  };
  var _default = exports.default = EditPost;
