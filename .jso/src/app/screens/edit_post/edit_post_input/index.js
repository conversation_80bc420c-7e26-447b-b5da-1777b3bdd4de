  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNativePasteInput = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      input: Object.assign({
        color: theme.centerChannelColor,
        padding: 15,
        textAlignVertical: 'top'
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200)),
      inputContainer: {
        backgroundColor: theme.centerChannelBg,
        marginTop: 2
      }
    };
  });
  var EditPostInput = (0, _react.forwardRef)(function (_ref, ref) {
    var inputHeight = _ref.inputHeight,
      message = _ref.message,
      onChangeText = _ref.onChangeText,
      onTextSelectionChange = _ref.onTextSelectionChange,
      hasError = _ref.hasError;
    var intl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[8]).useTheme)();
    var styles = getStyleSheet(theme);
    var managedConfig = (0, _$$_REQUIRE(_dependencyMap[9]).useManagedConfig)();
    var disableCopyAndPaste = managedConfig.copyAndPasteProtection === 'true';
    var inputRef = (0, _react.useRef)();
    var inputStyle = (0, _react.useMemo)(function () {
      return [styles.input, {
        height: inputHeight
      }];
    }, [inputHeight, styles]);
    var onSelectionChange = (0, _react.useCallback)(function (event) {
      var curPos = event.nativeEvent.selection.end;
      onTextSelectionChange(curPos);
    }, [onTextSelectionChange]);
    var containerStyle = (0, _react.useMemo)(function () {
      return [styles.inputContainer, hasError && {
        marginTop: 0
      }, {
        height: inputHeight
      }];
    }, [styles, inputHeight]);
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        focus: function focus() {
          var _inputRef$current;
          return (_inputRef$current = inputRef.current) == null ? undefined : _inputRef$current.focus();
        }
      };
    }, [inputRef.current]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: containerStyle,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativePasteInput.default, {
        allowFontScaling: true,
        disableCopyPaste: disableCopyAndPaste,
        disableFullscreenUI: true,
        keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[5]).getKeyboardAppearanceFromTheme)(theme),
        multiline: true,
        onChangeText: onChangeText,
        onPaste: _$$_REQUIRE(_dependencyMap[10]).emptyFunction,
        onSelectionChange: onSelectionChange,
        placeholder: intl.formatMessage({
          id: 'edit_post.editPost',
          defaultMessage: 'Edit the post...'
        }),
        placeholderTextColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.5),
        ref: inputRef,
        smartPunctuation: "disable",
        submitBehavior: "newline",
        style: inputStyle,
        selectionColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.buttonBg, 0.35),
        selectionHandleColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.buttonBg, 0.6),
        cursorColor: theme.buttonBg,
        testID: "edit_post.message.input",
        underlineColorAndroid: "transparent",
        value: message
      })
    });
  });
  EditPostInput.displayName = 'EditPostInput';
  var _default = exports.default = EditPostInput;
