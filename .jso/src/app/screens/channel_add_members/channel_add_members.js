  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = ChannelAddMembers;
  exports.getHeaderOptions = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _selected_users = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _server_user_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var CLOSE_BUTTON_ID = 'close-add-member';
  var TEST_ID = 'add_members';
  var CLOSE_BUTTON_TEST_ID = 'close.button';
  var getHeaderOptions = exports.getHeaderOptions = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (theme, displayName) {
      var inModal = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      var leftButtons;
      if (!inModal) {
        var closeButton = yield _compass_icon.default.getImageSourceSync('close', 24, theme.sidebarHeaderTextColor);
        leftButtons = [{
          id: CLOSE_BUTTON_ID,
          icon: closeButton,
          testID: `${TEST_ID}.${CLOSE_BUTTON_TEST_ID}`
        }];
      }
      return {
        topBar: {
          // subtitle: {
          // color: changeOpacity(theme.sidebarHeaderTextColor, 0.72),
          // text: displayName,
          // },
          leftButtons: leftButtons,
          backButton: inModal ? {
            color: theme.sidebarHeaderTextColor
          } : undefined
        },
        layout: {
          componentBackgroundColor: theme.centerChannelBg
        },
        statusBar: {
          visible: true,
          backgroundColor: theme.sidebarBg
        }
      };
    });
    return function getHeaderOptions(_x, _x2) {
      return _ref.apply(this, arguments);
    };
  }();
  var close = function close() {
    _reactNative.Keyboard.dismiss();
    (0, _$$_REQUIRE(_dependencyMap[13]).dismissModal)();
  };
  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[14]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1
      },
      searchBar: {
        marginLeft: 12,
        marginRight: _reactNative.Platform.select({
          ios: 4,
          default: 12
        }),
        marginVertical: 12
      },
      loadingContainer: {
        alignItems: 'center',
        backgroundColor: theme.centerChannelBg,
        height: 70,
        justifyContent: 'center'
      },
      loadingText: {
        color: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.centerChannelColor, 0.6)
      },
      noResultContainer: {
        flexGrow: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center'
      },
      noResultText: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.centerChannelColor, 0.5)
      }, (0, _$$_REQUIRE(_dependencyMap[15]).typography)('Heading', 600, 'Light'))
    };
  });
  function removeProfileFromList(list, id) {
    var newSelectedIds = Object.assign({}, list);
    Reflect.deleteProperty(newSelectedIds, id);
    return newSelectedIds;
  }
  function ChannelAddMembers(_ref2) {
    var componentId = _ref2.componentId,
      channel = _ref2.channel,
      currentUserId = _ref2.currentUserId,
      teammateNameDisplay = _ref2.teammateNameDisplay,
      tutorialWatched = _ref2.tutorialWatched,
      inModal = _ref2.inModal;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[16]).useServerUrl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[17]).useTheme)();
    var style = getStyleFromTheme(theme);
    var intl = (0, _$$_REQUIRE(_dependencyMap[18]).useIntl)();
    var formatMessage = intl.formatMessage;
    var mainView = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      containerHeight = _useState2[0],
      setContainerHeight = _useState2[1];
    var keyboardOverlap = (0, _$$_REQUIRE(_dependencyMap[19]).useKeyboardOverlap)(mainView, containerHeight);
    var _useState3 = (0, _react.useState)(''),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      term = _useState4[0],
      setTerm = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      addingMembers = _useState6[0],
      setAddingMembers = _useState6[1];
    var _useState7 = (0, _react.useState)({}),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      selectedIds = _useState8[0],
      setSelectedIds = _useState8[1];
    var clearSearch = (0, _react.useCallback)(function () {
      setTerm('');
    }, []);
    var handleRemoveProfile = (0, _react.useCallback)(function (id) {
      setSelectedIds(function (current) {
        return removeProfileFromList(current, id);
      });
    }, []);
    var addMembers = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!channel) {
        return;
      }
      if (addingMembers) {
        return;
      }
      var idsToUse = Object.keys(selectedIds);
      if (!idsToUse.length) {
        return;
      }
      setAddingMembers(true);
      var result = yield (0, _$$_REQUIRE(_dependencyMap[20]).addMembersToChannel)(serverUrl, channel.id, idsToUse);
      if (result.error) {
        (0, _$$_REQUIRE(_dependencyMap[21]).alertErrorWithFallback)(intl, result.error, {
          id: (0, _$$_REQUIRE(_dependencyMap[22]).t)('mobile.channel_add_members.error'),
          defaultMessage: 'There has been an error and we could not add those users to the channel.'
        });
        setAddingMembers(false);
      } else {
        close();
        (0, _$$_REQUIRE(_dependencyMap[23]).showAddChannelMembersSnackbar)(idsToUse.length);
      }
    }), [channel, addingMembers, selectedIds, serverUrl, intl]);
    var handleSelectProfile = (0, _react.useCallback)(function (user) {
      clearSearch();
      setSelectedIds(function (current) {
        if (current[user.id]) {
          return removeProfileFromList(current, user.id);
        }
        var newSelectedIds = Object.assign({}, current);
        newSelectedIds[user.id] = user;
        return newSelectedIds;
      });
    }, [currentUserId, clearSearch]);
    var onTextChange = (0, _react.useCallback)(function (searchTerm) {
      setTerm(searchTerm);
    }, []);
    var onLayout = (0, _react.useCallback)(function (e) {
      setContainerHeight(e.nativeEvent.layout.height);
    }, []);
    var updateNavigationButtons = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var options = yield getHeaderOptions(theme, (channel == null ? undefined : channel.displayName) || '', inModal);
      (0, _$$_REQUIRE(_dependencyMap[24]).mergeNavigationOptions)(componentId, options);
    }), [theme, channel == null ? undefined : channel.displayName, inModal, componentId]);
    var userFetchFunction = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (page) {
        var _result$users;
        if (!channel) {
          return [];
        }
        var result = yield (0, _$$_REQUIRE(_dependencyMap[25]).fetchProfilesNotInChannel)(serverUrl, channel.teamId, channel.id, channel.isGroupConstrained, page, _$$_REQUIRE(_dependencyMap[26]).General.PROFILE_CHUNK_SIZE);
        if ((_result$users = result.users) != null && _result$users.length) {
          return result.users.filter(function (u) {
            return !u.delete_at;
          });
        }
        return [];
      });
      return function (_x3) {
        return _ref5.apply(this, arguments);
      };
    }(), [serverUrl, channel]);
    var userSearchFunction = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (searchTerm) {
        if (!channel) {
          return [];
        }
        var lowerCasedTerm = searchTerm.toLowerCase();
        var results = yield (0, _$$_REQUIRE(_dependencyMap[25]).searchProfiles)(serverUrl, lowerCasedTerm, {
          team_id: channel.teamId,
          not_in_channel_id: channel.id,
          allow_inactive: false
        });
        if (results.data) {
          return results.data;
        }
        return [];
      });
      return function (_x4) {
        return _ref6.apply(this, arguments);
      };
    }(), [serverUrl, channel]);
    var createUserFilter = (0, _react.useCallback)(function (exactMatches, searchTerm) {
      return function (p) {
        if (p.username === searchTerm || p.username.startsWith(searchTerm)) {
          exactMatches.push(p);
          return false;
        }
        return true;
      };
    }, []);
    (0, _navigation_button_pressed.default)(CLOSE_BUTTON_ID, componentId, close, [close]);
    (0, _android_back_handler.default)(componentId, close);
    (0, _react.useEffect)(function () {
      updateNavigationButtons();
    }, [updateNavigationButtons]);
    if (addingMembers) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.container,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
          color: theme.centerChannelColor
        })
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[27]).SafeAreaView, {
      style: style.container,
      testID: `${TEST_ID}.screen`,
      onLayout: onLayout,
      ref: mainView,
      edges: ['top', 'left', 'right'],
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: style.searchBar,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_search.default, {
          testID: `${TEST_ID}.search_bar`,
          placeholder: formatMessage({
            id: 'search_bar.search',
            defaultMessage: 'Search'
          }),
          cancelButtonTitle: formatMessage({
            id: 'mobile.post.cancel',
            defaultMessage: 'Cancel'
          }),
          placeholderTextColor: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.centerChannelColor, 0.5),
          onChangeText: onTextChange,
          onCancel: clearSearch,
          autoCapitalize: "none",
          keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[14]).getKeyboardAppearanceFromTheme)(theme),
          value: term
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_server_user_list.default, {
        currentUserId: currentUserId,
        handleSelectProfile: handleSelectProfile,
        selectedIds: selectedIds,
        term: term,
        testID: `${TEST_ID}.user_list`,
        tutorialWatched: tutorialWatched,
        fetchFunction: userFetchFunction,
        searchFunction: userSearchFunction,
        createFilter: createUserFilter
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_selected_users.default, {
        keyboardOverlap: keyboardOverlap,
        selectedIds: selectedIds,
        onRemove: handleRemoveProfile,
        teammateNameDisplay: teammateNameDisplay,
        onPress: addMembers,
        buttonIcon: 'account-plus-outline',
        buttonText: formatMessage({
          id: 'channel_add_members.add_members.button',
          defaultMessage: 'Add Members'
        }),
        testID: `${TEST_ID}.selected`
      })]
    });
  }
