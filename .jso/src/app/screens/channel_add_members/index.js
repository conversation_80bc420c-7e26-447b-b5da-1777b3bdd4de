  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _channel_add_members = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['channelId'], function (_ref) {
    var database = _ref.database,
      channelId = _ref.channelId;
    var channel = (0, _$$_REQUIRE(_dependencyMap[3]).observeChannel)(database, channelId);
    return {
      channel: channel,
      teammateNameDisplay: (0, _$$_REQUIRE(_dependencyMap[4]).observeTeammateNameDisplay)(database),
      tutorialWatched: (0, _$$_REQUIRE(_dependencyMap[5]).observeTutorialWatched)(_$$_REQUIRE(_dependencyMap[6]).Tutorial.PROFILE_LONG_PRESS)
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_channel_add_members.default));
