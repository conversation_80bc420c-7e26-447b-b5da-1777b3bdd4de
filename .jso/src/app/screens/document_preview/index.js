  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[1]);
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNativeWebview = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNativeFs = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var Document_Preview = function Document_Preview(_ref) {
    var itemHolder = _ref.itemHolder;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[6]).useServerUrl)();
    var onBackPress = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[7]).popTopScreen)("DocumentPreview");
    }, ["DocumentPreview"]);
    (0, _android_back_handler.default)("DocumentPreview", onBackPress);
    var fileName = _reactNativeFs.default.DownloadDirectoryPath + '/' + itemHolder.name;
    var isFileExist = _reactNativeFs.default.exists(fileName);
    isFileExist.then(function (data) {
      console.log(`\n\nthis the file exist ${data}\n\n`);
    });
    var ref = (0, _react.useRef)();
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeWebview.default, {
      style: {
        height: 500,
        width: 400
      },
      originWhitelist: ['*'],
      source: {
        uri: `file://${fileName}`
      },
      javaScriptEnabled: true,
      domStorageEnabled: true,
      allowFileAccess: true,
      onError: function onError(error) {
        console.log(`\n\nthis the errror from loading file ${error}\n\n`);
      }
    });
  };
  {/* <WebView style={{ height: 500, width: 400 }}
     originWhitelist={['*']}
     source={{ uri: `file://${fileName}` }}
     javaScriptEnabled={true}
     domStorageEnabled={true}
   allowFileAccess={true}
   onError={(error)=>{
     console.log(`\n\nthis the errror from loading file ${error}\n\n`)
   }}
   />*/}
  var _default = exports.default = Document_Preview;
