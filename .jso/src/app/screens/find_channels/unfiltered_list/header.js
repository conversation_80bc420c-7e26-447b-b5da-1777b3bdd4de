  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _redirectController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        paddingVertical: 8,
        paddingTop: 12,
        paddingLeft: 2,
        flexDirection: 'row',
        alignItems: 'flex-start',
        backgroundColor: theme.centerChannelBg
      },
      heading: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.64),
        textTransform: 'uppercase'
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 75, 'SemiBold'))
    };
  });
  var FindChannelsHeader = function FindChannelsHeader(_ref) {
    var sectionName = _ref.sectionName;
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var styles = getStyleSheet(theme);
    var _useRedirectMessageCo = (0, _redirectController.default)(),
      isRedirecMessage = _useRedirectMessageCo.isRedirecMessage;
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.heading,
        testID: `find_channels.header.${sectionName}`,
        children: !isRedirecMessage && sectionName.toUpperCase()
      })
    });
  };
  var _default = exports.default = FindChannelsHeader;
