  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _unfiltered_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var MAX_CHANNELS = 100;
  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var database = _ref.database;
    var teamsCount = (0, _$$_REQUIRE(_dependencyMap[4]).queryJoinedTeams)(database).observeCount();
    var teamIds = (0, _$$_REQUIRE(_dependencyMap[4]).queryJoinedTeams)(database).observe().pipe(
    // eslint-disable-next-line max-nested-callbacks
    (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (teams) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(new Set(teams.map(function (t) {
        return t.id;
      })));
    }));
    var recentChannels = (0, _$$_REQUIRE(_dependencyMap[7]).queryMyRecentChannels)(database, MAX_CHANNELS).observeWithColumns(['last_viewed_at']).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (myChannels) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).retrieveChannels)(database, myChannels, true);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).combineLatestWith)(teamIds), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        myChannels = _ref3[0],
        tmIds = _ref3[1];
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)((0, _$$_REQUIRE(_dependencyMap[8]).removeChannelsFromArchivedTeams)(myChannels, tmIds));
    }));
    return {
      recentChannels: recentChannels,
      showTeamName: teamsCount.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (count) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(count > 1);
      }))
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_unfiltered_list.default));
