  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _channel_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _header = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _redirectController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var sectionNames = {
    recent: {
      id: (0, _$$_REQUIRE(_dependencyMap[10]).t)('mobile.channel_list.recent'),
      defaultMessage: 'Recent'
    }
  };
  var style = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    }
  });
  var buildSections = function buildSections(recentChannels) {
    var sections = [];
    if (recentChannels.length) {
      sections.push(Object.assign({}, sectionNames.recent, {
        data: recentChannels
      }));
    }
    return sections;
  };
  var UnfilteredList = function UnfilteredList(_ref) {
    var close = _ref.close,
      keyboardOverlap = _ref.keyboardOverlap,
      recentChannels = _ref.recentChannels,
      showTeamName = _ref.showTeamName,
      testID = _ref.testID;
    var intl = (0, _$$_REQUIRE(_dependencyMap[11]).useIntl)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[12]).useServerUrl)();
    var _useState = (0, _react.useState)(buildSections(recentChannels)),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      sections = _useState2[0],
      setSections = _useState2[1];
    var sectionListStyle = (0, _react.useMemo)(function () {
      return {
        paddingBottom: keyboardOverlap
      };
    }, [keyboardOverlap]);
    var _useRedirectMessageCo = (0, _redirectController.default)(),
      isRedirecMessage = _useRedirectMessageCo.isRedirecMessage,
      addChannaleToList = _useRedirectMessageCo.addChannaleToList,
      enRequestState = _useRedirectMessageCo.enRequestState,
      sendNewDirecMessage = _useRedirectMessageCo.sendNewDirecMessage;
    var onPress = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (c) {
        if (isRedirecMessage) {
          addChannaleToList(c);
        } else {
          yield close();
          (0, _$$_REQUIRE(_dependencyMap[13]).switchToChannelById)(serverUrl, c.id);
        }
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }(), [serverUrl, close]);
    var renderSectionHeader = (0, _react.useCallback)(function (_ref3) {
      var section = _ref3.section;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_header.default, {
        sectionName: intl.formatMessage({
          id: section.id,
          defaultMessage: section.defaultMessage
        })
      });
    }, [intl.locale]);
    var renderSectionItem = (0, _react.useCallback)(function (_ref4) {
      var item = _ref4.item;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_item.default, {
        channel: item,
        onPress: onPress,
        isOnCenterBg: true,
        showTeamName: showTeamName,
        shouldHighlightState: true,
        testID: `${testID}.channel_item`
      });
    }, [onPress, showTeamName]);
    (0, _react.useEffect)(function () {
      setSections(buildSections(recentChannels));
    }, [recentChannels]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
      entering: _reactNativeReanimated.FadeInDown.duration(200),
      exiting: _reactNativeReanimated.FadeOutUp.duration(100)
      //style={style.flex}
      ,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.SectionList, {
        contentContainerStyle: sectionListStyle,
        keyboardDismissMode: "interactive",
        keyboardShouldPersistTaps: "handled",
        renderItem: renderSectionItem,
        renderSectionHeader: renderSectionHeader,
        sections: sections,
        showsVerticalScrollIndicator: false,
        stickySectionHeadersEnabled: true,
        testID: `${testID}.section_list`
      }), isRedirecMessage && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: {
          position: 'static',
          bottom: 0,
          width: '100%',
          height: 50,
          zIndex: 20,
          backgroundColor: '#00987E',
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: 8
        },
        onPress: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
          sendNewDirecMessage(serverUrl, intl);
          yield (0, _$$_REQUIRE(_dependencyMap[14]).dismissBottomSheet)();
        }),
        children: enRequestState === 'loading' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[15]).ActivityIndicator, {}) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: {
            fontFamily: "IBMPlexSansArabic-SemiBold",
            color: "#fff"
          },
          children: "تحويل الرسائل"
        })
      })]
    });
  };
  var _default = exports.default = UnfilteredList;
