  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _quick_options = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)([], function (_ref) {
    var database = _ref.database;
    var team = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentTeam)(database);
    var currentUser = (0, _$$_REQUIRE(_dependencyMap[5]).observeCurrentUser)(database);
    var canJoinChannels = (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([currentUser, team]).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        u = _ref3[0],
        t = _ref3[1];
      return (0, _$$_REQUIRE(_dependencyMap[8]).observePermissionForTeam)(database, t, u, _$$_REQUIRE(_dependencyMap[9]).Permissions.JOIN_PUBLIC_CHANNELS, true);
    }));
    var canCreatePublicChannels = (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([currentUser, team]).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref4) {
      var _ref5 = (0, _slicedToArray2.default)(_ref4, 2),
        u = _ref5[0],
        t = _ref5[1];
      return (0, _$$_REQUIRE(_dependencyMap[8]).observePermissionForTeam)(database, t, u, _$$_REQUIRE(_dependencyMap[9]).Permissions.CREATE_PUBLIC_CHANNEL, true);
    }));
    var canCreatePrivateChannels = (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([currentUser, team]).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref6) {
      var _ref7 = (0, _slicedToArray2.default)(_ref6, 2),
        u = _ref7[0],
        t = _ref7[1];
      return (0, _$$_REQUIRE(_dependencyMap[8]).observePermissionForTeam)(database, t, u, _$$_REQUIRE(_dependencyMap[9]).Permissions.CREATE_PRIVATE_CHANNEL, false);
    }));
    var canCreateChannels = (0, _$$_REQUIRE(_dependencyMap[6]).combineLatest)([canCreatePublicChannels, canCreatePrivateChannels]).pipe((0, _$$_REQUIRE(_dependencyMap[7]).switchMap)(function (_ref8) {
      var _ref9 = (0, _slicedToArray2.default)(_ref8, 2),
        open = _ref9[0],
        priv = _ref9[1];
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(open || priv);
    }));
    return {
      canCreateChannels: canCreateChannels,
      canJoinChannels: canJoinChannels
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_quick_options.default));
