  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: 20,
      alignItems: 'center'
    },
    wrapper: {
      flexDirection: 'row',
      height: _$$_REQUIRE(_dependencyMap[8]).OPTIONS_HEIGHT
    },
    separator: {
      width: 8
    }
  });
  var QuickOptions = function QuickOptions(_ref) {
    var canCreateChannels = _ref.canCreateChannels,
      canJoinChannels = _ref.canJoinChannels,
      close = _ref.close;
    var theme = (0, _$$_REQUIRE(_dependencyMap[9]).useTheme)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[10]).useIntl)();
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      currentIndex = _useState2[0],
      changeCurrentIndex = _useState2[1];
    var browseChannels = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      changeCurrentIndex(0);
      var title = intl.formatMessage({
        id: 'browse_channels.title',
        defaultMessage: 'Browse channels'
      });
      var closeButton = yield _compass_icon.default.getImageSource('close', 24, theme.sidebarHeaderTextColor);
      yield close();
      (0, _$$_REQUIRE(_dependencyMap[11]).showModal)(_$$_REQUIRE(_dependencyMap[12]).Screens.BROWSE_CHANNELS, title, {
        closeButton: closeButton
      });
    }), [intl, theme]);
    var createNewChannel = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var title = intl.formatMessage({
        id: 'mobile.create_channel.title',
        defaultMessage: 'New channel'
      });
      yield close();
      (0, _$$_REQUIRE(_dependencyMap[11]).showModal)(_$$_REQUIRE(_dependencyMap[12]).Screens.CREATE_OR_EDIT_CHANNEL, title);
    }), [intl]);
    var openDirectMessage = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      changeCurrentIndex(0);
      var title = intl.formatMessage({
        id: 'create_direct_message.title',
        defaultMessage: 'Create Direct Message'
      });
      var closeButton = yield _compass_icon.default.getImageSource('close', 24, theme.sidebarHeaderTextColor);
      yield close();
      (0, _$$_REQUIRE(_dependencyMap[11]).showModal)(_$$_REQUIRE(_dependencyMap[12]).Screens.CREATE_DIRECT_MESSAGE, title, {
        closeButton: closeButton
      });
    }), [intl, theme]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      entering: _reactNativeReanimated.FadeInDown.duration(200),
      exiting: _reactNativeReanimated.FadeOutUp.duration(100),
      style: styles.container,
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: styles.wrapper,
        children: [canJoinChannels && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[13]).Pressable, {
            onPress: browseChannels,
            style: {
              alignItems: 'center',
              // backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
              // borderRadius: 4,
              flex: 1,
              maxHeight: 40,
              justifyContent: 'center',
              borderRadius: 8,
              minWidth: 30,
              borderWidth: 1,
              borderColor: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.sidebarText, 0.15),
              flexDirection: 'row'
            },
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[15]).ChatBubbleLeftRightIcon, {
              size: 24,
              color: theme.sidebarText,
              style: {
                height: 20,
                width: 20,
                marginHorizontal: 5
              }
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[16]).typography)('Heading', 75, 'SemiBold'), {
                color: theme.sidebarText
              }),
              children: intl.formatMessage({
                id: 'find_channels.directory',
                defaultMessage: 'Directory'
              })
            })]
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.separator
          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[13]).Pressable, {
            onPress: openDirectMessage,
            style: {
              alignItems: 'center',
              // backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
              // borderRadius: 4,
              flex: 1,
              maxHeight: 40,
              justifyContent: 'center',
              borderRadius: 8,
              minWidth: 60,
              borderWidth: 1,
              borderColor: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.sidebarText, 0.15),
              flexDirection: 'row'
            },
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Image, {
              tintColor: theme.sidebarText,
              style: {
                height: 20,
                width: 20,
                marginHorizontal: 5
              },
              source: _$$_REQUIRE(_dependencyMap[17])
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[16]).typography)('Heading', 50, 'SemiBold'), {
                color: theme.sidebarText
              }),
              children: intl.formatMessage({
                id: 'find_channels.open_dm',
                defaultMessage: 'Open a DM'
              })
            })]
          })]
        }), canCreateChannels && /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[13]).Pressable, {
            onPress: createNewChannel,
            style: {
              alignItems: 'center',
              // backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
              // borderRadius: 4,
              flex: 1,
              maxHeight: 40,
              justifyContent: 'center',
              borderRadius: 8,
              minWidth: 30,
              marginStart: 7,
              borderWidth: 1,
              borderColor: (0, _$$_REQUIRE(_dependencyMap[14]).changeOpacity)(theme.sidebarText, 0.15),
              flexDirection: 'row'
            },
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
              name: "plus",
              size: 20,
              color: theme.sidebarText,
              style: {
                marginEnd: 2
              }
            }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[16]).typography)('Heading', 50, 'SemiBold'), {
                color: theme.sidebarText
              }),
              children: intl.formatMessage({
                id: 'find_channels.new_channel',
                defaultMessage: 'New Channel'
              })
            })]
          })
        })]
      })
    });
  };
  var _default = exports.default = QuickOptions;
