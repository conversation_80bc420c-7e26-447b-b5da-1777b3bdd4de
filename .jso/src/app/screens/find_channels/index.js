  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _filtered_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _quick_options = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _unfiltered_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[11]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1,
        marginHorizontal: 20,
        marginTop: 20
      },
      inputContainerStyle: {
        //  backgroundColor: changeOpacity(theme.centerChannelColor, 0.12),
      },
      inputStyle: {
        color: theme.centerChannelColor
      },
      listContainer: {
        flex: 1,
        marginTop: 8
      }
    };
  });
  var FindChannels = function FindChannels(_ref) {
    var closeButtonId = _ref.closeButtonId,
      componentId = _ref.componentId;
    var theme = (0, _$$_REQUIRE(_dependencyMap[12]).useTheme)();
    var _useState = (0, _react.useState)(''),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      term = _useState2[0],
      setTerm = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loading = _useState4[0],
      setLoading = _useState4[1];
    var styles = getStyleSheet(theme);
    var color = (0, _react.useMemo)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[11]).changeOpacity)(theme.centerChannelColor, 0.72);
    }, [theme]);
    var listView = (0, _react.useRef)(null);
    var _useState5 = (0, _react.useState)(0),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      containerHeight = _useState6[0],
      setContainerHeight = _useState6[1];
    var overlap = (0, _$$_REQUIRE(_dependencyMap[13]).useKeyboardOverlap)(listView, containerHeight);
    var cancelButtonProps = (0, _react.useMemo)(function () {
      return {
        color: color,
        buttonTextStyle: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[14]).typography)('Heading', 100))
      };
    }, [color]);
    var onLayout = (0, _react.useCallback)(function (e) {
      setContainerHeight(e.nativeEvent.layout.height);
    }, []);
    var close = (0, _react.useCallback)(function () {
      _reactNative.Keyboard.dismiss();
      return (0, _$$_REQUIRE(_dependencyMap[15]).dismissModal)({
        componentId: componentId
      });
    }, []);
    var onCancel = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[15]).dismissModal)({
        componentId: componentId
      });
    }, []);
    var onChangeText = (0, _react.useCallback)(function (text) {
      setTerm(text);
      if (!text) {
        setLoading(false);
      }
    }, []);
    (0, _navigation_button_pressed.default)(closeButtonId, componentId, close, []);
    (0, _android_back_handler.default)(componentId, close);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      testID: "find_channels.screen",
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_search.default, {
        autoCapitalize: "none",
        autoFocus: true,
        cancelButtonProps: cancelButtonProps,
        clearIconColor: color,
        inputContainerStyle: styles.inputContainerStyle,
        inputStyle: styles.inputStyle,
        keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[11]).getKeyboardAppearanceFromTheme)(theme),
        onCancel: onCancel,
        onChangeText: onChangeText,
        placeholderTextColor: color,
        searchIconColor: color,
        selectionColor: color,
        showLoading: loading,
        value: term,
        testID: "find_channels.search_bar"
      }), term === '' && /*#__PURE__*/(0, _jsxRuntime.jsx)(_quick_options.default, {
        close: close
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.listContainer,
        onLayout: onLayout,
        ref: listView,
        children: [term === '' && /*#__PURE__*/(0, _jsxRuntime.jsx)(_unfiltered_list.default, {
          close: close,
          keyboardOverlap: overlap,
          testID: "find_channels.unfiltered_list"
        }), Boolean(term) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_filtered_list.default, {
          close: close,
          keyboardOverlap: overlap,
          loading: loading,
          onLoading: setLoading,
          term: term,
          testID: "find_channels.filtered_list"
        })]
      })]
    });
  };
  var _default = exports.default = FindChannels;
