  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.retrieveChannels = exports.removeChannelsFromArchivedTeams = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var _MM_TABLES$SERVER = _$$_REQUIRE(_dependencyMap[0]).MM_TABLES.SERVER,
    CHANNEL = _MM_TABLES$SERVER.CHANNEL,
    MY_CHANNEL = _MM_TABLES$SERVER.MY_CHANNEL;
  var retrieveChannels = exports.retrieveChannels = function retrieveChannels(database, myChannels) {
    var orderedByLastViewedAt = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    var ids = myChannels.map(function (m) {
      return m.id;
    });
    if (ids.length) {
      var idsStr = `'${ids.join("','")}'`;
      var order = orderedByLastViewedAt ? 'order by my.last_viewed_at desc' : '';
      return database.get(CHANNEL).query(_$$_REQUIRE(_dependencyMap[1]).Q.unsafeSqlQuery(`select distinct c.* from ${MY_CHANNEL} my
            inner join ${CHANNEL} c on c.id=my.id and c.id in (${idsStr})
            ${order}`)).observe();
    }
    return (0, _$$_REQUIRE(_dependencyMap[2]).of)([]);
  };
  var removeChannelsFromArchivedTeams = exports.removeChannelsFromArchivedTeams = function removeChannelsFromArchivedTeams(recentChannels, teamIds) {
    return recentChannels.filter(function (channel) {
      if (!channel.teamId) {
        return true;
      }
      return teamIds.has(channel.teamId);
    });
  };
