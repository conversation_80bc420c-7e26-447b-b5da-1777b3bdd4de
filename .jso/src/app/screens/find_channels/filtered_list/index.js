  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _filtered_list = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)(['term'], function (_ref) {
    var database = _ref.database,
      term = _ref.term;
    var teamIds = (0, _$$_REQUIRE(_dependencyMap[4]).queryJoinedTeams)(database).observe().pipe(
    // eslint-disable-next-line max-nested-callbacks
    (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (teams) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(new Set(teams.map(function (t) {
        return t.id;
      })));
    }));
    var joinedChannelsMatchStart = (0, _$$_REQUIRE(_dependencyMap[7]).observeJoinedChannelsByTerm)(database, term, _filtered_list.MAX_RESULTS, true);
    var joinedChannelsMatch = (0, _$$_REQUIRE(_dependencyMap[7]).observeJoinedChannelsByTerm)(database, term, _filtered_list.MAX_RESULTS);
    var directChannelsMatchStart = (0, _$$_REQUIRE(_dependencyMap[7]).observeDirectChannelsByTerm)(database, term, _filtered_list.MAX_RESULTS, true);
    var directChannelsMatch = (0, _$$_REQUIRE(_dependencyMap[7]).observeDirectChannelsByTerm)(database, term, _filtered_list.MAX_RESULTS);
    var channelsMatchStart = joinedChannelsMatchStart.pipe((0, _$$_REQUIRE(_dependencyMap[5]).combineLatestWith)(directChannelsMatchStart), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (matchStart) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).retrieveChannels)(database, matchStart.flat(), true);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).combineLatestWith)(teamIds), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
        myChannels = _ref3[0],
        tmIds = _ref3[1];
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)((0, _$$_REQUIRE(_dependencyMap[8]).removeChannelsFromArchivedTeams)(myChannels, tmIds));
    }));
    var channelsMatch = joinedChannelsMatch.pipe((0, _$$_REQUIRE(_dependencyMap[5]).combineLatestWith)(directChannelsMatch), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (matched) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).retrieveChannels)(database, matched.flat(), true);
    }), (0, _$$_REQUIRE(_dependencyMap[5]).combineLatestWith)(teamIds), (0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (_ref4) {
      var _ref5 = (0, _slicedToArray2.default)(_ref4, 2),
        myChannels = _ref5[0],
        tmIds = _ref5[1];
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)((0, _$$_REQUIRE(_dependencyMap[8]).removeChannelsFromArchivedTeams)(myChannels, tmIds));
    }));
    var archivedChannels = (0, _$$_REQUIRE(_dependencyMap[7]).observeArchiveChannelsByTerm)(database, term, _filtered_list.MAX_RESULTS).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (archived) {
      return (0, _$$_REQUIRE(_dependencyMap[8]).retrieveChannels)(database, archived);
    }));
    var usersMatchStart = (0, _$$_REQUIRE(_dependencyMap[7]).observeNotDirectChannelsByTerm)(database, term, _filtered_list.MAX_RESULTS, true);
    var usersMatch = (0, _$$_REQUIRE(_dependencyMap[7]).observeNotDirectChannelsByTerm)(database, term, _filtered_list.MAX_RESULTS);
    var restrictDirectMessage = (0, _$$_REQUIRE(_dependencyMap[9]).observeConfigValue)(database, 'RestrictDirectMessage').pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (v) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(v !== _$$_REQUIRE(_dependencyMap[10]).General.RESTRICT_DIRECT_MESSAGE_ANY);
    }));
    var teammateDisplayNameSetting = (0, _$$_REQUIRE(_dependencyMap[11]).observeTeammateNameDisplay)(database);
    return {
      archivedChannels: archivedChannels,
      channelsMatch: channelsMatch,
      channelsMatchStart: channelsMatchStart,
      currentTeamId: (0, _$$_REQUIRE(_dependencyMap[9]).observeCurrentTeamId)(database),
      isCRTEnabled: (0, _$$_REQUIRE(_dependencyMap[12]).observeIsCRTEnabled)(database),
      restrictDirectMessage: restrictDirectMessage,
      showTeamName: teamIds.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (ids) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(ids.size > 1);
      })),
      teamIds: teamIds,
      teammateDisplayNameSetting: teammateDisplayNameSetting,
      usersMatchStart: usersMatchStart,
      usersMatch: usersMatch
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[3]).withDatabase)(enhanced(_filtered_list.default));
