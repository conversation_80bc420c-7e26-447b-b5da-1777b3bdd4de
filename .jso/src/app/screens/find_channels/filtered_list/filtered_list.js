  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.MAX_RESULTS = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _channel_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _no_results_with_term = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _threads_button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _user_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var style = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    },
    noResultContainer: {
      flexGrow: 1,
      alignItems: 'center',
      justifyContent: 'center'
    }
  });
  var MAX_RESULTS = exports.MAX_RESULTS = 20;
  var sortByLastPostAt = function sortByLastPostAt(a, b) {
    return a.last_post_at > b.last_post_at ? 1 : -1;
  };
  var sortByUserOrChannel = function sortByUserOrChannel(locale, teammateDisplayNameSetting, a, b) {
    var aDisplayName = 'display_name' in a ? a.display_name : (0, _$$_REQUIRE(_dependencyMap[13]).displayUsername)(a, locale, teammateDisplayNameSetting);
    var bDisplayName = 'display_name' in b ? b.display_name : (0, _$$_REQUIRE(_dependencyMap[13]).displayUsername)(b, locale, teammateDisplayNameSetting);
    return aDisplayName.toLowerCase().localeCompare(bDisplayName.toLowerCase(), locale, {
      numeric: true
    });
  };
  var FilteredList = function FilteredList(_ref) {
    var archivedChannels = _ref.archivedChannels,
      close = _ref.close,
      channelsMatch = _ref.channelsMatch,
      channelsMatchStart = _ref.channelsMatchStart,
      currentTeamId = _ref.currentTeamId,
      isCRTEnabled = _ref.isCRTEnabled,
      keyboardOverlap = _ref.keyboardOverlap,
      loading = _ref.loading,
      onLoading = _ref.onLoading,
      restrictDirectMessage = _ref.restrictDirectMessage,
      showTeamName = _ref.showTeamName,
      teamIds = _ref.teamIds,
      teammateDisplayNameSetting = _ref.teammateDisplayNameSetting,
      term = _ref.term,
      usersMatch = _ref.usersMatch,
      usersMatchStart = _ref.usersMatchStart,
      testID = _ref.testID;
    var bounce = (0, _react.useRef)();
    var mounted = (0, _react.useRef)(false);
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[14]).useServerUrl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[15]).useTheme)();
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[16]).useIntl)(),
      locale = _useIntl.locale,
      formatMessage = _useIntl.formatMessage;
    var flatListStyle = (0, _react.useMemo)(function () {
      return {
        flexGrow: 1,
        paddingBottom: keyboardOverlap
      };
    }, [keyboardOverlap]);
    var _useState = (0, _react.useState)({
        archived: [],
        startWith: [],
        matches: []
      }),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      remoteChannels = _useState2[0],
      setRemoteChannels = _useState2[1];
    var _useAlert = (0, _$$_REQUIRE(_dependencyMap[17]).useAlert)(),
      showAlert = _useAlert.showAlert;
    var totalLocalResults = channelsMatchStart.length + channelsMatch.length + usersMatchStart.length;
    var search = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        onLoading(true);
        if (mounted.current) {
          setRemoteChannels({
            archived: [],
            startWith: [],
            matches: []
          });
        }
        var lowerCasedTerm = (term.startsWith('@') ? term.substring(1) : term).toLowerCase();
        if (channelsMatchStart.length + channelsMatch.length < MAX_RESULTS) {
          if (restrictDirectMessage) {
            (0, _$$_REQUIRE(_dependencyMap[18]).searchProfiles)(serverUrl, lowerCasedTerm, {
              team_id: currentTeamId,
              allow_inactive: true
            });
          } else {
            (0, _$$_REQUIRE(_dependencyMap[18]).searchProfiles)(serverUrl, lowerCasedTerm, {
              allow_inactive: true
            });
          }
        }
        if (!term.startsWith('@')) {
          if (totalLocalResults < MAX_RESULTS) {
            var _yield$searchAllChann = yield (0, _$$_REQUIRE(_dependencyMap[19]).searchAllChannels)(serverUrl, lowerCasedTerm, true),
              channels = _yield$searchAllChann.channels;
            if (channels) {
              var existingChannelIds = new Set(channelsMatchStart.concat(channelsMatch).concat(archivedChannels).map(function (c) {
                return c.id;
              }));
              var _channels$reduce = channels.reduce(function (_ref3, c) {
                  var _ref4 = (0, _slicedToArray2.default)(_ref3, 3),
                    s = _ref4[0],
                    m = _ref4[1],
                    a = _ref4[2];
                  if (existingChannelIds.has(c.id) || !teamIds.has(c.team_id)) {
                    return [s, m, a];
                  }
                  if (!c.delete_at) {
                    if (c.display_name.toLowerCase().startsWith(lowerCasedTerm)) {
                      return [[].concat((0, _toConsumableArray2.default)(s), [c]), m, a];
                    }
                    if (c.display_name.toLowerCase().includes(lowerCasedTerm)) {
                      return [s, [].concat((0, _toConsumableArray2.default)(m), [c]), a];
                    }
                    return [s, m, a];
                  }
                  if (c.display_name.toLowerCase().includes(lowerCasedTerm)) {
                    return [s, m, [].concat((0, _toConsumableArray2.default)(a), [c])];
                  }
                  return [s, m, a];
                }, [[], [], []]),
                _channels$reduce2 = (0, _slicedToArray2.default)(_channels$reduce, 3),
                startWith = _channels$reduce2[0],
                matches = _channels$reduce2[1],
                archived = _channels$reduce2[2];
              if (mounted.current) {
                setRemoteChannels({
                  archived: archived.sort(_$$_REQUIRE(_dependencyMap[20]).sortChannelsByDisplayName.bind(null, locale)).slice(0, MAX_RESULTS + 1),
                  startWith: startWith.sort(sortByLastPostAt).slice(0, MAX_RESULTS + 1),
                  matches: matches.sort(_$$_REQUIRE(_dependencyMap[20]).sortChannelsByDisplayName.bind(null, locale)).slice(0, MAX_RESULTS + 1)
                });
              }
            }
          }
        }
        onLoading(false);
      });
      return function search() {
        return _ref2.apply(this, arguments);
      };
    }();
    var onJoinChannel = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (c) {
        var res = yield (0, _$$_REQUIRE(_dependencyMap[19]).joinChannelIfNeeded)(serverUrl, c.id);
        var displayName = 'display_name' in c ? c.display_name : c.displayName;
        if ('error' in res) {
          showAlert('', formatMessage({
            id: 'mobile.join_channel.error',
            defaultMessage: "We couldn't join the channel {displayName}."
          }, {
            displayName: displayName
          }));
          return;
        }
        yield close();
        (0, _$$_REQUIRE(_dependencyMap[19]).switchToChannelById)(serverUrl, c.id, undefined, true);
      });
      return function (_x) {
        return _ref5.apply(this, arguments);
      };
    }(), [serverUrl, close, locale]);
    var onOpenDirectMessage = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (u) {
        var displayName = (0, _$$_REQUIRE(_dependencyMap[13]).displayUsername)(u, locale, teammateDisplayNameSetting);
        var _yield$makeDirectChan = yield (0, _$$_REQUIRE(_dependencyMap[19]).makeDirectChannel)(serverUrl, u.id, displayName, false),
          data = _yield$makeDirectChan.data,
          error = _yield$makeDirectChan.error;
        if (error || !data) {
          showAlert('', formatMessage({
            id: 'mobile.direct_message.error',
            defaultMessage: "We couldn't open a DM with {displayName}."
          }, {
            displayName: displayName
          }));
          return;
        }
        yield close();
        (0, _$$_REQUIRE(_dependencyMap[19]).switchToChannelById)(serverUrl, data.id);
      });
      return function (_x2) {
        return _ref6.apply(this, arguments);
      };
    }(), [serverUrl, close, locale, teammateDisplayNameSetting]);
    var onSwitchToChannel = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* (c) {
        yield close();
        (0, _$$_REQUIRE(_dependencyMap[19]).switchToChannelById)(serverUrl, c.id);
      });
      return function (_x3) {
        return _ref7.apply(this, arguments);
      };
    }(), [serverUrl, close]);
    var onSwitchToThreads = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      yield close();
      (0, _$$_REQUIRE(_dependencyMap[21]).switchToGlobalThreads)(serverUrl);
    }), [serverUrl, close]);
    var renderEmpty = (0, _react.useCallback)(function () {
      if (loading) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
          containerStyle: style.noResultContainer,
          size: "large",
          color: theme.buttonBg
        });
      }
      if (term) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.noResultContainer,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_no_results_with_term.default, {
            term: term
          })
        });
      }
      return null;
    }, [term, loading, theme]);
    var renderItem = (0, _react.useCallback)(function (_ref9) {
      var item = _ref9.item;
      if (item === 'thread') {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_threads_button.default, {
          onCenterBg: true,
          onPress: onSwitchToThreads
        });
      }
      if ('teamId' in item) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_item.default, {
          channel: item,
          isOnCenterBg: true,
          onPress: onSwitchToChannel,
          showTeamName: showTeamName,
          shouldHighlightState: true,
          testID: "find_channels.filtered_list.channel_item"
        });
      }
      if ('username' in item) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_user_item.default, {
          onUserPress: onOpenDirectMessage,
          user: item,
          testID: "find_channels.filtered_list.user_item",
          showBadges: true
        });
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_item.default, {
        channel: item,
        isOnCenterBg: true,
        onPress: onJoinChannel,
        showTeamName: showTeamName,
        shouldHighlightState: true,
        testID: "find_channels.filtered_list.remote_channel_item"
      });
    }, [onJoinChannel, onOpenDirectMessage, onSwitchToChannel, showTeamName, teammateDisplayNameSetting]);
    var threadLabel = (0, _react.useMemo)(function () {
      return formatMessage({
        id: 'threads',
        defaultMessage: 'Threads'
      }).toLowerCase();
    }, [locale]);
    var data = (0, _react.useMemo)(function () {
      var items = [];

      // Add threads item to show it on the top of the list
      if (isCRTEnabled) {
        var isThreadTerm = threadLabel.indexOf(term.toLowerCase()) === 0;
        if (isThreadTerm) {
          items.push('thread');
        }
      }
      items.push.apply(items, (0, _toConsumableArray2.default)(channelsMatchStart));

      // Channels that matches
      if (items.length < MAX_RESULTS) {
        items.push.apply(items, (0, _toConsumableArray2.default)(channelsMatch));
      }

      // Users that start with
      if (items.length < MAX_RESULTS) {
        items.push.apply(items, (0, _toConsumableArray2.default)(usersMatchStart));
      }

      // Archived channels local
      if (items.length < MAX_RESULTS) {
        var archivedAlpha = archivedChannels.sort(_$$_REQUIRE(_dependencyMap[20]).sortChannelsByDisplayName.bind(null, locale));
        items.push.apply(items, (0, _toConsumableArray2.default)(archivedAlpha.slice(0, MAX_RESULTS + 1)));
      }

      // Remote Channels that start with
      if (items.length < MAX_RESULTS) {
        items.push.apply(items, (0, _toConsumableArray2.default)(remoteChannels.startWith));
      }

      // Users & Channels that matches
      if (items.length < MAX_RESULTS) {
        var sortedByAlpha = [].concat((0, _toConsumableArray2.default)(usersMatch), (0, _toConsumableArray2.default)(remoteChannels.matches)).sort(sortByUserOrChannel.bind(null, locale, teammateDisplayNameSetting));
        items.push.apply(items, (0, _toConsumableArray2.default)(sortedByAlpha.slice(0, MAX_RESULTS + 1)));
      }

      // Archived channels
      if (items.length < MAX_RESULTS) {
        var _archivedAlpha = remoteChannels.archived.sort(_$$_REQUIRE(_dependencyMap[20]).sortChannelsByDisplayName.bind(null, locale));
        items.push.apply(items, (0, _toConsumableArray2.default)(_archivedAlpha.slice(0, MAX_RESULTS + 1)));
      }
      return (0, _toConsumableArray2.default)(new Set(items)).slice(0, MAX_RESULTS + 1);
    }, [archivedChannels, channelsMatchStart, channelsMatch, isCRTEnabled, remoteChannels, usersMatch, usersMatchStart, locale, teammateDisplayNameSetting, term, threadLabel]);
    (0, _react.useEffect)(function () {
      mounted.current = true;
      return function () {
        mounted.current = false;
      };
    }, []);
    (0, _react.useEffect)(function () {
      bounce.current = (0, _$$_REQUIRE(_dependencyMap[22]).debounce)(search, 500);
      bounce.current();
      return function () {
        if (bounce.current) {
          bounce.current.cancel();
        }
      };
    }, [term]);
    if ((data == null ? undefined : data.length) <= 0) return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {});
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      entering: _reactNativeReanimated.FadeInDown.duration(100),
      exiting: _reactNativeReanimated.FadeOutUp.duration(100),
      style: style.flex,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        contentContainerStyle: flatListStyle,
        keyboardDismissMode: "interactive",
        keyboardShouldPersistTaps: "handled",
        ListEmptyComponent: renderEmpty,
        renderItem: renderItem,
        data: data,
        showsVerticalScrollIndicator: false,
        testID: `${testID}.flat_list`
      })
    });
  };
  var _default = exports.default = FilteredList;
