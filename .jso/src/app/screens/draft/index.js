  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _draft = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['rootId'], function (_ref) {
    var database = _ref.database,
      serverUrl = _ref.serverUrl,
      rootId = _ref.rootId;
    var currentTeamId = (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentTeamId)(database);
    var categories = currentTeamId.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (ctid) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).queryCategoriesByTeamIds)(database, [ctid]).observeWithColumns(['sort_order']);
    }));
    var draftCounter = (0, _$$_REQUIRE(_dependencyMap[6]).queryDrafts)(database, '').observeWithColumns(['message', 'files']).pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (drafts) {
      if (drafts.length < 1) {
        return (0, _$$_REQUIRE(_dependencyMap[4]).of)(0);
      }
      var draftNumber = 0;
      for (var i = 0; i < drafts.length; i++) {
        if (drafts[i].files.length > 0 || drafts[i].message.length > 0) {
          draftNumber++;
        }
      }
      return (0, _$$_REQUIRE(_dependencyMap[4]).of)(draftNumber);
    }));
    return {
      categories: categories,
      draftCounter: draftCounter
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)((0, _$$_REQUIRE(_dependencyMap[7]).withServerUrl)(enhanced(_draft.default)));
