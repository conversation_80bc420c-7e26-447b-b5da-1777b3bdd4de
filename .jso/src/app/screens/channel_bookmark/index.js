  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _navigation_button_pressed = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _bookmark_detail = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _bookmark_file = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _bookmark_link = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[12]).makeStyleSheetFromTheme)(function (theme) {
    return {
      content: {
        flex: 1,
        paddingHorizontal: 20,
        paddingBottom: 16
      },
      progress: {
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
        borderRadius: 4,
        paddingLeft: 3,
        marginTop: 12
      },
      deleteBg: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[12]).changeOpacity)(theme.errorTextColor, 0.16)
      },
      deleteContainer: {
        paddingTop: 32
      },
      deleteText: {
        color: theme.errorTextColor
      }
    };
  });
  var RIGHT_BUTTON = (0, _$$_REQUIRE(_dependencyMap[13]).buildNavigationButton)('edit-bookmark', 'channel_bookmark.edit.save_button');
  var edges = ['bottom', 'left', 'right'];
  var emptyBookmark = {
    id: '',
    create_at: 0,
    update_at: 0,
    delete_at: 0,
    channel_id: '',
    owner_id: '',
    display_name: '',
    sort_order: 0,
    type: 'link'
  };
  var ChannelBookmarkAddOrEdit = function ChannelBookmarkAddOrEdit(_ref) {
    var original = _ref.bookmark,
      _ref$canDeleteBookmar = _ref.canDeleteBookmarks,
      canDeleteBookmarks = _ref$canDeleteBookmar === undefined ? false : _ref$canDeleteBookmar,
      channelId = _ref.channelId,
      closeButtonId = _ref.closeButtonId,
      componentId = _ref.componentId,
      originalFile = _ref.file,
      ownerId = _ref.ownerId,
      type = _ref.type;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[14]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var theme = (0, _$$_REQUIRE(_dependencyMap[15]).useTheme)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[16]).useServerUrl)();
    var styles = getStyleSheet(theme);
    var _useState = (0, _react.useState)(original),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      bookmark = _useState2[0],
      setBookmark = _useState2[1];
    var _useState3 = (0, _react.useState)(originalFile),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      file = _useState4[0],
      setFile = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isSaving = _useState6[0],
      setIsSaving = _useState6[1];
    var _useAlert = (0, _$$_REQUIRE(_dependencyMap[17]).useAlert)(),
      showAlert = _useAlert.showAlert;
    var enableSaveButton = (0, _react.useCallback)(function (enabled) {
      (0, _$$_REQUIRE(_dependencyMap[13]).setButtons)(componentId, {
        rightButtons: [Object.assign({}, RIGHT_BUTTON, {
          color: theme.sidebarHeaderTextColor,
          text: formatMessage({
            id: 'channel_bookmark.edit.save_button',
            defaultMessage: 'Save'
          }),
          enabled: enabled
        })]
      });
    }, [formatMessage, theme]);
    var setBookmarkToSave = (0, _react.useCallback)(function (b) {
      enableSaveButton((b == null ? undefined : b.type) === 'link' && Boolean(b == null ? undefined : b.link_url) || (b == null ? undefined : b.type) === 'file' && Boolean(b.file_id));
      setBookmark(b);
    }, []);
    var handleError = (0, _react.useCallback)(function (error, buttons) {
      var title = original ? formatMessage({
        id: 'channel_bookmark.edit.failed_title',
        defaultMessage: 'Error editing bookmark'
      }) : formatMessage({
        id: 'channel_bookmark.add.failed_title',
        defaultMessage: 'Error adding bookmark'
      });
      showAlert(title, formatMessage({
        id: 'channel_bookmark.add_edit.failed_desc',
        defaultMessage: 'Details: {error}'
      }, {
        error: error
      }), buttons);
      setIsSaving(false);
      var enabled = Boolean((bookmark == null ? undefined : bookmark.display_name) && ((bookmark == null ? undefined : bookmark.type) === 'link' && Boolean(bookmark == null ? undefined : bookmark.link_url) || (bookmark == null ? undefined : bookmark.type) === 'file' && Boolean(bookmark.file_id)));
      enableSaveButton(enabled);
    }, [bookmark, enableSaveButton, formatMessage]);
    var close = (0, _react.useCallback)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[13]).dismissModal)({
        componentId: componentId
      });
    }, [componentId]);
    var createBookmark = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (b) {
        var res = yield (0, _$$_REQUIRE(_dependencyMap[18]).createChannelBookmark)(serverUrl, channelId, b);
        if (res.bookmark) {
          close();
          return;
        }
        handleError(res.error.message);
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }(), [channelId, handleError, serverUrl]);
    var updateBookmark = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (b) {
        var res = yield (0, _$$_REQUIRE(_dependencyMap[18]).editChannelBookmark)(serverUrl, b);
        if (res.bookmarks) {
          close();
          return;
        }
        handleError(res.error.message);
      });
      return function (_x2) {
        return _ref3.apply(this, arguments);
      };
    }(), [handleError, serverUrl]);
    var setLinkBookmark = (0, _react.useCallback)(function (url, title, imageUrl) {
      var b = Object.assign({}, bookmark || emptyBookmark, {
        owner_id: ownerId,
        channel_id: channelId,
        link_url: url,
        image_url: imageUrl,
        display_name: title,
        type: 'link'
      });
      setBookmarkToSave(b);
    }, [bookmark, channelId, setBookmarkToSave, ownerId]);
    var setFileBookmark = (0, _react.useCallback)(function (f) {
      var b = Object.assign({}, bookmark || emptyBookmark, {
        owner_id: ownerId,
        channel_id: channelId,
        display_name: decodeURIComponent(f.name),
        type: 'file',
        file_id: f.id
      });
      setBookmarkToSave(b);
      setFile(f);
    }, [bookmark, channelId, ownerId]);
    var setBookmarkDisplayName = (0, _react.useCallback)(function (displayName) {
      if (bookmark) {
        setBookmark(function (prev) {
          return Object.assign({}, prev, {
            display_name: displayName
          });
        });
      }
      enableSaveButton(Boolean(displayName));
    }, [bookmark, enableSaveButton]);
    var setBookmarkEmoji = (0, _react.useCallback)(function (emoji) {
      if (bookmark) {
        setBookmark(function (prev) {
          return Object.assign({}, prev, {
            emoji: emoji
          });
        });
        var prevEmoji = original ? original.emoji : '';
        if (prevEmoji !== emoji) {
          enableSaveButton(true);
        }
      }
      if (emoji) {
        (0, _$$_REQUIRE(_dependencyMap[19]).addRecentReaction)(serverUrl, [emoji]);
      }
    }, [bookmark, enableSaveButton, serverUrl]);
    var resetBookmark = (0, _react.useCallback)(function () {
      setBookmarkToSave(original);
      setFile(originalFile);
    }, [setBookmarkToSave]);
    var onSaveBookmark = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (bookmark) {
        enableSaveButton(false);
        setIsSaving(true);
        if (original) {
          updateBookmark(bookmark);
          return;
        }
        createBookmark(bookmark);
      }
    }), [bookmark, createBookmark, updateBookmark]);
    var handleDelete = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (bookmark) {
        setIsSaving(true);
        enableSaveButton(false);
        var _yield$deleteChannelB = yield (0, _$$_REQUIRE(_dependencyMap[18]).deleteChannelBookmark)(serverUrl, bookmark.channel_id, bookmark.id),
          error = _yield$deleteChannelB.error;
        if (error) {
          showAlert(formatMessage({
            id: 'channel_bookmark.delete.failed_title',
            defaultMessage: 'Error deleting bookmark'
          }), formatMessage({
            id: 'channel_bookmark.add_edit.failed_desc',
            defaultMessage: 'Details: {error}'
          }, {
            error: (0, _$$_REQUIRE(_dependencyMap[20]).getFullErrorMessage)(error)
          }));
          setIsSaving(false);
          enableSaveButton(true);
          return;
        }
        close();
      }
    }), [bookmark, serverUrl, close]);
    var onDelete = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (bookmark) {
        showAlert(formatMessage({
          id: 'channel_bookmark.delete.confirm_title',
          defaultMessage: 'Delete bookmark'
        }), formatMessage({
          id: 'channel_bookmark.delete.confirm',
          defaultMessage: 'You sure want to delete the bookmark {displayName}?'
        }, {
          displayName: bookmark.display_name
        }), [{
          text: formatMessage({
            id: 'channel_bookmark.delete.yes',
            defaultMessage: 'Yes'
          }),
          style: 'destructive',
          isPreferred: true,
          onPress: handleDelete
        }, {
          text: formatMessage({
            id: 'channel_bookmark.add.file_cancel',
            defaultMessage: 'Cancel'
          }),
          style: 'cancel'
        }]);
      }
    }), [bookmark, handleDelete]);
    (0, _react.useEffect)(function () {
      enableSaveButton(false);
    }, []);
    (0, _navigation_button_pressed.default)(RIGHT_BUTTON.id, componentId, onSaveBookmark, [bookmark]);
    (0, _navigation_button_pressed.default)(closeButtonId, componentId, close, [close]);
    (0, _android_back_handler.default)(componentId, close);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[21]).SafeAreaView, {
      edges: edges,
      style: styles.content,
      testID: "channel_bookmark.screen",
      children: [type === 'link' && /*#__PURE__*/(0, _jsxRuntime.jsx)(_bookmark_link.default, {
        disabled: isSaving,
        initialUrl: original == null ? undefined : original.link_url,
        setBookmark: setLinkBookmark,
        resetBookmark: resetBookmark
      }), type === 'file' && /*#__PURE__*/(0, _jsxRuntime.jsx)(_bookmark_file.default, {
        channelId: channelId,
        close: close,
        disabled: isSaving,
        initialFile: originalFile,
        setBookmark: setFileBookmark
      }), Boolean(bookmark) && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_bookmark_detail.default, {
          disabled: isSaving,
          emoji: bookmark == null ? undefined : bookmark.emoji,
          imageUrl: bookmark == null ? undefined : bookmark.image_url,
          title: (bookmark == null ? undefined : bookmark.display_name) || '',
          file: file,
          setBookmarkDisplayName: setBookmarkDisplayName,
          setBookmarkEmoji: setBookmarkEmoji
        }), canDeleteBookmarks && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.deleteContainer,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_button.default, {
            buttonType: "destructive",
            size: "m",
            text: "Delete bookmark",
            iconName: "trash-can-outline",
            textStyle: styles.deleteText,
            backgroundStyle: styles.deleteBg,
            iconSize: 18,
            onPress: onDelete,
            theme: theme,
            disabled: isSaving
          })
        })]
      })]
    });
  };
  var _default = exports.default = ChannelBookmarkAddOrEdit;
