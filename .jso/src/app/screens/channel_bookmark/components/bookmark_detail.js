  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _bookmark_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      title: Object.assign({
        color: theme.centerChannelColor,
        marginBottom: 8
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 100, 'SemiBold')),
      container: {
        flexDirection: 'row'
      },
      disabled: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.16)
      },
      iconContainer: {
        borderWidth: 1,
        paddingLeft: 16,
        paddingRight: 8,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.16),
        borderRightWidth: 0,
        borderTopLeftRadius: 4,
        borderBottomLeftRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row'
      },
      iconButton: {
        backgroundColor: 'transparent',
        alignItems: 'center',
        justifyContent: 'center'
      },
      imageContainer: {
        width: 28,
        height: 28,
        marginRight: 2
      },
      image: {
        width: 24,
        height: 24
      },
      input: Object.assign({
        borderBottomRightRadius: 4,
        borderTopRightRadius: 4,
        borderWidth: 1,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.16),
        paddingVertical: 12,
        paddingHorizontal: 16,
        flex: 1,
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 200), {
        lineHeight: undefined
      }),
      genericBookmark: {
        alignSelf: 'center',
        top: 2
      }
    };
  });
  var BookmarkDetail = function BookmarkDetail(_ref) {
    var disabled = _ref.disabled,
      emoji = _ref.emoji,
      file = _ref.file,
      imageUrl = _ref.imageUrl,
      setBookmarkDisplayName = _ref.setBookmarkDisplayName,
      setBookmarkEmoji = _ref.setBookmarkEmoji,
      title = _ref.title;
    var intl = (0, _$$_REQUIRE(_dependencyMap[9]).useIntl)();
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[11]).useIsTablet)();
    var paddingStyle = (0, _react.useMemo)(function () {
      return {
        paddingHorizontal: isTablet ? 42 : 0
      };
    }, [isTablet]);
    var styles = getStyleSheet(theme);
    var openEmojiPicker = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[12]).openAsBottomSheet)({
        closeButtonId: 'close-add-emoji',
        screen: _$$_REQUIRE(_dependencyMap[13]).Screens.EMOJI_PICKER,
        theme: theme,
        title: intl.formatMessage({
          id: 'channel_bookmark.add.emoji',
          defaultMessage: 'Add emoji'
        }),
        props: {
          onEmojiPress: setBookmarkEmoji,
          imageUrl: imageUrl,
          file: file
        }
      });
    }, [imageUrl, file, theme, setBookmarkEmoji]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: paddingStyle,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        id: "channel_bookmark.add.detail_title",
        defaultMessage: "Title",
        style: styles.title
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: [styles.container, disabled && styles.disabled],
        children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[14]).Button, {
          buttonStyle: styles.iconButton,
          containerStyle: styles.iconContainer,
          onPress: openEmojiPicker,
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.imageContainer,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_bookmark_icon.default, {
              emoji: emoji,
              emojiSize: 22,
              file: file,
              genericStyle: styles.genericBookmark,
              iconSize: 30,
              imageStyle: styles.image,
              imageUrl: imageUrl
            })
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
            size: 12,
            color: theme.centerChannelColor,
            name: "chevron-down"
          })]
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TextInput, {
          editable: !disabled,
          onChangeText: setBookmarkDisplayName,
          value: title,
          style: styles.input
        })]
      })]
    });
  };
  var _default = exports.default = BookmarkDetail;
