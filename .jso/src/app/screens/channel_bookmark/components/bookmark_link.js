  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _floating_text_input_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _did_update = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[10]).makeStyleSheetFromTheme)(function (theme) {
    return {
      viewContainer: {
        marginVertical: 32,
        width: '100%'
      },
      description: {
        marginTop: 8
      },
      descriptionText: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[11]).typography)('Heading', 75, "Light"), {
        color: (0, _$$_REQUIRE(_dependencyMap[10]).changeOpacity)(theme.centerChannelColor, 0.56)
      }),
      loading: {
        alignItems: 'flex-end',
        justifyContent: 'center'
      }
    };
  });
  var BookmarkLink = function BookmarkLink(_ref) {
    var disabled = _ref.disabled,
      _ref$initialUrl = _ref.initialUrl,
      initialUrl = _ref$initialUrl === undefined ? '' : _ref$initialUrl,
      resetBookmark = _ref.resetBookmark,
      setBookmark = _ref.setBookmark;
    var theme = (0, _$$_REQUIRE(_dependencyMap[12]).useTheme)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[13]).useIntl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[14]).useIsTablet)();
    var _useState = (0, _react.useState)(''),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      error = _useState2[0],
      setError = _useState2[1];
    var _useState3 = (0, _react.useState)(initialUrl),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      url = _useState4[0],
      setUrl = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      loading = _useState6[0],
      setLoading = _useState6[1];
    var styles = getStyleSheet(theme);
    var keyboard = _reactNative.Platform.OS === 'android' ? 'default' : 'url';
    var subContainerStyle = (0, _react.useMemo)(function () {
      return [styles.viewContainer, {
        paddingHorizontal: isTablet ? 42 : 0
      }];
    }, [isTablet, styles]);
    var descContainer = (0, _react.useMemo)(function () {
      return [styles.description, {
        paddingHorizontal: isTablet ? 42 : 0
      }];
    }, [isTablet, styles]);
    var validateAndFetchOG = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[15]).debounce)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (text) {
        setLoading(true);
        var link = yield (0, _$$_REQUIRE(_dependencyMap[16]).getUrlAfterRedirect)(text, false);
        if (link.error) {
          link = yield (0, _$$_REQUIRE(_dependencyMap[16]).getUrlAfterRedirect)(text, true);
        }
        if (link.url) {
          var result = yield (0, _$$_REQUIRE(_dependencyMap[17]).fetchOpenGraph)(link.url, true);
          var title = result.title || text;
          var imageUrl = result.favIcon || result.imageURL || '';
          setLoading(false);
          setBookmark(link.url, title, imageUrl);
          return;
        }
        setError(intl.formatMessage({
          id: 'channel_bookmark_add.link.invalid',
          defaultMessage: 'Please enter a valid link'
        }));
        setLoading(false);
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }(), 500), [intl]);
    var onChangeText = (0, _react.useCallback)(function (text) {
      resetBookmark();
      setUrl(text);
      setError('');
    }, [resetBookmark]);
    var onSubmitEditing = (0, _react.useCallback)(function () {
      if (url) {
        validateAndFetchOG(url);
      }
    }, [url, error]);
    (0, _did_update.default)((0, _$$_REQUIRE(_dependencyMap[15]).debounce)(function () {
      onSubmitEditing();
    }, 300), [onSubmitEditing]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: subContainerStyle,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_text_input_label.default, {
        autoCapitalize: 'none',
        autoCorrect: false,
        disableFullscreenUI: true,
        editable: !disabled,
        keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[10]).getKeyboardAppearanceFromTheme)(theme),
        keyboardType: keyboard,
        returnKeyType: "go",
        label: intl.formatMessage({
          id: 'channel_bookmark_add.link',
          defaultMessage: 'Link'
        }),
        onChangeText: onChangeText,
        theme: theme,
        error: error,
        showErrorIcon: true,
        value: url,
        onSubmitEditing: onSubmitEditing,
        endAdornment: loading && /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
          size: "small",
          color: theme.buttonBg,
          containerStyle: styles.loading
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: descContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "channel_bookmark_add.link.input.description",
          defaultMessage: "Add a link to any post, file, or any external link",
          style: styles.descriptionText,
          testID: "channel_bookmark_add.link.input.description"
        })
      })]
    });
  };
  var _default = exports.default = BookmarkLink;
