  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _bookmark_file = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)([], function (_ref) {
    var database = _ref.database;
    return {
      maxFileSize: (0, _$$_REQUIRE(_dependencyMap[3]).observeConfigIntValue)(database, 'MaxFileSize', _$$_REQUIRE(_dependencyMap[4]).DEFAULT_SERVER_MAX_FILE_SIZE)
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhanced(_bookmark_file.default));
