  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _file_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _progress_bar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _touchable_with_feedback = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _file_picker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[12]).makeStyleSheetFromTheme)(function (theme) {
    return {
      viewContainer: {
        marginTop: 32,
        marginBottom: 24,
        width: '100%',
        flex: 0
      },
      title: Object.assign({
        color: theme.centerChannelColor,
        marginBottom: 8
      }, (0, _$$_REQUIRE(_dependencyMap[13]).typography)('Heading', 100, 'SemiBold')),
      shadowContainer: {
        alignItems: 'center',
        borderColor: (0, _$$_REQUIRE(_dependencyMap[12]).changeOpacity)(theme.centerChannelColor, 0.16),
        borderWidth: 1,
        borderRadius: 4
      },
      fileContainer: {
        height: 64,
        flexDirection: 'row',
        paddingLeft: 12,
        alignItems: 'center'
      },
      fileInfoContainer: {
        paddingHorizontal: 16,
        flex: 1,
        justifyContent: 'center'
      },
      filename: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[13]).typography)('Heading', 200, 'SemiBold')),
      fileInfo: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[12]).changeOpacity)(theme.centerChannelColor, 0.64),
        textTransform: 'uppercase'
      }, (0, _$$_REQUIRE(_dependencyMap[13]).typography)('Heading', 75)),
      uploadError: Object.assign({
        color: theme.errorTextColor
      }, (0, _$$_REQUIRE(_dependencyMap[13]).typography)('Heading', 75)),
      retry: {
        paddingRight: 20,
        height: 40,
        justifyContent: 'center'
      },
      removeContainer: {
        position: 'absolute',
        elevation: 11,
        top: -18,
        right: -12,
        width: 24,
        height: 24
      },
      removeButton: {
        borderRadius: 12,
        alignSelf: 'center',
        marginTop: _reactNative.Platform.select({
          ios: 5.4,
          android: 4.75
        }),
        backgroundColor: theme.centerChannelBg,
        width: 24,
        height: 25
      },
      uploading: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[12]).changeOpacity)(theme.centerChannelColor, 0.64)
      }, (0, _$$_REQUIRE(_dependencyMap[13]).typography)('Heading', 75)),
      progressContainer: {
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
        bottom: 2,
        borderBottomRightRadius: 4,
        borderBottomLeftRadius: 4
      },
      progress: {
        borderRadius: 4,
        borderTopRightRadius: 0,
        borderTopLeftRadius: 0
      }
    };
  });
  var shadowSides = {
    top: false,
    bottom: true,
    end: true,
    start: false
  };
  var hitSlop = {
    top: 10,
    bottom: 10,
    left: 10,
    right: 10
  };
  var BookmarkFile = function BookmarkFile(_ref) {
    var channelId = _ref.channelId,
      close = _ref.close,
      disabled = _ref.disabled,
      initialFile = _ref.initialFile,
      maxFileSize = _ref.maxFileSize,
      setBookmark = _ref.setBookmark;
    var theme = (0, _$$_REQUIRE(_dependencyMap[14]).useTheme)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[15]).useIntl)();
    var isTablet = (0, _$$_REQUIRE(_dependencyMap[16]).useIsTablet)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[17]).useServerUrl)();
    var _useState = (0, _react.useState)(initialFile),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      file = _useState2[0],
      setFile = _useState2[1];
    var _useState3 = (0, _react.useState)(''),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      error = _useState4[0],
      setError = _useState4[1];
    var _useState5 = (0, _react.useState)(0),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      progress = _useState6[0],
      setProgress = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      uploading = _useState8[0],
      setUploading = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
      failed = _useState10[0],
      setFailed = _useState10[1];
    var styles = getStyleSheet(theme);
    var subContainerStyle = (0, _react.useMemo)(function () {
      return [styles.viewContainer, {
        paddingHorizontal: isTablet ? 42 : 0
      }];
    }, [isTablet]);
    var cancelUpload = (0, _react.useRef)();
    var onProgress = (0, _react.useCallback)(function (p, bytes) {
      if (!file) {
        return;
      }
      var f = Object.assign({}, file);
      f.bytesRead = bytes;
      setProgress(p);
      setFile(f);
    }, []);
    var onComplete = (0, _react.useCallback)(function (response) {
      cancelUpload.current = undefined;
      if (response.code !== 201 || !response.data) {
        setUploadError();
        return;
      }
      var data = response.data.file_infos;
      if (!(data != null && data.length)) {
        setUploadError();
        return;
      }
      var fileInfo = data[0];
      setFile(fileInfo);
      setBookmark(fileInfo);
      setUploading(false);
      setProgress(1);
      setFailed(false);
      setError('');
    }, []);
    var onError = (0, _react.useCallback)(function () {
      cancelUpload.current = undefined;
      setUploadError();
    }, []);
    var setUploadError = (0, _react.useCallback)(function () {
      setProgress(0);
      setUploading(false);
      setFailed(true);
      setError(intl.formatMessage({
        id: 'channel_bookmark.add.file_upload_error',
        defaultMessage: 'Error uploading file. Please try again.'
      }));
    }, [file, intl]);
    var startUpload = (0, _react.useCallback)(function (fileInfo) {
      setUploading(true);
      setProgress(0);
      var _uploadFile = (0, _$$_REQUIRE(_dependencyMap[18]).uploadFile)(serverUrl, fileInfo, channelId, onProgress, onComplete, onError, fileInfo.bytesRead, true),
        cancel = _uploadFile.cancel,
        uploadError = _uploadFile.error;
      if (cancel) {
        cancelUpload.current = cancel;
      }
      if (uploadError) {
        setUploadError();
        cancelUpload.current == null ? undefined : cancelUpload.current();
      }
    }, [channelId, onProgress, onComplete, onError, serverUrl]);
    var browseFile = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var picker = new _file_picker.default(intl, function (files) {
        if (files.length) {
          var f = files[0];
          var extension = (0, _$$_REQUIRE(_dependencyMap[19]).getExtensionFromMime)(f.mime_type) || '';
          var fileWithExtension = Object.assign({}, f, {
            extension: extension
          });
          setFile(fileWithExtension);
          startUpload(fileWithExtension);
        }
      });
      var res = yield picker.attachFileFromFiles(undefined, false);
      if (res.error) {
        close();
      }
    }), [close, startUpload]);
    var removeAndUpload = (0, _react.useCallback)(function () {
      cancelUpload.current == null ? undefined : cancelUpload.current();
      browseFile();
    }, [file, browseFile]);
    var retry = (0, _react.useCallback)(function () {
      cancelUpload.current == null ? undefined : cancelUpload.current();
      if (file) {
        startUpload(file);
      }
    }, [file, startUpload]);
    (0, _react.useEffect)(function () {
      if (!initialFile) {
        browseFile();
      }
      return function () {
        cancelUpload.current == null ? undefined : cancelUpload.current();
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (uploading) {
        return;
      }
      if (!(file != null && file.id) && ((file == null ? undefined : file.size) || 0) > maxFileSize) {
        setError((0, _$$_REQUIRE(_dependencyMap[19]).fileSizeWarning)(intl, maxFileSize));
        return;
      }
      if (!(file != null && file.id) && file != null && file.name) {
        setBookmark(file);
      }
    }, [file, intl, maxFileSize, uploading]);
    var info;
    if (error) {
      info = /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.uploadError,
        children: error
      });
    } else if (uploading) {
      info = /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        id: "channel_bookmark.add.file_uploading",
        defaultMessage: "Uploading... ({progress}%)",
        values: {
          progress: Math.ceil(progress * 100)
        },
        style: styles.uploading
      });
    } else if (file) {
      info = /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.fileInfo,
        children: `${file.extension} ${(0, _$$_REQUIRE(_dependencyMap[19]).getFormattedFileSize)(file.size || 0)}`
      });
    }
    if (file) {
      return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: subContainerStyle,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "channel_bookmark.add.file_title",
          defaultMessage: "Attachment",
          style: styles.title
        }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[20]).Shadow, {
          style: styles.shadowContainer,
          startColor: "rgba(61, 60, 64, 0.08)",
          distance: 4,
          sides: shadowSides,
          children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.fileContainer,
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_file_icon.default, {
              file: file
            }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: styles.fileInfoContainer,
              children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                numberOfLines: 1,
                ellipsizeMode: "tail",
                style: styles.filename,
                children: decodeURIComponent(file.name.trim())
              }), info]
            }), failed && /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[21]).Button, {
              onPress: retry,
              containerStyle: styles.retry,
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
                color: (0, _$$_REQUIRE(_dependencyMap[12]).changeOpacity)(theme.centerChannelColor, 0.56),
                name: "refresh",
                size: 20
              })
            })]
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_touchable_with_feedback.default, {
            disabled: disabled,
            hitSlop: hitSlop,
            style: styles.removeContainer,
            onPress: removeAndUpload,
            type: "opacity",
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.removeButton,
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
                name: "close-circle",
                color: (0, _$$_REQUIRE(_dependencyMap[12]).changeOpacity)(theme.centerChannelColor, disabled ? 0.16 : 0.56),
                size: 24
              })
            })
          })]
        }), uploading && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.progressContainer,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_progress_bar.default, {
            progress: progress,
            color: theme.buttonBg,
            style: styles.progress
          })
        })]
      });
    }
    return null;
  };
  var _default = exports.default = BookmarkFile;
