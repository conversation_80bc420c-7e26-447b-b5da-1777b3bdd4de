  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _tinycolor = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  /*
  const WebViewHolder = ()=>{
  return (
        <SafeAreaView style={{ flex: 1 }}>
          <WebView 
            
            source={{ uri: 'https://www.youtube.com/' }} 
          />
        </SafeAreaView>
      );
  }
  
  export default WebViewHolder;
  */

  //import { useLocalSearchParams } from "expo-router";
  //import {} from "react-native-navigation"

  function sendingMeetingUrl(channelId, value, serverUrl, serverUrlHolder) {
    var fullUrl = `${serverUrlHolder}/${value}/role=meeting_link`;
    console.log('this the fullurl jitsi meet', fullUrl);
    var post = {
      user_id: "",
      channel_id: `${channelId}`,
      root_id: "",
      message: fullUrl
      //message: `https://meet.soffa.chat/${value}/role=meeting_link`,
      //message: `${serverUrl}:4443/${value}/role=meeting_link`,
    };

    //  post.metadata = {
    //                   priority:{"priority":"","requested_ack":true,"persistent_notifications":false} ,
    //               };

    (0, _$$_REQUIRE(_dependencyMap[6]).createPost)(`${serverUrl}`, post, []);
  }
  var WebViewHolder = function WebViewHolder(_ref) {
    var roomHolder = _ref.roomHolder,
      _ref$channelId = _ref.channelId,
      channelId = _ref$channelId === undefined ? "" : _ref$channelId,
      _ref$serverUrl = _ref.serverUrl,
      serverUrl = _ref$serverUrl === undefined ? "" : _ref$serverUrl,
      _ref$roomUrl = _ref.roomUrl,
      roomUrl = _ref$roomUrl === undefined ? "" : _ref$roomUrl,
      _ref$isCreation = _ref.isCreation,
      isCreation = _ref$isCreation === undefined ? false : _ref$isCreation,
      currentUser = _ref.currentUser;
    var serverUrlHolder =
    //=== "https://chat.meta-yemen.one" ?
    //  "https://meet.meta-yemen.one"
    "https://meet.soffa.chat";
    //  : serverUrl === "https://chat.creativepoint.io" ?
    // "https://meet.soffa.chat" : serverUrl;

    (0, _react.useEffect)(function () {
      //if (channelId !== null && channelId?.length > 0){
      if (isCreation === true) sendingMeetingUrl(channelId, roomHolder, serverUrl, serverUrlHolder);
      //  console.log(`\n\n\n\n\n\n\nthis mean the meating is creation \n\n\n\n\n\n\n\n`)
      //}
    }, [isCreation === true]);

    // console.log(`\n\nthis the vedio url === ${currentUser!==undefined&&JSON.stringify(currentUser)}\n\n`)

    var onReadyToClose = (0, _react.useCallback)(function () {
      // @ts-ignore
      jitsiMeeting.current.close();
    }, []);
    var onEndpointMessageReceived = (0, _react.useCallback)(function () {
      //console.log('\n\n\nYou got a message! = = == = == == == =  \n\n\n');
    }, []);
    var eventListeners = {
      onReadyToClose: onReadyToClose,
      onEndpointMessageReceived: onEndpointMessageReceived
    };
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var isDark = (0, _tinycolor.default)(theme.sidebarBg).isDark();
    var onBackPress = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[8]).popTopScreen)("WebViewScrean");
    }, ["WebViewScrean"]);
    (0, _android_back_handler.default)("WebViewScrean", onBackPress);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.SafeAreaView, {
      style: {
        height: '100%',
        width: '100%'
      },
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[9]).JitsiMeeting, {
        config: {
          //startScreenSharing: true,
          screenshotCapture: {
            enabled: false
          },
          hideConferenceTimer: true
        },
        eventListeners: eventListeners,
        flags: {
          "invite.enabled": true,
          "ios.screensharing.enabled": false,
          "android.screensharing.enabled": false,
          "video-share.enabled": true
        }
        // ref = { jitsiMeeting }
        ,
        style: {
          flex: 1,
          height: '100%',
          width: '100%'
        }
        //room={`${roomHolder}`}
        ,
        room: `${roomHolder}`,
        userInfo: {
          displayName: (currentUser == null ? undefined : currentUser.user) !== undefined ? currentUser.user.first_name + " " + currentUser.user.last_name : "",
          avatarURL: "",
          email: ""
        }
        //serverURL = { "https://sova" } />
        //serverURL={"https://public.creativepoint.io:4443/"}
        // serverURL={serverUrl + ':4443'}
        //serverURL = { "https://meet.soffa.chat/" } 
        //serverURL={serverUrlHolder ?? "https://meet.soffa.chat"}
        ,
        serverURL: serverUrlHolder
      })
    });
  };
  var _default = exports.default = WebViewHolder;
