  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _team_list = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    container: {
      padding: 12
    },
    listContainer: {
      marginTop: 12
    }
  });
  var TeamSelectorList = function TeamSelectorList(_ref) {
    var teams = _ref.teams,
      selectTeam = _ref.selectTeam;
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var _useState = (0, _react.useState)(teams),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      filteredTeams = _useState2[0],
      setFilteredTeam = _useState2[1];
    var color = (0, _react.useMemo)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.centerChannelColor, 0.72);
    }, [theme]);
    var handleOnChangeSearchText = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[9]).debounce)(function (searchTerm) {
      if (searchTerm === '') {
        setFilteredTeam(teams);
      } else {
        setFilteredTeam(teams.filter(function (team) {
          return team.display_name.includes(searchTerm) || team.name.includes(searchTerm);
        }));
      }
    }, 200), [teams]);
    var handleOnPress = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[10]).preventDoubleTap)(function (teamId) {
      selectTeam(teamId);
      (0, _$$_REQUIRE(_dependencyMap[11]).popTopScreen)();
    }), []);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_search.default, {
        autoCapitalize: "none",
        autoFocus: true,
        keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[8]).getKeyboardAppearanceFromTheme)(theme),
        placeholderTextColor: color,
        searchIconColor: color,
        testID: "convert_gm_to_channel_team_search_bar",
        onChangeText: handleOnChangeSearchText
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.listContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_team_list.default, {
          teams: filteredTeams,
          onPress: handleOnPress
        })
      })]
    });
  };
  var _default = exports.default = TeamSelectorList;
