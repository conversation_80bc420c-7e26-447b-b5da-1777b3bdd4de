  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.NoCommonTeamForm = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _message_box = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var handleOnPress = (0, _$$_REQUIRE(_dependencyMap[6]).preventDoubleTap)(function () {
    (0, _$$_REQUIRE(_dependencyMap[7]).popTopScreen)();
  });
  var NoCommonTeamForm = exports.NoCommonTeamForm = function NoCommonTeamForm(_ref) {
    var containerStyles = _ref.containerStyles;
    var theme = (0, _$$_REQUIRE(_dependencyMap[8]).useTheme)();
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[9]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var header = formatMessage({
      id: 'channel_info.convert_gm_to_channel.warning.no_teams.header',
      defaultMessage: 'Unable to convert to a channel because group members are part of different teams'
    });
    var body = formatMessage({
      id: 'channel_info.convert_gm_to_channel.warning.no_teams.body',
      defaultMessage: 'Group Message cannot be converted to a channel because members are not a part of the same team. Add all members to a single team to convert this group message to a channel.'
    });
    var buttonText = formatMessage({
      id: 'generic.back',
      defaultMessage: 'Back'
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: containerStyles,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_message_box.default, {
        header: header,
        body: body,
        type: "danger"
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_button.default, {
        onPress: handleOnPress,
        text: buttonText,
        theme: theme,
        size: "lg"
      })]
    });
  };
