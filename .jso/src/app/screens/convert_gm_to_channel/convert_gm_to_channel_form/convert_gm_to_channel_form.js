  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ConvertGMToChannelForm = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _message_box = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[9]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        paddingVertical: 24,
        paddingHorizontal: 20,
        display: 'flex',
        flexDirection: 'column',
        gap: 24
      },
      errorMessage: {
        color: theme.dndIndicator
      },
      loadingContainerStyle: {
        marginRight: 10,
        padding: 0,
        top: -2
      }
    };
  });
  var ConvertGMToChannelForm = exports.ConvertGMToChannelForm = function ConvertGMToChannelForm(_ref) {
    var channelId = _ref.channelId,
      commonTeams = _ref.commonTeams,
      profiles = _ref.profiles,
      locale = _ref.locale,
      teammateNameDisplay = _ref.teammateNameDisplay;
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var styles = getStyleFromTheme(theme);
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[11]).useServerUrl)();
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[12]).useIntl)(),
      formatList = _useIntl.formatList,
      formatMessage = _useIntl.formatMessage;
    var _useState = (0, _react.useState)(commonTeams[0]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      selectedTeam = _useState2[0],
      setSelectedTeam = _useState2[1];
    var _useState3 = (0, _react.useState)(''),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      newChannelName = _useState4[0],
      setNewChannelName = _useState4[1];
    var _useState5 = (0, _react.useState)(''),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      errorMessage = _useState6[0],
      setErrorMessage = _useState6[1];
    var _useState7 = (0, _react.useState)(''),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      channelNameErrorMessage = _useState8[0],
      setChannelNameErrorMessage = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
      conversionInProgress = _useState10[0],
      setConversionInProgress = _useState10[1];
    var userDisplayNames = (0, _react.useMemo)(function () {
      return profiles.map(function (profile) {
        return (0, _$$_REQUIRE(_dependencyMap[13]).displayUsername)(profile, locale, teammateNameDisplay);
      });
    }, [profiles, teammateNameDisplay, locale]);
    var submitButtonEnabled = !conversionInProgress && selectedTeam && newChannelName.trim();
    var handleOnPress = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[14]).preventDoubleTap)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      if (!submitButtonEnabled) {
        return;
      }
      setConversionInProgress(true);
      var _yield$convertGroupMe = yield (0, _$$_REQUIRE(_dependencyMap[15]).convertGroupMessageToPrivateChannel)(serverUrl, channelId, selectedTeam.id, newChannelName),
        updatedChannel = _yield$convertGroupMe.updatedChannel,
        error = _yield$convertGroupMe.error;
      if (error) {
        if ((0, _$$_REQUIRE(_dependencyMap[16]).isServerError)(error) && error.server_error_id === _$$_REQUIRE(_dependencyMap[17]).ServerErrors.DUPLICATE_CHANNEL_NAME && (0, _$$_REQUIRE(_dependencyMap[16]).isErrorWithMessage)(error)) {
          setChannelNameErrorMessage(error.message);
        } else if ((0, _$$_REQUIRE(_dependencyMap[16]).isErrorWithMessage)(error)) {
          setErrorMessage(error.message);
        } else {
          setErrorMessage(formatMessage({
            id: 'channel_info.convert_gm_to_channel.conversion_error',
            defaultMessage: 'Something went wrong. Failed to convert Group Message to Private Channel.'
          }));
        }
        setConversionInProgress(false);
        return;
      }
      if (!updatedChannel) {
        (0, _$$_REQUIRE(_dependencyMap[18]).logError)('No updated channel received from server when converting GM to private channel');
        setErrorMessage(formatMessage({
          id: 'channel_info.convert_gm_to_channel.conversion_error',
          defaultMessage: 'Something went wrong. Failed to convert Group Message to Private Channel.'
        }));
        setConversionInProgress(false);
        return;
      }
      setErrorMessage('');
      (0, _$$_REQUIRE(_dependencyMap[15]).switchToChannelById)(serverUrl, updatedChannel.id, selectedTeam.id);
      setConversionInProgress(false);
    })), [selectedTeam, newChannelName, submitButtonEnabled]);
    if (commonTeams.length === 0) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[19]).NoCommonTeamForm, {
        containerStyles: styles.container
      });
    }
    var messageBoxHeader = formatMessage({
      id: 'channel_info.convert_gm_to_channel.warning.header',
      defaultMessage: 'Conversation history will be visible to any channel members'
    });
    var textConvert = formatMessage({
      id: 'channel_info.convert_gm_to_channel.button_text',
      defaultMessage: 'Convert to Private Channel'
    });
    var textConverting = formatMessage({
      id: 'channel_info.convert_gm_to_channel.button_text_converting',
      defaultMessage: 'Converting...'
    });
    var confirmButtonText = conversionInProgress ? textConverting : textConvert;
    var defaultUserDisplayNames = formatMessage({
      id: 'channel_info.convert_gm_to_channel.warning.body.yourself',
      defaultMessage: 'yourself'
    });
    var memberNames = profiles.length > 0 ? formatList(userDisplayNames) : defaultUserDisplayNames;
    var messageBoxBody = formatMessage({
      id: 'channel_info.convert_gm_to_channel.warning.bodyXXXX',
      defaultMessage: 'You are about to convert the Group Message with {memberNames} to a Channel. This cannot be undone.'
    }, {
      memberNames: memberNames
    });
    var buttonIcon = conversionInProgress ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
      containerStyle: styles.loadingContainerStyle,
      color: (0, _$$_REQUIRE(_dependencyMap[9]).changeOpacity)(theme.centerChannelColor, 0.32)
    }) : null;
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_message_box.default, {
        header: messageBoxHeader,
        body: messageBoxBody
      }), commonTeams.length > 1 && /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[20]).TeamSelector, {
        commonTeams: commonTeams,
        onSelectTeam: setSelectedTeam,
        selectedTeamId: selectedTeam == null ? undefined : selectedTeam.id
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[21]).ChannelNameInput, {
        onChange: setNewChannelName,
        error: channelNameErrorMessage
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_button.default, {
        onPress: handleOnPress,
        text: confirmButtonText,
        theme: theme,
        buttonType: submitButtonEnabled ? 'destructive' : 'disabled',
        size: "lg",
        iconComponent: buttonIcon
      }), errorMessage && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.errorMessage,
        children: errorMessage
      })]
    });
  };
