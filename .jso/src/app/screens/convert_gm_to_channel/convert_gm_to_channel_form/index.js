  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[0]).withObservables)([], function (_ref) {
    var database = _ref.database;
    var locale = (0, _$$_REQUIRE(_dependencyMap[1]).observeCurrentUser)(database).pipe((0, _$$_REQUIRE(_dependencyMap[2]).switchMap)(function (user) {
      return (0, _$$_REQUIRE(_dependencyMap[3]).of)(user == null ? undefined : user.locale);
    }), (0, _$$_REQUIRE(_dependencyMap[2]).distinctUntilChanged)());
    var teammateNameDisplay = (0, _$$_REQUIRE(_dependencyMap[1]).observeTeammateNameDisplay)(database);
    return {
      locale: locale,
      teammateNameDisplay: teammateNameDisplay
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[0]).withDatabase)(enhanced(_$$_REQUIRE(_dependencyMap[4]).ConvertGMToChannelForm));
