  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getBaseStyles = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.sidebarTextActiveBorder, 0.08),
        borderWidth: 1,
        borderRadius: 8,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.sidebarTextActiveBorder, 0.16),
        display: 'flex',
        flexDirection: 'row',
        padding: 16,
        gap: 12
      },
      icon: {
        marginTop: 5,
        fontSize: 20,
        width: 28,
        height: 28,
        borderWidth: 3,
        color: theme.sidebarTextActiveBorder,
        borderColor: theme.sidebarTextActiveBorder,
        borderRadius: 14,
        textAlign: 'center'
      },
      iconContainer: {},
      textContainer: {
        display: 'flex',
        flexDirection: 'column',
        gap: 8,
        flex: 1
      },
      heading: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 100, 'SemiBold')),
      body: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 100))
    };
  });
  var getDefaultStylesFromTheme = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.sidebarTextActiveBorder, 0.08),
        borderColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.sidebarTextActiveBorder, 0.16)
      },
      icon: {
        color: theme.sidebarTextActiveBorder,
        borderColor: theme.sidebarTextActiveBorder
      }
    };
  });
  var getDangerStylesFromTheme = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.dndIndicator, 0.08),
        borderColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.dndIndicator, 0.16)
      },
      icon: {
        color: theme.dndIndicator,
        borderColor: theme.dndIndicator
      }
    };
  });
  var getStyleFromTheme = function getStyleFromTheme(theme, kind) {
    var kindStyles;
    switch (kind) {
      case 'danger':
        {
          kindStyles = getDangerStylesFromTheme(theme);
          break;
        }
      default:
        {
          kindStyles = getDefaultStylesFromTheme(theme);
          break;
        }
    }
    return kindStyles;
  };
  var MessageBox = function MessageBox(_ref) {
    var header = _ref.header,
      body = _ref.body,
      type = _ref.type;
    var theme = (0, _$$_REQUIRE(_dependencyMap[7]).useTheme)();
    var baseStyle = getBaseStyles(theme);
    var kindStyle = getStyleFromTheme(theme, type);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [baseStyle.container, kindStyle.container],
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: baseStyle.iconContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          name: "exclamation-thick",
          style: [baseStyle.icon, kindStyle.icon]
        })
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: baseStyle.textContainer,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: baseStyle.heading,
            children: header
          })
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: baseStyle.body,
            children: body
          })
        })]
      })]
    });
  };
  var _default = exports.default = MessageBox;
