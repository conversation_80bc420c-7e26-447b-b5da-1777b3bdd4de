  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _convert_gm_to_channel_form = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var loadingIndicatorTimeout = 1200;
  var matchUserProfiles = function matchUserProfiles(users, members, currentUserId) {
    // Gotta make sure we use profiles that are in members.
    // See comment in fetchChannelMemberships for more details.

    var usersById = {};
    users.forEach(function (profile) {
      if (profile.id !== currentUserId) {
        usersById[profile.id] = profile;
      }
    });
    var filteredUsers = [];
    members.forEach(function (member) {
      if (usersById[member.user_id]) {
        filteredUsers.push(usersById[member.user_id]);
      }
    });
    return filteredUsers;
  };
  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[8]).makeStyleSheetFromTheme)(function (theme) {
    return {
      loadingContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,
        gap: 24
      },
      text: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.centerChannelColor, 0.56)
      }, (0, _$$_REQUIRE(_dependencyMap[9]).typography)('Heading', 300, 'SemiBold')),
      container: {
        paddingVertical: 24,
        paddingHorizontal: 20,
        display: 'flex',
        flexDirection: 'column',
        gap: 24
      }
    };
  });
  var ConvertGMToChannel = function ConvertGMToChannel(_ref) {
    var channelId = _ref.channelId,
      currentUserId = _ref.currentUserId;
    var theme = (0, _$$_REQUIRE(_dependencyMap[10]).useTheme)();
    var styles = getStyleFromTheme(theme);
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[11]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loadingAnimationTimeout = _useState2[0],
      setLoadingAnimationTimeout = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      commonTeamsFetched = _useState4[0],
      setCommonTeamsFetched = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      channelMembersFetched = _useState6[0],
      setChannelMembersFetched = _useState6[1];
    var _useState7 = (0, _react.useState)([]),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      commonTeams = _useState8[0],
      setCommonTeams = _useState8[1];
    var _useState9 = (0, _react.useState)([]),
      _useState10 = (0, _slicedToArray2.default)(_useState9, 2),
      profiles = _useState10[0],
      setProfiles = _useState10[1];
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[12]).useServerUrl)();
    var mounted = (0, _react.useRef)(false);
    var loadingAnimationTimeoutRef = (0, _react.useRef)();
    (0, _react.useEffect)(function () {
      loadingAnimationTimeoutRef.current = setTimeout(function () {
        return setLoadingAnimationTimeout(true);
      }, loadingIndicatorTimeout);
      function work() {
        return _work.apply(this, arguments);
      }
      function _work() {
        _work = (0, _asyncToGenerator2.default)(function* () {
          var _yield$fetchGroupMess = yield (0, _$$_REQUIRE(_dependencyMap[13]).fetchGroupMessageMembersCommonTeams)(serverUrl, channelId),
            teams = _yield$fetchGroupMess.teams;
          if (!teams || !mounted.current) {
            return;
          }
          setCommonTeams(teams);
          setCommonTeamsFetched(true);
        });
        return _work.apply(this, arguments);
      }
      work();
      return function () {
        clearTimeout(loadingAnimationTimeoutRef.current);
      };
    }, []);
    (0, _react.useEffect)(function () {
      mounted.current = true;
      return function () {
        mounted.current = false;
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (!currentUserId) {
        return;
      }
      var options = {
        sort: 'admin',
        active: true,
        per_page: _$$_REQUIRE(_dependencyMap[14]).PER_PAGE_DEFAULT
      };
      (0, _$$_REQUIRE(_dependencyMap[13]).fetchChannelMemberships)(serverUrl, channelId, options, true).then(function (_ref2) {
        var users = _ref2.users,
          members = _ref2.members;
        if (!mounted.current) {
          return;
        }
        if (users.length) {
          setProfiles(matchUserProfiles(users, members, currentUserId));
        }
        setChannelMembersFetched(true);
      });
    }, [serverUrl, channelId, currentUserId]);
    var showLoader = !loadingAnimationTimeout || !commonTeamsFetched || !channelMembersFetched;
    var onBackPress = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[15]).popTopScreen)("ConvertGMToChannel");
    }, ["ConvertGMToChannel"]);
    (0, _android_back_handler.default)("ConvertGMToChannel", onBackPress);
    if (showLoader) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_loading.default, {
        containerStyle: styles.loadingContainer,
        size: "large",
        color: theme.buttonBg,
        footerText: formatMessage({
          id: 'channel_info.convert_gm_to_channel.loading.footer',
          defaultMessage: 'Fetching details...'
        }),
        footerTextStyles: styles.text
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_convert_gm_to_channel_form.default, {
      commonTeams: commonTeams,
      profiles: profiles,
      channelId: channelId
    });
  };
  var _default = exports.default = ConvertGMToChannel;
