  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ChannelNameInput = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _floating_text_input_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var ChannelNameInput = exports.ChannelNameInput = function ChannelNameInput(_ref) {
    var error = _ref.error,
      onChange = _ref.onChange;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[4]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var theme = (0, _$$_REQUIRE(_dependencyMap[5]).useTheme)();
    var labelDisplayName = formatMessage({
      id: 'channel_modal.name',
      defaultMessage: 'Name'
    });
    var placeholder = formatMessage({
      id: 'channel_modal.name',
      defaultMessage: 'Channel Name'
    });
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_text_input_label.default, {
      autoCorrect: false,
      autoCapitalize: "none",
      blurOnSubmit: false,
      disableFullscreenUI: true,
      enablesReturnKeyAutomatically: true,
      label: labelDisplayName,
      placeholder: placeholder,
      maxLength: _$$_REQUIRE(_dependencyMap[6]).Channel.MAX_CHANNEL_NAME_LENGTH,
      keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[7]).getKeyboardAppearanceFromTheme)(theme),
      returnKeyType: "next",
      showErrorIcon: true,
      spellCheck: false,
      testID: "gonvert_gm_to_channel.channel_display_name.input",
      theme: theme,
      error: error,
      onChangeText: onChange
    });
  };
