  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TeamSelector = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _option_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleFromTheme = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      teamSelector: {
        borderTopWidth: 1,
        borderBottomWidth: 1,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.08)
      },
      labelContainerStyle: {
        flexShrink: 0
      }
    };
  });
  var TeamSelector = exports.TeamSelector = function TeamSelector(_ref) {
    var commonTeams = _ref.commonTeams,
      onSelectTeam = _ref.onSelectTeam,
      selectedTeamId = _ref.selectedTeamId;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var theme = (0, _$$_REQUIRE(_dependencyMap[8]).useTheme)();
    var styles = getStyleFromTheme(theme);
    var label = formatMessage({
      id: 'channel_into.convert_gm_to_channel.team_selector.label',
      defaultMessage: 'Team'
    });
    var placeholder = formatMessage({
      id: 'channel_into.convert_gm_to_channel.team_selector.placeholder',
      defaultMessage: 'Select a Team'
    });
    var selectedTeam = (0, _react.useMemo)(function () {
      return commonTeams.find(function (t) {
        return t.id === selectedTeamId;
      });
    }, [commonTeams, selectedTeamId]);
    var selectTeam = (0, _react.useCallback)(function (teamId) {
      var team = commonTeams.find(function (t) {
        return t.id === teamId;
      });
      if (team) {
        onSelectTeam(team);
      }
    }, []);
    var goToTeamSelectorList = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[9]).preventDoubleTap)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      yield (0, _$$_REQUIRE(_dependencyMap[10]).dismissBottomSheet)();
      var title = formatMessage({
        id: 'channel_info.convert_gm_to_channel.team_selector_list.title',
        defaultMessage: 'Select Team'
      });
      (0, _$$_REQUIRE(_dependencyMap[10]).goToScreen)(_$$_REQUIRE(_dependencyMap[11]).Screens.TEAM_SELECTOR_LIST, title, {
        teams: commonTeams,
        selectTeam: selectTeam,
        selectedTeamId: selectedTeamId
      });
    })), [commonTeams, selectTeam, selectedTeamId]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_option_item.default, {
      action: goToTeamSelectorList,
      containerStyle: styles.teamSelector,
      label: label,
      type: _reactNative.Platform.select({
        ios: 'arrow',
        default: 'default'
      }),
      info: selectedTeam ? selectedTeam.display_name : placeholder,
      labelContainerStyle: styles.labelContainerStyle
    });
  };
