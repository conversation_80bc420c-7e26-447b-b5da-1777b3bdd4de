  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _convert_gm_to_channel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhance = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)([], function (_ref) {
    var database = _ref.database;
    var currentUserId = (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentUserId)(database);
    var teammateNameDisplay = (0, _$$_REQUIRE(_dependencyMap[4]).observeTeammateNameDisplay)(database);
    return {
      currentUserId: currentUserId,
      teammateNameDisplay: teammateNameDisplay
    };
  });
  var _default = exports.default = (0, _$$_REQUIRE(_dependencyMap[2]).withDatabase)(enhance(_convert_gm_to_channel.default));
