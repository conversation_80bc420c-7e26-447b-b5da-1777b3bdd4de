  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _syntax_highlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _android_back_handler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var edges = ['left', 'right'];
  var styles = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    }
  });
  var Code = function Code(_ref) {
    var code = _ref.code,
      componentId = _ref.componentId,
      language = _ref.language,
      textStyle = _ref.textStyle;
    var managedConfig = (0, _$$_REQUIRE(_dependencyMap[6]).useManagedConfig)();
    (0, _android_back_handler.default)(componentId, _$$_REQUIRE(_dependencyMap[7]).popTopScreen);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[8]).SafeAreaView, {
      edges: edges,
      style: styles.flex,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_syntax_highlight.default, {
        code: code,
        language: language,
        selectable: managedConfig.copyAndPasteProtection !== 'true',
        textStyle: textStyle
      })
    });
  };
  var _default = exports.default = Code;
