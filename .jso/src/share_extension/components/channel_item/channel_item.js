  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.textStyle = exports.getStyleSheet = exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = exports.getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flexDirection: 'row',
        paddingHorizontal: 20,
        minHeight: 40,
        alignItems: 'center'
      },
      infoItem: {
        paddingHorizontal: 0
      },
      wrapper: {
        flex: 1,
        flexDirection: 'row'
      },
      text: {
        marginTop: -1,
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.sidebarText, 0.72),
        paddingLeft: 12,
        paddingRight: 20
      },
      textInfo: {
        color: theme.centerChannelColor,
        paddingRight: 20
      },
      teamName: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.64),
        paddingLeft: 12,
        marginTop: 4
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 75))
    };
  });
  var textStyle = exports.textStyle = _reactNative.StyleSheet.create({
    bold: (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Body', 200, 'SemiBold'),
    regular: (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Body', 200, 'Regular')
  });
  var ChannelListItem = function ChannelListItem(_ref) {
    var channel = _ref.channel,
      currentUserId = _ref.currentUserId,
      membersCount = _ref.membersCount,
      hasMember = _ref.hasMember,
      onPress = _ref.onPress,
      teamDisplayName = _ref.teamDisplayName,
      theme = _ref.theme;
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)(),
      formatMessage = _useIntl.formatMessage;
    var styles = getStyleSheet(theme);
    var height = (0, _react.useMemo)(function () {
      return teamDisplayName ? 58 : 44;
    }, [teamDisplayName]);
    var handleOnPress = (0, _react.useCallback)(function () {
      onPress(channel.id);
    }, [channel.id]);
    var textStyles = (0, _react.useMemo)(function () {
      return [textStyle.regular, styles.text, styles.textInfo];
    }, [styles]);
    var containerStyle = (0, _react.useMemo)(function () {
      return [styles.container, styles.infoItem, {
        minHeight: height
      }];
    }, [height, styles]);
    if (!hasMember) {
      return null;
    }
    var teammateId = channel.type === _$$_REQUIRE(_dependencyMap[8]).General.DM_CHANNEL ? (0, _$$_REQUIRE(_dependencyMap[9]).getUserIdFromChannelName)(currentUserId, channel.name) : undefined;
    var isOwnDirectMessage = channel.type === _$$_REQUIRE(_dependencyMap[8]).General.DM_CHANNEL && currentUserId === teammateId;
    var displayName = channel.displayName;
    if (isOwnDirectMessage) {
      displayName = formatMessage({
        id: 'channel_header.directchannel.you',
        defaultMessage: '{displayName} (you)'
      }, {
        displayName: displayName
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: handleOnPress,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: containerStyle,
          children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.wrapper,
            children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_icon.default, {
              database: channel.database,
              membersCount: membersCount,
              name: channel.name,
              shared: channel.shared,
              size: 24,
              theme: theme,
              type: channel.type
            }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
              children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                ellipsizeMode: "tail",
                numberOfLines: 1,
                style: textStyles,
                children: displayName
              }), Boolean(teamDisplayName) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
                ellipsizeMode: "tail",
                numberOfLines: 1,
                style: styles.teamName,
                children: teamDisplayName
              })]
            })]
          })
        })
      })
    });
  };
  var _default = exports.default = ChannelListItem;
