  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        marginLeft: 4
      },
      icon: {
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.72),
        left: 1
      },
      image: {
        borderRadius: 12,
        height: 24,
        width: 24
      }
    };
  });
  var Avatar = function Avatar(_ref) {
    var author = _ref.author,
      theme = _ref.theme;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[6]).useShareExtensionServerUrl)();
    var style = getStyleSheet(theme);
    var pictureUrl = '';
    if (author != null && author.deleteAt) {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        name: "archive-outline",
        style: style.icon,
        size: 24
      });
    }
    if (author && serverUrl) {
      pictureUrl = (0, _$$_REQUIRE(_dependencyMap[7]).buildProfileImageUrlFromUser)(serverUrl, author);
    }
    var icon;
    if (pictureUrl && serverUrl) {
      var imgSource = {
        uri: (0, _$$_REQUIRE(_dependencyMap[8]).buildAbsoluteUrl)(serverUrl, pictureUrl)
      };
      icon = /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[9]).Image, {
        style: style.image,
        source: imgSource
      }, pictureUrl);
    } else {
      icon = /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.72),
        name: _$$_REQUIRE(_dependencyMap[10]).ACCOUNT_OUTLINE_IMAGE,
        size: 24
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: style.container,
      children: icon
    });
  };
  var _default = exports.default = Avatar;
