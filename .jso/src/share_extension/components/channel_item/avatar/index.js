  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _avatar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhance = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['channelName', 'database'], function (_ref) {
    var channelName = _ref.channelName,
      database = _ref.database;
    var currentUserId = (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentUserId)(database);
    var authorId = currentUserId.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (userId) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)((0, _$$_REQUIRE(_dependencyMap[6]).getUserIdFromChannelName)(userId, channelName));
    }));
    var author = authorId.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (id) {
      return (0, _$$_REQUIRE(_dependencyMap[7]).observeUser)(database, id);
    }));
    return {
      author: author
    };
  });
  var _default = exports.default = enhance(_avatar.default);
