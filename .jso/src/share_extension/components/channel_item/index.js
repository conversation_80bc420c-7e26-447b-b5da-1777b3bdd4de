  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _channel_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhance = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)(['channel', 'showTeamName'], function (_ref) {
    var channel = _ref.channel,
      showTeamName = _ref.showTeamName;
    var database = channel.database;
    var currentUserId = (0, _$$_REQUIRE(_dependencyMap[4]).observeCurrentUserId)(database);
    var myChannel = (0, _$$_REQUIRE(_dependencyMap[5]).observeMyChannel)(database, channel.id);
    var teamDisplayName = (0, _$$_REQUIRE(_dependencyMap[6]).of)('');
    if (channel.teamId && showTeamName) {
      teamDisplayName = (0, _$$_REQUIRE(_dependencyMap[7]).observeTeam)(database, channel.teamId).pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (team) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)((team == null ? undefined : team.displayName) || '');
      }), (0, _$$_REQUIRE(_dependencyMap[8]).distinctUntilChanged)());
    }
    var membersCount = (0, _$$_REQUIRE(_dependencyMap[6]).of)(0);
    if (channel.type === _$$_REQUIRE(_dependencyMap[9]).General.GM_CHANNEL) {
      membersCount = (0, _$$_REQUIRE(_dependencyMap[5]).queryChannelMembers)(database, channel.id).observeCount(false);
    }
    var hasMember = myChannel.pipe((0, _$$_REQUIRE(_dependencyMap[8]).switchMap)(function (mc) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(Boolean(mc));
    }), (0, _$$_REQUIRE(_dependencyMap[8]).distinctUntilChanged)());
    return {
      channel: channel.observe(),
      currentUserId: currentUserId,
      membersCount: membersCount,
      teamDisplayName: teamDisplayName,
      hasMember: hasMember
    };
  });
  var _default = exports.default = _react2.default.memo((0, _$$_REQUIRE(_dependencyMap[10]).withServerUrl)(enhance(_channel_item.default)));
