  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _general = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _avatar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        alignItems: 'center',
        justifyContent: 'center'
      },
      icon: {
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.sidebarText, 0.4)
      },
      iconInfo: {
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.72)
      },
      groupBox: {
        alignItems: 'center',
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.sidebarText, 0.16),
        borderRadius: 4,
        justifyContent: 'center'
      },
      groupBoxInfo: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.3)
      },
      group: Object.assign({
        color: theme.sidebarText
      }, (0, _$$_REQUIRE(_dependencyMap[8]).typography)('Heading', 75, 'SemiBold')),
      groupInfo: {
        color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.72)
      }
    };
  });
  var ChannelIcon = function ChannelIcon(_ref) {
    var database = _ref.database,
      _ref$membersCount = _ref.membersCount,
      membersCount = _ref$membersCount === undefined ? 0 : _ref$membersCount,
      name = _ref.name,
      shared = _ref.shared,
      _ref$size = _ref.size,
      size = _ref$size === undefined ? 12 : _ref$size,
      style = _ref.style,
      theme = _ref.theme,
      type = _ref.type;
    var styles = getStyleSheet(theme);
    var icon;
    if (shared) {
      var iconName = type === _general.default.PRIVATE_CHANNEL ? 'circle-multiple-outline-lock' : 'circle-multiple-outline';
      icon = /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        name: iconName,
        style: [styles.icon, styles.iconInfo, {
          fontSize: size,
          left: 0.5
        }]
      });
    } else if (type === _general.default.OPEN_CHANNEL) {
      icon = /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        name: "globe",
        style: [styles.icon, styles.iconInfo, {
          fontSize: size,
          left: 1
        }]
      });
    } else if (type === _general.default.PRIVATE_CHANNEL) {
      icon = /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        name: "lock-outline",
        style: [styles.icon, styles.iconInfo, {
          fontSize: size,
          left: 0.5
        }]
      });
    } else if (type === _general.default.GM_CHANNEL) {
      var fontSize = size - 12;
      icon = /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.groupBox, styles.groupBoxInfo, {
          width: size,
          height: size
        }],
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [styles.group, styles.groupInfo, {
            fontSize: fontSize
          }],
          children: membersCount - 1
        })
      });
    } else if (type === _general.default.DM_CHANNEL) {
      icon = /*#__PURE__*/(0, _jsxRuntime.jsx)(_avatar.default, {
        channelName: name,
        database: database,
        theme: theme
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.container, {
        width: size,
        height: size
      }, style],
      children: icon
    });
  };
  var _default = exports.default = _react.default.memo(ChannelIcon);
