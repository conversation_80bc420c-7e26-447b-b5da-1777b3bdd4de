  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.MAX_RESULTS = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _no_results_with_term = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _channel_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var style = _reactNative.StyleSheet.create({
    flex: {
      flex: 1
    },
    noResultContainer: {
      flexGrow: 1,
      alignItems: 'center',
      justifyContent: 'center'
    },
    list: {
      flexGrow: 1
    }
  });
  var MAX_RESULTS = exports.MAX_RESULTS = 20;
  var SearchChannels = function SearchChannels(_ref) {
    var archivedChannels = _ref.archivedChannels,
      channelsMatch = _ref.channelsMatch,
      channelsMatchStart = _ref.channelsMatchStart,
      showTeamName = _ref.showTeamName,
      term = _ref.term,
      theme = _ref.theme;
    var navigation = (0, _$$_REQUIRE(_dependencyMap[8]).useNavigation)();
    var _useIntl = (0, _$$_REQUIRE(_dependencyMap[9]).useIntl)(),
      locale = _useIntl.locale;
    var onPress = (0, _react.useCallback)(function (channelId) {
      (0, _$$_REQUIRE(_dependencyMap[10]).setShareExtensionChannelId)(channelId);
      navigation.goBack();
    }, []);
    var renderEmpty = (0, _react.useCallback)(function () {
      if (term) {
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: style.noResultContainer,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_no_results_with_term.default, {
            term: term
          })
        });
      }
      return null;
    }, [term, theme]);
    var renderItem = (0, _react.useCallback)(function (_ref2) {
      var item = _ref2.item;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_item.default, {
        channel: item,
        onPress: onPress,
        showTeamName: showTeamName,
        theme: theme
      });
    }, [showTeamName]);
    var data = (0, _react.useMemo)(function () {
      var items = (0, _toConsumableArray2.default)(channelsMatchStart);

      // Channels that matches
      if (items.length < MAX_RESULTS) {
        items.push.apply(items, (0, _toConsumableArray2.default)(channelsMatch));
      }

      // Archived channels local
      if (items.length < MAX_RESULTS) {
        var archivedAlpha = archivedChannels.sort(_$$_REQUIRE(_dependencyMap[11]).sortChannelsByDisplayName.bind(null, locale));
        items.push.apply(items, (0, _toConsumableArray2.default)(archivedAlpha.slice(0, MAX_RESULTS + 1)));
      }
      return (0, _toConsumableArray2.default)(new Set(items)).slice(0, MAX_RESULTS + 1);
    }, [archivedChannels, channelsMatchStart, channelsMatch, locale]);
    if ((data == null ? undefined : data.length) <= 0) return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {});
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      entering: _reactNativeReanimated.FadeInDown.duration(100),
      exiting: _reactNativeReanimated.FadeOutUp.duration(100),
      style: style.flex,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        contentContainerStyle: style.list,
        keyboardDismissMode: "interactive",
        keyboardShouldPersistTaps: "handled",
        ListEmptyComponent: renderEmpty,
        renderItem: renderItem,
        data: data,
        showsVerticalScrollIndicator: false
      })
    });
  };
  var _default = exports.default = SearchChannels;
