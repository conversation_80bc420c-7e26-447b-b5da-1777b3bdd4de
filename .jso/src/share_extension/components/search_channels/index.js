  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _search_channels = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[1]).withObservables)(['term', 'database'], function (_ref) {
    var database = _ref.database,
      term = _ref.term;
    var teamsCount = (0, _$$_REQUIRE(_dependencyMap[2]).queryJoinedTeams)(database).observeCount();
    var joinedChannelsMatchStart = (0, _$$_REQUIRE(_dependencyMap[3]).observeJoinedChannelsByTerm)(database, term, _search_channels.MAX_RESULTS, true);
    var joinedChannelsMatch = (0, _$$_REQUIRE(_dependencyMap[3]).observeJoinedChannelsByTerm)(database, term, _search_channels.MAX_RESULTS);
    var directChannelsMatchStart = (0, _$$_REQUIRE(_dependencyMap[3]).observeDirectChannelsByTerm)(database, term, _search_channels.MAX_RESULTS, true);
    var directChannelsMatch = (0, _$$_REQUIRE(_dependencyMap[3]).observeDirectChannelsByTerm)(database, term, _search_channels.MAX_RESULTS);
    var channelsMatchStart = joinedChannelsMatchStart.pipe((0, _$$_REQUIRE(_dependencyMap[4]).combineLatestWith)(directChannelsMatchStart), (0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (matchStart) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).retrieveChannels)(database, matchStart.flat(), true);
    }));
    var channelsMatch = joinedChannelsMatch.pipe((0, _$$_REQUIRE(_dependencyMap[4]).combineLatestWith)(directChannelsMatch), (0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (matched) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).retrieveChannels)(database, matched.flat(), true);
    }));
    var archivedChannels = (0, _$$_REQUIRE(_dependencyMap[3]).observeArchiveChannelsByTerm)(database, term, _search_channels.MAX_RESULTS).pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (archived) {
      return (0, _$$_REQUIRE(_dependencyMap[5]).retrieveChannels)(database, archived);
    }));
    return {
      archivedChannels: archivedChannels,
      channelsMatch: channelsMatch,
      channelsMatchStart: channelsMatchStart,
      showTeamName: teamsCount.pipe((0, _$$_REQUIRE(_dependencyMap[4]).switchMap)(function (count) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(count > 1);
      }))
    };
  });
  var _default = exports.default = enhanced(_search_channels.default);
