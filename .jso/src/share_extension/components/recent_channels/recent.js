  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _channel_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _header = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var buildSections = function buildSections(recentChannels) {
    var sections = [{
      data: recentChannels
    }];
    return sections;
  };
  var RecentList = function RecentList(_ref) {
    var recentChannels = _ref.recentChannels,
      showTeamName = _ref.showTeamName,
      theme = _ref.theme;
    var navigation = (0, _$$_REQUIRE(_dependencyMap[7]).useNavigation)();
    var _useState = (0, _react.useState)(buildSections(recentChannels)),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      sections = _useState2[0],
      setSections = _useState2[1];
    var onPress = (0, _react.useCallback)(function (channelId) {
      (0, _$$_REQUIRE(_dependencyMap[8]).setShareExtensionChannelId)(channelId);
      navigation.goBack();
    }, []);
    var renderSectionHeader = (0, _react.useCallback)(function () {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_header.default, {
        theme: theme
      });
    }, [theme]);
    var renderSectionItem = (0, _react.useCallback)(function (_ref2) {
      var item = _ref2.item;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_channel_item.default, {
        channel: item,
        onPress: onPress,
        showTeamName: showTeamName,
        theme: theme
      });
    }, [onPress, showTeamName]);
    (0, _react.useEffect)(function () {
      setSections(buildSections(recentChannels));
    }, [recentChannels]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.SectionList, {
      keyboardDismissMode: "interactive",
      keyboardShouldPersistTaps: "handled",
      renderItem: renderSectionItem,
      renderSectionHeader: renderSectionHeader,
      sections: sections,
      showsVerticalScrollIndicator: false,
      stickySectionHeadersEnabled: true
    });
  };
  var _default = exports.default = RecentList;
