  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyles = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        paddingVertical: 8,
        paddingTop: 12,
        paddingLeft: 2,
        flexDirection: 'row',
        alignItems: 'flex-start',
        backgroundColor: theme.centerChannelBg
      },
      heading: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.64),
        textTransform: 'uppercase'
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 75, 'SemiBold'))
    };
  });
  var RecentHeader = function RecentHeader(_ref) {
    var theme = _ref.theme;
    var styles = getStyles(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        id: "mobile.channel_list.recent",
        defaultMessage: "Recent",
        style: styles.heading
      })
    });
  };
  var _default = exports.default = RecentHeader;
