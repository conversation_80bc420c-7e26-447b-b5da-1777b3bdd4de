  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _recent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var MAX_CHANNELS = 20;
  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['database'], function (_ref) {
    var database = _ref.database;
    var teamsCount = (0, _$$_REQUIRE(_dependencyMap[3]).queryJoinedTeams)(database).observeCount();
    var recentChannels = (0, _$$_REQUIRE(_dependencyMap[4]).queryMyRecentChannels)(database, MAX_CHANNELS).observeWithColumns(['last_viewed_at']).pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (myChannels) {
      return (0, _$$_REQUIRE(_dependencyMap[6]).retrieveChannels)(database, myChannels, true);
    }));
    return {
      recentChannels: recentChannels,
      showTeamName: teamsCount.pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (count) {
        return (0, _$$_REQUIRE(_dependencyMap[7]).of)(count > 1);
      }))
    };
  });
  var _default = exports.default = enhanced(_recent.default);
