  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _option = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    container: {
      marginHorizontal: 20
    }
  });
  var Options = function Options(_ref) {
    var channelDisplayName = _ref.channelDisplayName,
      hasChannels = _ref.hasChannels,
      serverDisplayName = _ref.serverDisplayName,
      theme = _ref.theme;
    var navigator = (0, _$$_REQUIRE(_dependencyMap[6]).useNavigation)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)();
    var serverLabel = (0, _react.useMemo)(function () {
      return intl.formatMessage({
        id: 'share_extension.server_label',
        defaultMessage: 'Server'
      });
    }, [intl.locale]);
    var channelLabel = (0, _react.useMemo)(function () {
      return intl.formatMessage({
        id: 'share_extension.channel_label',
        defaultMessage: 'Channel'
      });
    }, [intl.locale]);
    var errorLabel = (0, _react.useMemo)(function () {
      return intl.formatMessage({
        id: 'share_extension.channel_error',
        defaultMessage: 'You are not a member of a team on the selected server. Select another server or open Mattermost to join a team.'
      });
    }, [intl.locale]);
    var onServerPress = (0, _react.useCallback)(function () {
      navigator.navigate('Servers');
    }, []);
    var onChannelPress = (0, _react.useCallback)(function () {
      navigator.navigate('Channels');
    }, []);
    var channel;
    if (hasChannels) {
      channel = /*#__PURE__*/(0, _jsxRuntime.jsx)(_option.default, {
        label: channelLabel,
        value: channelDisplayName || '',
        onPress: onChannelPress,
        theme: theme
      });
    } else {
      channel = /*#__PURE__*/(0, _jsxRuntime.jsx)(_label.default, {
        style: {
          marginHorizontal: 0
        },
        text: errorLabel,
        theme: theme
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_option.default, {
        label: serverLabel,
        value: serverDisplayName,
        onPress: onServerPress,
        theme: theme
      }), channel]
    });
  };
  var _default = exports.default = Options;
