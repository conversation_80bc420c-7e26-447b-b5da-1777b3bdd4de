  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _options = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[3]).withObservables)(['database', 'channelId', 'serverUrl'], function (_ref) {
    var database = _ref.database,
      channelId = _ref.channelId,
      serverUrl = _ref.serverUrl;
    var channel = (0, _$$_REQUIRE(_dependencyMap[4]).observeChannel)(database, channelId || '');
    var teamsCount = (0, _$$_REQUIRE(_dependencyMap[5]).queryJoinedTeams)(database).observeCount();
    var team = channel.pipe((0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (c) {
      return c != null && c.teamId ? (0, _$$_REQUIRE(_dependencyMap[5]).observeTeam)(database, c.teamId) : (0, _$$_REQUIRE(_dependencyMap[6]).of)(undefined);
    }));
    var channelDisplayName = channel.pipe((0, _$$_REQUIRE(_dependencyMap[6]).combineLatestWith)(team, teamsCount), (0, _$$_REQUIRE(_dependencyMap[6]).switchMap)(function (_ref2) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 3),
        c = _ref3[0],
        t = _ref3[1],
        count = _ref3[2];
      if (!c) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(undefined);
      }
      if (t) {
        return (0, _$$_REQUIRE(_dependencyMap[6]).of)(count > 1 ? `${c.displayName} (${t.displayName})` : c.displayName);
      }
      return (0, _$$_REQUIRE(_dependencyMap[6]).of)(c.displayName);
    }));
    return {
      channelDisplayName: channelDisplayName,
      serverDisplayName: (0, _$$_REQUIRE(_dependencyMap[7]).observeServerDisplayName)(serverUrl || ''),
      hasChannels: (0, _$$_REQUIRE(_dependencyMap[8]).observeServerHasChannels)(serverUrl || '')
    };
  });
  var _default = exports.default = enhanced(_options.default);
