  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyles = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flexDirection: 'row',
        height: 48,
        alignItems: 'center'
      },
      label: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200)),
      row: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'flex-end'
      },
      value: Object.assign({
        alignItems: 'flex-end',
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.56),
        top: 2,
        flexShrink: 1,
        marginLeft: 10
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 100))
    };
  });
  var Option = function Option(_ref) {
    var label = _ref.label,
      onPress = _ref.onPress,
      theme = _ref.theme,
      value = _ref.value;
    var styles = getStyles(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: onPress,
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.label,
        children: label
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.row,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          numberOfLines: 1,
          style: styles.value,
          children: value
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.32),
          name: "chevron-right",
          size: 24
        })]
      })]
    });
  };
  var _default = exports.default = Option;
