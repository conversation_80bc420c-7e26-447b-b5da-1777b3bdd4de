  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _content_view = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['database'], function (_ref) {
    var database = _ref.database;
    return {
      currentUserId: (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentUserId)(database),
      currentChannelId: (0, _$$_REQUIRE(_dependencyMap[3]).observeCurrentChannelId)(database)
    };
  });
  var _default = exports.default = enhanced(_content_view.default);
