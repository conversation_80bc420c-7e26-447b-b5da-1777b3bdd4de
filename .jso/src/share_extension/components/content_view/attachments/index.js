  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _attachments = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var enhanced = (0, _$$_REQUIRE(_dependencyMap[2]).withObservables)(['database'], function (_ref) {
    var database = _ref.database;
    return {
      canUploadFiles: (0, _$$_REQUIRE(_dependencyMap[3]).observeCanUploadFiles)(database),
      maxFileCount: (0, _$$_REQUIRE(_dependencyMap[3]).observeMaxFileCount)(database),
      maxFileSize: (0, _$$_REQUIRE(_dependencyMap[3]).observeConfigIntValue)(database, 'MaxFileSize')
    };
  });
  var _default = exports.default = enhanced(_attachments.default);
