  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _single = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getKey = function getKey(item) {
    return item.value;
  };
  var getStyles = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      first: {
        marginLeft: 20
      },
      item: {
        marginRight: 12
      },
      last: {
        marginRight: 20
      },
      list: {
        height: 114,
        top: -8,
        width: '100%'
      },
      container: {
        alignItems: 'flex-end'
      },
      labelContainer: {
        alignItems: 'flex-start',
        marginTop: 8,
        paddingHorizontal: 20,
        width: '100%'
      },
      label: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.72)
      }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 75))
    };
  });
  var Multiple = function Multiple(_ref) {
    var files = _ref.files,
      maxFileSize = _ref.maxFileSize,
      theme = _ref.theme;
    var styles = getStyles(theme);
    var count = (0, _react.useMemo)(function () {
      return {
        count: files.length
      };
    }, [files.length]);
    var renderItem = (0, _react.useCallback)(function (_ref2) {
      var item = _ref2.item,
        index = _ref2.index;
      var containerStyle = [styles.item];
      if (index === 0) {
        containerStyle.push(styles.first);
      } else if (index === files.length - 1) {
        containerStyle.push(styles.last);
      }
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: containerStyle,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_single.default, {
          file: item,
          isSmall: true,
          maxFileSize: maxFileSize,
          theme: theme
        })
      });
    }, [maxFileSize, theme, files]);
    if ((files == null ? undefined : files.length) <= 0) return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {});
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: files,
        horizontal: true,
        keyExtractor: getKey,
        renderItem: renderItem,
        style: styles.list,
        contentContainerStyle: styles.container,
        overScrollMode: "always"
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.labelContainer,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
          id: "share_extension.multiple_label",
          defaultMessage: "{count, number} attachments",
          values: count,
          style: styles.label
        })
      })]
    });
  };
  var _default = exports.default = Multiple;
