  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _info = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _thumbnail = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var hitSlop = {
    top: 10,
    left: 10,
    right: 10,
    bottom: 10
  };
  var layoutAnimConfig = {
    duration: 300,
    update: {
      type: _reactNative.LayoutAnimation.Types.easeInEaseOut
    },
    delete: {
      duration: 100,
      type: _reactNative.LayoutAnimation.Types.easeInEaseOut,
      property: _reactNative.LayoutAnimation.Properties.opacity
    }
  };
  var getStyles = (0, _$$_REQUIRE(_dependencyMap[7]).makeStyleSheetFromTheme)(function (theme) {
    return {
      remove: {
        backgroundColor: theme.centerChannelBg,
        borderRadius: 12,
        height: 24,
        position: 'absolute',
        right: -5,
        top: -7,
        width: 24
      }
    };
  });
  var Single = function Single(_ref) {
    var file = _ref.file,
      isSmall = _ref.isSmall,
      maxFileSize = _ref.maxFileSize,
      theme = _ref.theme;
    var styles = getStyles(theme);
    var fileInfo = (0, _react.useMemo)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[8]).toFileInfo)(file);
    }, [file]);
    var contentMode = isSmall ? 'small' : 'large';
    var type = (0, _react.useMemo)(function () {
      if ((0, _$$_REQUIRE(_dependencyMap[9]).isImage)(fileInfo)) {
        return 'image';
      }
      if ((0, _$$_REQUIRE(_dependencyMap[9]).isVideo)(fileInfo)) {
        return 'video';
      }
      return undefined;
    }, [fileInfo]);
    var hasError = (0, _react.useMemo)(function () {
      var size = file.size || 0;
      if (size > maxFileSize) {
        return true;
      }
      if (type === 'image' && file.height && file.width) {
        return file.width * file.height > _$$_REQUIRE(_dependencyMap[10]).MAX_RESOLUTION;
      }
      return false;
    }, [file, maxFileSize, type]);
    var onPress = (0, _react.useCallback)(function () {
      _reactNative.LayoutAnimation.configureNext(layoutAnimConfig);
      (0, _$$_REQUIRE(_dependencyMap[11]).removeShareExtensionFile)(file);
    }, [file]);
    var attachment;
    if (type) {
      attachment = /*#__PURE__*/(0, _jsxRuntime.jsx)(_thumbnail.default, {
        contentMode: contentMode,
        file: file,
        hasError: hasError,
        theme: theme,
        type: type
      });
    } else {
      attachment = /*#__PURE__*/(0, _jsxRuntime.jsx)(_info.default, {
        contentMode: contentMode,
        file: fileInfo,
        hasError: hasError,
        theme: theme
      });
    }
    if (isSmall) {
      return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [attachment, /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.remove,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            hitSlop: hitSlop,
            onPress: onPress,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
              name: "close-circle",
              size: 24,
              color: (0, _$$_REQUIRE(_dependencyMap[7]).changeOpacity)(theme.centerChannelColor, 0.56)
            })
          })
        })]
      });
    }
    return attachment;
  };
  var _default = exports.default = Single;
