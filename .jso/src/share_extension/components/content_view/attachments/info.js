  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _file_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyles = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        alignItems: 'center',
        borderColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.16),
        borderWidth: 1,
        flexDirection: 'row',
        height: 64,
        justifyContent: 'flex-start',
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2
        },
        shadowRadius: 3,
        shadowOpacity: 0.8,
        width: '100%'
      },
      error: {
        borderColor: theme.errorTextColor,
        borderWidth: 2
      },
      info: Object.assign({
        textTransform: 'uppercase',
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.64)
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 75)),
      name: Object.assign({
        color: theme.centerChannelColor
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200, 'SemiBold')),
      small: {
        flexDirection: 'column',
        height: 104,
        justifyContent: 'center',
        paddingHorizontal: 4,
        width: 104
      },
      smallName: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 75, 'SemiBold')),
      smallWrapper: {
        alignItems: 'center'
      }
    };
  });
  var Info = function Info(_ref) {
    var contentMode = _ref.contentMode,
      file = _ref.file,
      hasError = _ref.hasError,
      theme = _ref.theme;
    var styles = getStyles(theme);
    var containerStyle = (0, _react.useMemo)(function () {
      return [styles.container, contentMode === 'small' && styles.small, hasError && styles.error];
    }, [contentMode, hasError]);
    var textContainerStyle = (0, _react.useMemo)(function () {
      return contentMode === 'small' && styles.smallWrapper;
    }, [contentMode]);
    var nameStyle = (0, _react.useMemo)(function () {
      return [styles.name, contentMode === 'small' && styles.smallName];
    }, [contentMode]);
    var size = (0, _react.useMemo)(function () {
      return `${file.extension} ${(0, _$$_REQUIRE(_dependencyMap[7]).getFormattedFileSize)(file.size)}`;
    }, [file.size, file.extension]);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: containerStyle,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_file_icon.default, {
        file: file
      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: textContainerStyle,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          numberOfLines: 1,
          style: nameStyle,
          children: file.name
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.info,
          children: size
        })]
      })]
    });
  };
  var _default = exports.default = Info;
