  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var GRADIENT_COLORS = ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, .16)'];
  var GRADIENT_END = {
    x: 1,
    y: 1
  };
  var GRADIENT_LOCATIONS = [0.5, 1];
  var GRADIENT_START = {
    x: 0.3,
    y: 0.3
  };
  var getStyles = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        borderRadius: 4,
        borderColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.16),
        borderWidth: 1,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2
        },
        shadowRadius: 3,
        shadowOpacity: 0.8,
        elevation: 1
      },
      center: {
        alignItems: 'center',
        justifyContent: 'center'
      },
      error: {
        borderColor: theme.errorTextColor,
        borderWidth: 2
      },
      play: Object.assign({
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
        padding: 2
      }, _reactNative.StyleSheet.absoluteFillObject),
      radius: {
        borderRadius: 4
      }
    };
  });
  var Thumbnail = function Thumbnail(_ref) {
    var contentMode = _ref.contentMode,
      file = _ref.file,
      hasError = _ref.hasError,
      theme = _ref.theme,
      type = _ref.type;
    var dimensions = (0, _reactNative.useWindowDimensions)();
    var styles = getStyles(theme);
    var isSmall = contentMode === 'small';
    var imgStyle = (0, _react.useMemo)(function () {
      if (isSmall) {
        return {
          height: 104,
          width: 104
        };
      }
      if (!file.width || !file.height) {
        return {
          height: 0,
          width: 0
        };
      }
      return (0, _$$_REQUIRE(_dependencyMap[6]).imageDimensions)(file.height, file.width, 156, dimensions.width - 20);
    }, [isSmall, file, dimensions.width]);
    var containerStyle = (0, _react.useMemo)(function () {
      return [styles.container, hasError && styles.error, styles.radius];
    }, [styles, imgStyle, hasError]);
    var source = (0, _react.useMemo)(function () {
      return {
        uri: type === 'video' ? file.videoThumb : file.value
      };
    }, [type, file]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: containerStyle,
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.center,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[7]).Image, {
          source: source,
          style: [imgStyle, styles.radius],
          contentFit: "cover"
        }), type === 'video' && /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[8]).LinearGradient, {
            start: GRADIENT_START,
            end: GRADIENT_END,
            locations: GRADIENT_LOCATIONS,
            colors: GRADIENT_COLORS,
            style: _reactNative.StyleSheet.absoluteFill
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.play,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
              name: "play",
              size: 20,
              color: "white"
            })
          })]
        })]
      })
    });
  };
  var _default = exports.default = Thumbnail;
