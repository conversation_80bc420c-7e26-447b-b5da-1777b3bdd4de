  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _multiple = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _single = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: 'center'
    },
    margin: {
      marginHorizontal: 20
    }
  });
  var Attachments = function Attachments(_ref) {
    var canUploadFiles = _ref.canUploadFiles,
      maxFileCount = _ref.maxFileCount,
      maxFileSize = _ref.maxFileSize,
      theme = _ref.theme;
    var intl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)();
    var files = (0, _$$_REQUIRE(_dependencyMap[8]).useShareExtensionFiles)();
    var error = (0, _react.useMemo)(function () {
      if (!canUploadFiles) {
        return intl.formatMessage({
          id: 'share_extension.upload_disabled',
          defaultMessage: 'File uploads are disabled for the selected server'
        });
      }
      if (files.length > maxFileCount) {
        return intl.formatMessage({
          id: 'share_extension.count_limit',
          defaultMessage: 'You can only share {count, number} {count, plural, one {file} other {files}} on this server'
        }, {
          count: maxFileCount
        });
      }
      var maxResolutionError = false;
      var totalSize = files.reduce(function (total, file) {
        if (file.width && file.height && !maxResolutionError) {
          maxResolutionError = file.width * file.height > _$$_REQUIRE(_dependencyMap[9]).MAX_RESOLUTION;
        }
        return total + (file.size || 0);
      }, 0);
      if (totalSize > maxFileSize) {
        if (files.length > 1) {
          return intl.formatMessage({
            id: 'share_extension.file_limit.multiple',
            defaultMessage: 'Each file must be less than {size}'
          }, {
            size: (0, _$$_REQUIRE(_dependencyMap[10]).getFormattedFileSize)(maxFileSize)
          });
        }
        return intl.formatMessage({
          id: 'share_extension.file_limit.single',
          defaultMessage: 'File must be less than {size}'
        }, {
          size: (0, _$$_REQUIRE(_dependencyMap[10]).getFormattedFileSize)(maxFileSize)
        });
      }
      if (maxResolutionError) {
        return intl.formatMessage({
          id: 'share_extension.max_resolution',
          defaultMessage: 'Image exceeds maximum dimensions of 7680 x 4320 px'
        });
      }
      return undefined;
    }, [canUploadFiles, maxFileCount, maxFileSize, files, intl.locale]);
    var attachmentsContainerStyle = (0, _react.useMemo)(function () {
      return [styles.container, files.length === 1 && styles.margin];
    }, [files]);
    (0, _react.useEffect)(function () {
      (0, _$$_REQUIRE(_dependencyMap[8]).setShareExtensionGlobalError)(Boolean(error));
    }, [error]);
    var attachments;
    if (files.length === 1) {
      attachments = /*#__PURE__*/(0, _jsxRuntime.jsx)(_single.default, {
        file: files[0],
        maxFileSize: maxFileSize,
        theme: theme
      });
    } else {
      attachments = /*#__PURE__*/(0, _jsxRuntime.jsx)(_multiple.default, {
        files: files,
        maxFileSize: maxFileSize,
        theme: theme
      });
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: attachmentsContainerStyle,
        children: attachments
      }), Boolean(error) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_label.default, {
        text: error,
        theme: theme
      })]
    });
  };
  var _default = exports.default = Attachments;
