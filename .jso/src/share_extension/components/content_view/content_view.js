  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _attachments = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _link_preview = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _message = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _options = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyles = (0, _$$_REQUIRE(_dependencyMap[8]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1
      },
      content: {
        paddingTop: 20
      },
      divider: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[8]).changeOpacity)(theme.centerChannelColor, 0.08),
        height: 1,
        marginBottom: 8,
        marginHorizontal: 20,
        marginTop: 20
      }
    };
  });
  var ContentView = function ContentView(_ref) {
    var database = _ref.database,
      currentChannelId = _ref.currentChannelId,
      currentUserId = _ref.currentUserId,
      theme = _ref.theme;
    var _useShareExtensionSta = (0, _$$_REQUIRE(_dependencyMap[9]).useShareExtensionState)(),
      linkPreviewUrl = _useShareExtensionSta.linkPreviewUrl,
      files = _useShareExtensionSta.files,
      serverUrl = _useShareExtensionSta.serverUrl,
      channelId = _useShareExtensionSta.channelId;
    var styles = getStyles(theme);
    (0, _react.useEffect)(function () {
      (0, _$$_REQUIRE(_dependencyMap[9]).setShareExtensionUserAndChannelIds)(currentUserId, currentChannelId);
    }, [currentUserId, currentChannelId]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_$$_REQUIRE(_dependencyMap[10]).KeyboardAwareScrollView, {
        bounces: false,
        enableAutomaticScroll: true,
        enableOnAndroid: false,
        enableResetScrollToCoords: true,
        keyboardDismissMode: "on-drag",
        keyboardShouldPersistTaps: "handled",
        scrollToOverflowEnabled: true,
        contentContainerStyle: styles.content,
        children: [Boolean(linkPreviewUrl) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_link_preview.default, {
          theme: theme,
          url: linkPreviewUrl
        }), files.length > 0 && /*#__PURE__*/(0, _jsxRuntime.jsx)(_attachments.default, {
          database: database,
          theme: theme
        }), (files.length > 0 || Boolean(linkPreviewUrl)) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.divider
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_options.default, {
          channelId: channelId,
          database: database,
          serverUrl: serverUrl,
          theme: theme
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.divider
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_message.default, {
          theme: theme
        })]
      })
    });
  };
  var _default = exports.default = ContentView;
