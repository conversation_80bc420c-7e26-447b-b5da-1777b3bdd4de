  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyles = (0, _$$_REQUIRE(_dependencyMap[6]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        borderColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.16),
        borderWidth: 1,
        borderRadius: 4,
        flexDirection: 'row',
        maxHeight: 96,
        height: 96,
        marginHorizontal: 20,
        padding: 12
      },
      flex: {
        flex: 1
      },
      image: {
        alignItems: 'center',
        borderColor: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.16),
        borderRadius: 4,
        borderWidth: 1,
        height: 72,
        justifyContent: 'center',
        marginLeft: 10,
        width: 72
      },
      link: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.64),
        marginTop: 4
      }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 75, 'Regular')),
      title: Object.assign({
        color: theme.linkColor
      }, (0, _$$_REQUIRE(_dependencyMap[7]).typography)('Heading', 200, 'SemiBold'))
    };
  });
  var OpenGraphImage = function OpenGraphImage(_ref) {
    var style = _ref.style,
      theme = _ref.theme,
      url = _ref.url;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loading = _useState2[0],
      setLoading = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      error = _useState4[0],
      setError = _useState4[1];
    var onLoad = (0, _react.useCallback)(function () {
      setLoading(false);
    }, []);
    var onError = (0, _react.useCallback)(function () {
      setError(true);
      setLoading(false);
    }, []);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: style,
      children: [error && !loading && /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.16),
        name: "file-image-broken-outline-large",
        size: 24
      }), loading && /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        color: (0, _$$_REQUIRE(_dependencyMap[6]).changeOpacity)(theme.centerChannelColor, 0.16),
        size: "small"
      }), !error && /*#__PURE__*/(0, _jsxRuntime.jsx)(_$$_REQUIRE(_dependencyMap[8]).Image, {
        contentFit: "cover",
        source: {
          uri: url
        },
        style: {
          borderRadius: 4,
          height: 72,
          width: 72
        },
        onLoad: onLoad,
        onError: onError
      })]
    });
  };
  var LinkPreview = function LinkPreview(_ref2) {
    var theme = _ref2.theme,
      url = _ref2.url;
    var styles = getStyles(theme);
    var _useState5 = (0, _react.useState)(),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      data = _useState6[0],
      setData = _useState6[1];
    (0, _react.useEffect)(function () {
      if (url) {
        (0, _$$_REQUIRE(_dependencyMap[9]).fetchOpenGraph)(url).then(setData);
      }
    }, [url]);
    if (!data || data.error) {
      return null;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.flex,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          numberOfLines: 2,
          style: styles.title,
          children: url
        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
          numberOfLines: 1,
          style: styles.link,
          children: data.link
        })]
      }), Boolean(data.imageURL) && /*#__PURE__*/(0, _jsxRuntime.jsx)(OpenGraphImage, {
        style: styles.image,
        theme: theme,
        url: data.imageURL
      })]
    });
  };
  var _default = exports.default = LinkPreview;
