  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _floating_text_input_label = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyles = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        marginHorizontal: 20
      },
      input: Object.assign({
        color: theme.centerChannelColor,
        minHeight: 154
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200, 'Regular')),
      textInputContainer: {
        marginTop: 20,
        alignSelf: 'center',
        minHeight: 154,
        height: undefined
      }
    };
  });
  var Message = function Message(_ref) {
    var theme = _ref.theme;
    var intl = (0, _$$_REQUIRE(_dependencyMap[7]).useIntl)();
    var styles = getStyles(theme);
    var message = (0, _$$_REQUIRE(_dependencyMap[8]).useShareExtensionMessage)();
    var label = (0, _react.useMemo)(function () {
      return intl.formatMessage({
        id: 'share_extension.message',
        defaultMessage: 'Enter a message (optional)'
      });
    }, [intl.locale]);
    var onChangeText = (0, _react.useCallback)((0, _$$_REQUIRE(_dependencyMap[9]).debounce)(function (text) {
      (0, _$$_REQUIRE(_dependencyMap[8]).setShareExtensionMessage)(text);
    }, 250), []);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_floating_text_input_label.default, {
        allowFontScaling: false,
        autoCapitalize: "none",
        autoCorrect: false,
        autoFocus: false,
        containerStyle: styles.textInputContainer,
        keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[5]).getKeyboardAppearanceFromTheme)(theme),
        label: label,
        multiline: true,
        onChangeText: onChangeText,
        returnKeyType: "default",
        textAlignVertical: "top",
        textInputStyle: styles.input,
        theme: theme,
        underlineColorAndroid: "transparent",
        defaultValue: message || ''
      })
    });
  };
  var _default = exports.default = Message;
