  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyleSheet = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        alignItems: 'center',
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.04),
        borderRadius: 8,
        flexDirection: 'row',
        height: 72,
        marginBottom: 12
      },
      details: {
        marginLeft: 14,
        flex: 1
      },
      name: Object.assign({
        color: theme.centerChannelColor,
        flexShrink: 1
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200, 'SemiBold')),
      nameView: {
        flexDirection: 'row',
        marginRight: 7
      },
      row: {
        flexDirection: 'row',
        alignItems: 'center',
        marginHorizontal: 18
      },
      url: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.72),
        marginRight: 7
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 75, 'Regular'))
    };
  });
  var ServerItem = function ServerItem(_ref) {
    var server = _ref.server,
      theme = _ref.theme;
    var navigation = (0, _$$_REQUIRE(_dependencyMap[7]).useNavigation)();
    var styles = getStyleSheet(theme);
    var onServerPressed = (0, _react.useCallback)(function () {
      (0, _$$_REQUIRE(_dependencyMap[8]).setShareExtensionServerUrl)(server.url);
      navigation.goBack();
    }, [server]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: onServerPressed,
      style: styles.container,
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.row,
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          size: 36,
          name: "server-variant",
          color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.56)
        }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.details,
          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.nameView,
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
              numberOfLines: 1,
              ellipsizeMode: "tail",
              style: styles.name,
              children: server.displayName
            })
          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
            numberOfLines: 1,
            ellipsizeMode: "tail",
            style: styles.url,
            children: (0, _$$_REQUIRE(_dependencyMap[9]).removeProtocol)((0, _$$_REQUIRE(_dependencyMap[9]).stripTrailingSlashes)(server.url))
          })]
        })]
      })
    });
  };
  var _default = exports.default = ServerItem;
