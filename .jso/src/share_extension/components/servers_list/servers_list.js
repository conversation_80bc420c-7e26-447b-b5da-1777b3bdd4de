  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _server_item = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      marginTop: 16,
      marginHorizontal: 20
    },
    contentContainer: {
      marginVertical: 4
    }
  });
  var keyExtractor = function keyExtractor(item) {
    return item.url;
  };
  var ServersList = function ServersList(_ref) {
    var servers = _ref.servers,
      theme = _ref.theme;
    var intl = (0, _$$_REQUIRE(_dependencyMap[5]).useIntl)();
    var data = (0, _react.useMemo)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[6]).sortServersByDisplayName)(servers, intl);
    }, [intl.locale, servers]);
    var renderServer = (0, _react.useCallback)(function (_ref2) {
      var item = _ref2.item;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_server_item.default, {
        server: item,
        theme: theme
      });
    }, [theme]);
    if ((data == null ? undefined : data.length) <= 0) return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {});
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: data,
        renderItem: renderServer,
        keyExtractor: keyExtractor,
        contentContainerStyle: styles.contentContainer
      })
    });
  };
  var _default = exports.default = ServersList;
