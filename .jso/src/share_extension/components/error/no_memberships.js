  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _formatted_text = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyles = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 32
      },
      title: Object.assign({
        color: theme.centerChannelColor,
        marginBottom: 8
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 400)),
      description: Object.assign({
        color: (0, _$$_REQUIRE(_dependencyMap[5]).changeOpacity)(theme.centerChannelColor, 0.72),
        textAlign: 'center'
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 200))
    };
  });
  var NoMemberships = function NoMemberships(_ref) {
    var theme = _ref.theme;
    var styles = getStyles(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        id: "extension.no_memberships.title",
        defaultMessage: "Not a member of any team yet",
        style: styles.title
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_formatted_text.default, {
        id: "extension.no_memberships.description",
        defaultMessage: "To share content, you'll need to be a member of a team on a Mattermost server.",
        style: styles.description
      })]
    });
  };
  var _default = exports.default = NoMemberships;
