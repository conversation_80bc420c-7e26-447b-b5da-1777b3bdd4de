  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyles = (0, _$$_REQUIRE(_dependencyMap[5]).makeStyleSheetFromTheme)(function (theme) {
    return {
      icon: {
        color: theme.errorTextColor
      },
      message: Object.assign({
        color: theme.errorTextColor,
        marginLeft: 7,
        top: -2
      }, (0, _$$_REQUIRE(_dependencyMap[6]).typography)('Heading', 75)),
      row: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginHorizontal: 20,
        marginTop: 12
      }
    };
  });
  var ErrorLabel = function ErrorLabel(_ref) {
    var style = _ref.style,
      text = _ref.text,
      theme = _ref.theme;
    var styles = getStyles(theme);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.row, style],
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
        name: "alert-outline",
        size: 12,
        style: styles.icon
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.message,
        children: text
      })]
    });
  };
  var _default = exports.default = ErrorLabel;
