  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _compass_icon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var hitSlop = {
    top: 10,
    left: 10,
    right: 10,
    bottom: 10
  };
  var styles = _reactNative.StyleSheet.create({
    left: {
      marginLeft: 10
    }
  });
  var CloseHeaderButton = function CloseHeaderButton(_ref) {
    var theme = _ref.theme;
    var _useShareExtensionSta = (0, _$$_REQUIRE(_dependencyMap[5]).useShareExtensionState)(),
      closeExtension = _useShareExtensionSta.closeExtension;
    var onPress = (0, _react.useCallback)(function () {
      closeExtension(null);
    }, [closeExtension]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: onPress,
      hitSlop: hitSlop,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.left,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_compass_icon.default, {
          name: "close",
          color: theme.sidebarHeaderTextColor,
          size: 24
        })
      })
    });
  };
  var _default = exports.default = CloseHeaderButton;
