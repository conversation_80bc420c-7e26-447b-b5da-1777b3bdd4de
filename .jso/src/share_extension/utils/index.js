  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.imageDimensions = imageDimensions;
  exports.toFileInfo = toFileInfo;
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  function toFileInfo(f) {
    return {
      post_id: '',
      user_id: '',
      extension: f.extension,
      mime_type: f.type,
      has_preview_image: (f.width || 0) > 0,
      height: f.height || 0,
      width: f.width || 0,
      name: f.filename || '',
      size: f.size || 0,
      uri: f.value
    };
  }
  function imageDimensions(imgHeight, imgWidth, maxHeight, viewportWidth) {
    if (!imgHeight || !imgWidth) {
      return {
        height: 0,
        width: 0
      };
    }
    var widthRatio = imgWidth / imgHeight;
    var heightRatio = imgWidth / imgHeight;
    var height = imgHeight;
    var width = imgWidth;
    if (imgWidth >= viewportWidth) {
      width = viewportWidth;
      height = width * widthRatio;
    }
    if (height > maxHeight) {
      height = maxHeight;
      width = height * heightRatio;
    }
    return {
      height: height,
      width: width
    };
  }
