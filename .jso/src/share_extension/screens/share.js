  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.errorScreenMessages = exports.default = undefined;
  var _react2 = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _manager = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _content_view = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _no_memberships = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _no_servers = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _close_header_button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _post_button = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var errorScreenMessages = exports.errorScreenMessages = (0, _$$_REQUIRE(_dependencyMap[10]).defineMessages)({
    label: {
      id: 'share_extension.error_screen.label',
      defaultMessage: 'An error ocurred'
    },
    description: {
      id: 'share_extension.error_screen.description',
      defaultMessage: 'There was an error when attempting to share the content to {applicationName}.'
    },
    reason: {
      id: 'share_extension.error_screen.reason',
      defaultMessage: 'Reason: {reason}'
    }
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1
    }
  });
  var ShareScreen = function ShareScreen(_ref) {
    var hasChannelMemberships = _ref.hasChannelMemberships,
      initialServerUrl = _ref.initialServerUrl,
      files = _ref.files,
      linkPreviewUrl = _ref.linkPreviewUrl,
      message = _ref.message,
      theme = _ref.theme;
    var navigator = (0, _$$_REQUIRE(_dependencyMap[11]).useNavigation)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[10]).useIntl)();
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[12]).useShareExtensionServerUrl)();
    var hasServers = (0, _react2.useMemo)(function () {
      return Object.keys(_manager.default.serverDatabases).length > 0;
    }, []);
    var serverDb = (0, _react2.useMemo)(function () {
      try {
        if (!serverUrl) {
          return undefined;
        }
        var _DatabaseManager$getS = _manager.default.getServerDatabaseAndOperator(serverUrl),
          database = _DatabaseManager$getS.database;
        return database;
      } catch (_unused) {
        return undefined;
      }
    }, [serverUrl]);
    (0, _react2.useEffect)(function () {
      navigator.setOptions({
        title: intl.formatMessage({
          id: 'share_extension.share_screen.title',
          defaultMessage: 'Share to {applicationName}'
        }, {
          applicationName: _$$_REQUIRE(_dependencyMap[13]).applicationName
        }),
        headerTitleStyle: {
          fontFamily: "IBMPlexSansArabic-Light"
        }
      });
    }, [intl.locale]);
    (0, _react2.useEffect)(function () {
      (0, _$$_REQUIRE(_dependencyMap[12]).setShareExtensionState)({
        files: files,
        linkPreviewUrl: linkPreviewUrl,
        message: message,
        serverUrl: initialServerUrl
      });
      navigator.setOptions({
        headerLeft: function headerLeft() {
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_close_header_button.default, {
            theme: theme
          });
        },
        headerRight: function headerRight() {
          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_post_button.default, {
            theme: theme
          });
        }
      });
    }, []);
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [!hasServers && /*#__PURE__*/(0, _jsxRuntime.jsx)(_no_servers.default, {
        theme: theme
      }), hasServers && !hasChannelMemberships && /*#__PURE__*/(0, _jsxRuntime.jsx)(_no_memberships.default, {
        theme: theme
      }), hasServers && hasChannelMemberships && Boolean(serverDb) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_content_view.default, {
        database: serverDb,
        theme: theme
      })]
    });
  };
  var enhanced = (0, _$$_REQUIRE(_dependencyMap[14]).withObservables)([], function () {
    return {
      initialServerUrl: (0, _$$_REQUIRE(_dependencyMap[15]).from)((0, _$$_REQUIRE(_dependencyMap[16]).getActiveServerUrl)()),
      hasChannelMemberships: (0, _$$_REQUIRE(_dependencyMap[15]).from)((0, _$$_REQUIRE(_dependencyMap[17]).hasChannels)())
    };
  });
  var _default = exports.default = enhanced(ShareScreen);
