  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _search = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _manager = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _recent_channels = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _search_channels = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var getStyles = (0, _$$_REQUIRE(_dependencyMap[9]).makeStyleSheetFromTheme)(function (theme) {
    return {
      container: {
        flex: 1,
        marginHorizontal: 20,
        marginTop: 20
      },
      inputContainerStyle: {
        backgroundColor: (0, _$$_REQUIRE(_dependencyMap[9]).changeOpacity)(theme.centerChannelColor, 0.12)
      },
      inputStyle: {
        color: theme.centerChannelColor
      },
      listContainer: {
        flex: 1,
        marginTop: 8
      }
    };
  });
  var Channels = function Channels(_ref) {
    var theme = _ref.theme;
    var serverUrl = (0, _$$_REQUIRE(_dependencyMap[10]).useShareExtensionServerUrl)();
    var styles = getStyles(theme);
    var _useState = (0, _react.useState)(''),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      term = _useState2[0],
      setTerm = _useState2[1];
    var color = (0, _react.useMemo)(function () {
      return (0, _$$_REQUIRE(_dependencyMap[9]).changeOpacity)(theme.centerChannelColor, 0.72);
    }, [theme]);
    var navigator = (0, _$$_REQUIRE(_dependencyMap[11]).useNavigation)();
    var intl = (0, _$$_REQUIRE(_dependencyMap[12]).useIntl)();
    (0, _react.useEffect)(function () {
      navigator.setOptions({
        title: intl.formatMessage({
          id: 'share_extension.channels_screen.title',
          defaultMessage: 'Select channel'
        })
      });
    }, [intl.locale]);
    var cancelButtonProps = (0, _react.useMemo)(function () {
      return {
        color: color,
        buttonTextStyle: Object.assign({}, (0, _$$_REQUIRE(_dependencyMap[13]).typography)('Heading', 100))
      };
    }, [color]);
    var database = (0, _react.useMemo)(function () {
      try {
        var server = _manager.default.getServerDatabaseAndOperator(serverUrl || '');
        return server.database;
      } catch (_unused) {
        return undefined;
      }
    }, [serverUrl]);
    if (!database) {
      return null;
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_search.default, {
        autoCapitalize: "none",
        autoFocus: true,
        cancelButtonProps: cancelButtonProps,
        clearIconColor: color,
        inputContainerStyle: styles.inputContainerStyle,
        inputStyle: styles.inputStyle,
        keyboardAppearance: (0, _$$_REQUIRE(_dependencyMap[9]).getKeyboardAppearanceFromTheme)(theme),
        onChangeText: setTerm,
        placeholderTextColor: color,
        searchIconColor: color,
        selectionColor: color,
        value: term
      }), term === '' && /*#__PURE__*/(0, _jsxRuntime.jsx)(_recent_channels.default, {
        database: database,
        theme: theme
      }), Boolean(term) && /*#__PURE__*/(0, _jsxRuntime.jsx)(_search_channels.default, {
        database: database,
        term: term,
        theme: theme
      })]
    });
  };
  var _default = exports.default = Channels;
