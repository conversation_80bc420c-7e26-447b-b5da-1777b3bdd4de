  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useShareExtensionState = exports.useShareExtensionServerUrl = exports.useShareExtensionMessage = exports.useShareExtensionFiles = exports.setShareExtensionUserId = exports.setShareExtensionUserAndChannelIds = exports.setShareExtensionState = exports.setShareExtensionServerUrl = exports.setShareExtensionMessage = exports.setShareExtensionGlobalError = exports.setShareExtensionChannelId = exports.removeShareExtensionFile = exports.getShareExtensionState = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _rnshare = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _$$_REQUIRE(_dependencyMap[4]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var defaultState = {
    closeExtension: _rnshare.default.close,
    channelId: undefined,
    files: [],
    globalError: false,
    linkPreviewUrl: undefined,
    message: undefined,
    serverUrl: undefined,
    userId: undefined
  };
  var subject = new (_$$_REQUIRE(_dependencyMap[5]).BehaviorSubject)(defaultState);
  var getShareExtensionState = exports.getShareExtensionState = function getShareExtensionState() {
    return subject.value;
  };
  var setShareExtensionState = exports.setShareExtensionState = function setShareExtensionState(state) {
    var prevState = getShareExtensionState();
    subject.next(Object.assign({}, prevState, state));
  };
  var setShareExtensionGlobalError = exports.setShareExtensionGlobalError = function setShareExtensionGlobalError(globalError) {
    var state = getShareExtensionState();
    var newState = Object.assign({}, state, {
      globalError: globalError
    });
    setShareExtensionState(newState);
  };
  var setShareExtensionMessage = exports.setShareExtensionMessage = function setShareExtensionMessage(message) {
    var state = getShareExtensionState();
    var newState = Object.assign({}, state, {
      message: message
    });
    setShareExtensionState(newState);
  };
  var setShareExtensionServerUrl = exports.setShareExtensionServerUrl = function setShareExtensionServerUrl(serverUrl) {
    var state = getShareExtensionState();
    var newState = Object.assign({}, state, {
      serverUrl: serverUrl
    });
    setShareExtensionState(newState);
  };
  var setShareExtensionUserAndChannelIds = exports.setShareExtensionUserAndChannelIds = function setShareExtensionUserAndChannelIds(userId, channelId) {
    var state = getShareExtensionState();
    var newState = Object.assign({}, state, {
      channelId: channelId,
      userId: userId
    });
    setShareExtensionState(newState);
  };
  var setShareExtensionUserId = exports.setShareExtensionUserId = function setShareExtensionUserId(userId) {
    var state = getShareExtensionState();
    var newState = Object.assign({}, state, {
      userId: userId
    });
    setShareExtensionState(newState);
  };
  var setShareExtensionChannelId = exports.setShareExtensionChannelId = function setShareExtensionChannelId(channelId) {
    var state = getShareExtensionState();
    var newState = Object.assign({}, state, {
      channelId: channelId
    });
    setShareExtensionState(newState);
  };
  var removeShareExtensionFile = exports.removeShareExtensionFile = function removeShareExtensionFile(file) {
    var state = getShareExtensionState();
    var files = (0, _toConsumableArray2.default)(state.files);
    var index = files.findIndex(function (f) {
      return f === file;
    });
    if (index > -1) {
      files.splice(index, 1);
      var newState = Object.assign({}, state, {
        files: files
      });
      setShareExtensionState(newState);
    }
  };
  var useShareExtensionState = exports.useShareExtensionState = function useShareExtensionState() {
    var _useState = (0, _react.useState)(defaultState),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      state = _useState2[0],
      setState = _useState2[1];
    (0, _react.useEffect)(function () {
      var sub = subject.subscribe(setState);
      return function () {
        return sub.unsubscribe();
      };
    }, []);
    return state;
  };
  var useShareExtensionServerUrl = exports.useShareExtensionServerUrl = function useShareExtensionServerUrl() {
    var state = useShareExtensionState();
    var _useState3 = (0, _react.useState)(state.serverUrl),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      serverUrl = _useState4[0],
      setServerUrl = _useState4[1];
    (0, _react.useEffect)(function () {
      setServerUrl(state.serverUrl);
    }, [state.serverUrl]);
    return serverUrl;
  };
  var useShareExtensionMessage = exports.useShareExtensionMessage = function useShareExtensionMessage() {
    var state = useShareExtensionState();
    var _useState5 = (0, _react.useState)(state.message),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      message = _useState6[0],
      setMessage = _useState6[1];
    (0, _react.useEffect)(function () {
      setMessage(state.message);
    }, [state.message]);
    return message;
  };
  var useShareExtensionFiles = exports.useShareExtensionFiles = function useShareExtensionFiles() {
    var state = useShareExtensionState();
    var _useState7 = (0, _react.useState)(state.files),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      files = _useState8[0],
      setFiles = _useState8[1];
    (0, _react.useEffect)(function () {
      setFiles(state.files);
    }, [state.files]);
    return files;
  };
