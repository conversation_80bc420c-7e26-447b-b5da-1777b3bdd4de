  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.queryHasChannels = exports.observeServerHasChannels = exports.hasChannels = exports.getServerHasChannels = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _manager = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var CHANNEL = _$$_REQUIRE(_dependencyMap[3]).MM_TABLES.SERVER.CHANNEL;
  var queryHasChannels = exports.queryHasChannels = function queryHasChannels(serverUrl) {
    try {
      var _DatabaseManager$getS = _manager.default.getServerDatabaseAndOperator(serverUrl),
        database = _DatabaseManager$getS.database;
      return database.get(CHANNEL).query(_$$_REQUIRE(_dependencyMap[4]).Q.unsafeSqlQuery('SELECT DISTINCT c.* FROM Channel c \
            INNER JOIN MyChannel my ON c.id=my.id AND c.delete_at = 0 \
            INNER JOIN Team t ON c.team_id=t.id'));
    } catch (e) {
      return undefined;
    }
  };
  var getServerHasChannels = exports.getServerHasChannels = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (serverUrl) {
      var _queryHasChannels, _channelsCount$length;
      var channelsCount = yield (_queryHasChannels = queryHasChannels(serverUrl)) == null ? undefined : _queryHasChannels.fetch();
      return ((_channelsCount$length = channelsCount == null ? undefined : channelsCount.length) != null ? _channelsCount$length : 0) > 0;
    });
    return function getServerHasChannels(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  var observeServerHasChannels = exports.observeServerHasChannels = function observeServerHasChannels(serverUrl) {
    var _queryHasChannels2;
    return ((_queryHasChannels2 = queryHasChannels(serverUrl)) == null ? undefined : _queryHasChannels2.observe().pipe((0, _$$_REQUIRE(_dependencyMap[5]).switchMap)(function (channels) {
      var _ref2;
      return (0, _$$_REQUIRE(_dependencyMap[5]).of)((_ref2 = (channels == null ? undefined : channels.length) > 0) != null ? _ref2 : false);
    }))) || (0, _$$_REQUIRE(_dependencyMap[5]).of)(false);
  };
  var hasChannels = exports.hasChannels = /*#__PURE__*/function () {
    var _ref3 = (0, _asyncToGenerator2.default)(function* () {
      try {
        var servers = yield (0, _$$_REQUIRE(_dependencyMap[6]).getAllServers)();
        var activeSrvers = servers.filter(function (s) {
          return s.identifier && s.lastActiveAt;
        });
        var promises = [];
        for (var active of activeSrvers) {
          promises.push(getServerHasChannels(active.url));
        }
        var result = yield Promise.all(promises);
        return result.some(function (r) {
          return r;
        });
      } catch (_unused) {
        return false;
      }
    });
    return function hasChannels() {
      return _ref3.apply(this, arguments);
    };
  }();
