  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.__INTERNAL_VIEW_CONFIG = exports.Commands = undefined;
  var _codegenNativeCommands = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _codegenNativeComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2])); // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var nativeComponentName = 'MattermostKeyboardTrackerView';
  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {
    uiViewClassName: 'MattermostKeyboardTrackerView',
    bubblingEventTypes: {
      topKeyboardWillShow: {
        phasedRegistrationNames: {
          captured: 'onKeyboardWillShowCapture',
          bubbled: 'onKeyboardWillShow'
        }
      }
    },
    validAttributes: Object.assign({
      scrollBehavior: true,
      revealKeyboardInteractive: true,
      manageScrollView: true,
      requiresSameParentToManageScrollView: true,
      addBottomView: true,
      scrollToFocusedInput: true,
      allowHitsOutsideBounds: true,
      normalList: true,
      viewInitialOffsetY: true,
      scrollViewNativeID: true,
      accessoriesContainerID: true,
      backgroundColor: {
        process: _$$_REQUIRE(_dependencyMap[3]).default
      }
    }, _$$_REQUIRE(_dependencyMap[4]).ConditionallyIgnoredEventHandlers({
      onKeyboardWillShow: true
    }))
  };
  var _default = exports.default = _$$_REQUIRE(_dependencyMap[5]).get(nativeComponentName, function () {
    return __INTERNAL_VIEW_CONFIG;
  });
  var Commands = exports.Commands = {
    resetScrollView: function resetScrollView(ref, scrollViewNativeID) {
      _$$_REQUIRE(_dependencyMap[6]).dispatchCommand(ref, "resetScrollView", [scrollViewNativeID]);
    },
    pauseTracking: function pauseTracking(ref, scrollViewNativeID) {
      _$$_REQUIRE(_dependencyMap[6]).dispatchCommand(ref, "pauseTracking", [scrollViewNativeID]);
    },
    resumeTracking: function resumeTracking(ref, scrollViewNativeID) {
      _$$_REQUIRE(_dependencyMap[6]).dispatchCommand(ref, "resumeTracking", [scrollViewNativeID]);
    },
    scrollToStart: function scrollToStart(ref) {
      _$$_REQUIRE(_dependencyMap[6]).dispatchCommand(ref, "scrollToStart", []);
    }
  };
