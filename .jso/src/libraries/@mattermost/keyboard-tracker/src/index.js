  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _exportNames = {};
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _MattermostKeyboardTrackerViewNativeComponent = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  Object.keys(_MattermostKeyboardTrackerViewNativeComponent).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
    if (key in exports && exports[key] === _MattermostKeyboardTrackerViewNativeComponent[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _MattermostKeyboardTrackerViewNativeComponent[key];
      }
    });
  });
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
  function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var KeyboardTrackingView = (0, _react.forwardRef)(function (props, ref) {
    var viewRef = (0, _react.useRef)(null);
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        resetScrollView: function resetScrollView(scrollViewNativeID) {
          if (viewRef.current) {
            _MattermostKeyboardTrackerViewNativeComponent.Commands.resumeTracking(viewRef.current, scrollViewNativeID);
          }
        },
        pauseTracking: function pauseTracking(scrollViewNativeID) {
          if (viewRef.current) {
            _MattermostKeyboardTrackerViewNativeComponent.Commands.pauseTracking(viewRef.current, scrollViewNativeID);
          }
        },
        resumeTracking: function resumeTracking(scrollViewNativeID) {
          if (viewRef.current) {
            _MattermostKeyboardTrackerViewNativeComponent.Commands.resumeTracking(viewRef.current, scrollViewNativeID);
          }
        },
        scrollToStart: function scrollToStart() {
          if (viewRef.current) {
            _MattermostKeyboardTrackerViewNativeComponent.Commands.scrollToStart(viewRef.current);
          }
        }
      };
    });
    var _onKeyboardShow = (0, _react.useCallback)(function (e) {
      _reactNative.DeviceEventEmitter.emit('MattermostKeyboardTrackerView', e);
      props.onKeyboardWillShow == null ? undefined : props.onKeyboardWillShow(e);
    }, [props.onKeyboardWillShow]);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_MattermostKeyboardTrackerViewNativeComponent.default, Object.assign({}, props, {
      onKeyboardWillShow: _onKeyboardShow,
      ref: viewRef
    }));
  });
  KeyboardTrackingView.displayName = 'KeyboardTrackingView';
  var _default = exports.default = KeyboardTrackingView;
