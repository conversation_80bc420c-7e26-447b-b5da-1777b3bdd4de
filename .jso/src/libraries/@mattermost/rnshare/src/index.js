  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var LINKING_ERROR = 'The package \'mattermost-share\' doesn\'t seem to be linked. Make sure: \n\n' + _reactNative.Platform.select({
    ios: "- You have run 'pod install'\n",
    default: ''
  }) + '- You rebuilt the app after installing the package\n' + '- You are not using Expo Go\n';

  // @ts-expect-error global
  var isTurboModuleEnabled = global.__turboModuleProxy != null;
  var MattermostShareModule = isTurboModuleEnabled ? _$$_REQUIRE(_dependencyMap[1]).default : _reactNative.NativeModules.MattermostShare;
  var MattermostShare = MattermostShareModule || new Proxy({}, {
    get: function get() {
      throw new Error(LINKING_ERROR);
    }
  });
  var _default = exports.default = MattermostShare;
