  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useHardwareKeyboardEvents = useHardwareKeyboardEvents;
  var _react = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  // Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
  // See LICENSE.txt for license information.

  var LINKING_ERROR = 'The package \'mattermost-hardware-keyboard\' doesn\'t seem to be linked. Make sure: \n\n' + _reactNative.Platform.select({
    ios: "- You have run 'pod install'\n",
    default: ''
  }) + '- You rebuilt the app after installing the package\n' + '- You are not using Expo Go\n';

  // @ts-expect-error global
  var isTurboModuleEnabled = global.__turboModuleProxy != null;
  var MattermostHardwareKeyboardModule = isTurboModuleEnabled ? _$$_REQUIRE(_dependencyMap[2]).default : _reactNative.NativeModules.MattermostHardwareKeyboard;
  var MattermostHardwareKeyboard = MattermostHardwareKeyboardModule || new Proxy({}, {
    get: function get() {
      throw new Error(LINKING_ERROR);
    }
  });

  // Only create NativeEventEmitter if the module is properly available and has required methods
  var emitter = MattermostHardwareKeyboardModule && typeof MattermostHardwareKeyboardModule.addListener === 'function' && typeof MattermostHardwareKeyboardModule.removeListeners === 'function' ? new _reactNative.NativeEventEmitter(MattermostHardwareKeyboard) : null;
  function useHardwareKeyboardEvents(events) {
    var handleEvent = (0, _react.useCallback)(function (e) {
      switch (e.action) {
        case 'enter':
          events.onEnterPressed == null ? undefined : events.onEnterPressed();
          break;
        case 'shift-enter':
          events.onShiftEnterPressed == null ? undefined : events.onShiftEnterPressed();
          break;
        case 'find-channels':
          events.onFindChannels == null ? undefined : events.onFindChannels();
          break;
      }
    }, [events]);
    (0, _react.useEffect)(function () {
      if (!emitter) {
        return;
      }
      var listener = emitter.addListener('mmHardwareKeyboardEvent', handleEvent);
      return function () {
        return listener.remove();
      };
    }, [handleEvent]);
  }
