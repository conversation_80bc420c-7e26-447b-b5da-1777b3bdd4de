# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# ================================
# REACT NATIVE OPTIMIZATION RULES
# ================================

# Keep attributes for debugging and reflection
-keepattributes InnerClasses
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keepattributes EnclosingMethod

# React Native specific rules
-keep class com.facebook.react.** { *; }
-keep class com.facebook.hermes.** { *; }
-keep class com.facebook.jni.** { *; }

# Keep React Native bridge classes
-keep class com.facebook.react.bridge.** { *; }
-keep class com.facebook.react.uimanager.** { *; }
-keep class com.facebook.react.views.** { *; }

# Keep native modules
-keep class * extends com.facebook.react.bridge.ReactContextBaseJavaModule { *; }
-keep class * extends com.facebook.react.bridge.BaseJavaModule { *; }

# Keep JavaScript interface methods
-keepclassmembers class * {
    @com.facebook.react.bridge.ReactMethod <methods>;
}

# Keep Hermes related classes
-keep class com.facebook.hermes.reactexecutor.** { *; }

# ================================
# THIRD PARTY LIBRARIES
# ================================

# JWT Token library
-keep class io.jsonwebtoken.** { *; }
-keepnames class io.jsonwebtoken.* { *; }
-keepnames interface io.jsonwebtoken.* { *; }

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }

# Retrofit (if used)
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# Gson (if used)
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# ================================
# MATTERMOST SPECIFIC RULES
# ================================

# Keep Mattermost specific classes
-keep class com.mattermost.** { *; }

# Keep WebRTC classes (for Jitsi/calls functionality)
-keep class org.webrtc.** { *; }
-keep class org.jitsi.** { *; }

# Keep notification related classes
-keep class com.wix.reactnativenotifications.** { *; }

# ================================
# PERFORMANCE OPTIMIZATIONS
# ================================

# Enable aggressive optimizations
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Remove debug code
-assumenosideeffects class * {
    boolean isDebug();
    boolean isDebugEnabled();
}

# ================================
# SIZE REDUCTION RULES
# ================================

# Remove unused classes and methods (but keep essential ones)
-dontwarn **
# Comment out ignorewarnings to catch critical issues
# -ignorewarnings

# Keep only essential annotations
-keepattributes RuntimeVisibleAnnotations
-keepattributes RuntimeInvisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations
-keepattributes RuntimeInvisibleParameterAnnotations

# Remove unused resources (handled by resource shrinking)
# This works in conjunction with shrinkResources true in build.gradle

# ================================
# CRITICAL CRASH PREVENTION RULES
# ================================

# Keep MainApplication and essential classes
-keep class com.mattermost.rnbeta.MainApplication { *; }
-keep class com.mattermost.rnbeta.** { *; }

# Keep React Native essential classes
-keep class com.facebook.react.** { *; }
-keep class com.facebook.hermes.** { *; }
-keep class com.facebook.jni.** { *; }

# Keep JSI and Hermes classes
-keep class com.facebook.react.bridge.** { *; }
-keep class com.facebook.react.uimanager.** { *; }

# Keep Navigation classes
-keep class com.reactnativenavigation.** { *; }

# Keep Expo modules
-keep class expo.modules.** { *; }

# Keep WatermelonDB JSI
-keep class com.nozbe.watermelondb.jsi.** { *; }

# Keep error handling classes
-keep class * extends java.lang.Exception { *; }

# Keep reflection-based classes
-keepclassmembers class * {
    @com.facebook.react.bridge.ReactMethod <methods>;
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}
