apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */

 react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")
    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]
    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    entryFile = file("../../index.ts")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []
    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   Optimized Hermes flags for smaller bundle size and better performance
    hermesFlags = ["-O", "-output-source-map", "-w"]
}

apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"

if (System.getenv("SENTRY_ENABLED") == "true") {
    project.ext.sentryCli = [
        logLevel: "error",
        flavorAware: false
    ]

    apply from: "../../node_modules/@sentry/react-native/sentry.gradle"
}

/**
 * Set this to true to create four separate APKs instead of one,
 * one for each native architecture. This is useful if you don't
 * use App Bundles (https://developer.android.com/guide/app-bundle/)
 * and want to have separate APKs to upload to the Play Store
 *
 * For universal APK generation, we set this to false and enable universalApk below
 */
def enableSeparateBuildPerCPUArchitecture = project.hasProperty('separateApk') ? project.property('separateApk').toBoolean() : false

/**
 * Set this to true to Run Proguard/R8 on Release builds to minify the Java bytecode.
 * This significantly reduces APK size and improves performance.
 */
def enableProguardInReleaseBuilds = true

/**
 * Enable resource shrinking to remove unused resources from the APK
 */
def enableResourceShrinking = true

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc-intl:+'

/**
 * Private function to get the list of Native Architectures you want to build.
 * This reads the value from reactNativeArchitectures in your gradle.properties
 * file and works together with the --active-arch-only flag of react-native run-android.
 */
def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdkVersion rootProject.ext.compileSdkVersion
    namespace "com.mattermost.rnbeta.fack"

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    // Optimize compilation for size and performance
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // Bundle configuration for size optimization
    bundle {
        language {
            // Disable unused language resources
            enableSplit = true
        }
        density {
            // Enable density splits for smaller downloads
            enableSplit = true
        }
        abi {
            // Enable ABI splits for smaller downloads
            enableSplit = true
        }
    }

    // Packaging options to exclude unnecessary files
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/*.kotlin_module'
    }

    defaultConfig {
        applicationId "com.mattermost.rnbeta"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 555
        versionName "2.20.0"
        testBuildType System.getProperty('testBuildType', 'debug')
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
    }

    signingConfigs {
        release {
            if (project.hasProperty('MATTERMOST_RELEASE_STORE_FILE')) {
                storeFile file(MATTERMOST_RELEASE_STORE_FILE)
                storePassword MATTERMOST_RELEASE_PASSWORD
                keyAlias MATTERMOST_RELEASE_KEY_ALIAS
                keyPassword MATTERMOST_RELEASE_PASSWORD
            }
        }
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }
    splits {
        abi {
            reset()
            enable enableSeparateBuildPerCPUArchitecture
            // Always generate a universal APK for maximum compatibility
            universalApk true
            include (*reactNativeArchitectures())
        }
    }
    buildTypes {
        def useReleaseKey = project.hasProperty('MATTERMOST_RELEASE_STORE_FILE')
        release {
            // Enable R8/ProGuard for code minification and obfuscation
            minifyEnabled enableProguardInReleaseBuilds
            // Temporarily disable resource shrinking to prevent crashes
            shrinkResources false
            // Use standard ProGuard configuration (not optimize) for stability
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            proguardFile "${rootProject.projectDir}/../node_modules/detox/android/detox/proguard-rules-app.pro"

            // Additional optimizations for size and performance
            zipAlignEnabled true
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            pseudoLocalesEnabled false

            if (useReleaseKey) {
                signingConfig signingConfigs.release
            } else {
                signingConfig signingConfigs.debug
            }
        }
        debug {
            if (useReleaseKey) {
                signingConfig signingConfigs.release
            } else {
                signingConfig signingConfigs.debug
            }
        }
        unsigned.initWith(buildTypes.release)
        unsigned {
            signingConfig null
            matchingFallbacks = ['release']
        }
    }

    // applicationVariants are e.g. debug, release
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            // For each separate APK per architecture, set a unique version code as described here:
            // http://tools.android.com/tech-docs/new-build-system/user-guide/apk-splits
            def versionCodes = ["armeabi-v7a":1, "x86":2, "arm64-v8a": 3, "x86_64": 4]
            def abi = output.filters[0]
            if (abi != null) {  // null for the universal-debug, universal-release variants
                output.versionCodeOverride =
                        versionCodes.get(abi.identifier) * 2000000 + defaultConfig.versionCode
            }
        }
    }
}

repositories {
    maven {
        url 'https://maven.google.com'
    }
}

dependencies {
   
     implementation "com.facebook.react:react-native:+"  // From node_modules

    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"


    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }


    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.1'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation "com.google.firebase:firebase-messaging:$firebaseVersion"

    androidTestImplementation('com.wix:detox:+')
    implementation project(':reactnativenotifications')
    implementation project(':watermelondb-jsi')

    api('io.jsonwebtoken:jjwt-api:0.12.5')
    runtimeOnly('io.jsonwebtoken:jjwt-impl:0.12.5')
    runtimeOnly('io.jsonwebtoken:jjwt-orgjson:0.12.5') {
        exclude(group: 'org.json', module: 'json') //provided by Android natively
    }


}

configurations.all {
    resolutionStrategy {
        eachDependency { DependencyResolveDetails details ->
            if (details.requested.name == 'play-services-base') {
                details.useTarget group: details.requested.group, name: details.requested.name, version: '18.2.0'
            }
            if (details.requested.name == 'play-services-tasks') {
                details.useTarget group: details.requested.group, name: details.requested.name, version: '18.0.2'
            }
            if (details.requested.name == 'play-services-basement') {
                details.useTarget group: details.requested.group, name: details.requested.name, version: '18.2.0'
            }
            if (details.requested.name == 'okhttp') {
                details.useTarget group: details.requested.group, name: details.requested.name, version: '4.12.0'
            }
            if (details.requested.name == 'okhttp-tls') {
                details.useTarget group: details.requested.group, name: details.requested.name, version: '4.12.0'
            }
            if (details.requested.name == 'okhttp-urlconnection') {
                details.useTarget group: details.requested.group, name: details.requested.name, version: '4.12.0'
            }
        }
    }
}

// Run this once to be able to run the application with BUCK
// puts all compile dependencies into folder libs for BUCK to use
tasks.register('copyDownloadableDepsToLibs', Copy) {
    from configurations.implementation
    into 'libs'
}

apply plugin: 'com.google.gms.google-services'
apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
