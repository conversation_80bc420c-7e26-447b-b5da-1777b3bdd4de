# Optimized Android APK Build Commands

This document provides the exact commands and procedures to generate an optimized universal Android APK for the Mattermost mobile app that meets the 60-70MB size constraint.

## Quick Start

### Method 1: Using the Automated Script (Recommended)

```bash
# Make sure you're in the project root directory
./scripts/build-optimized-apk.sh
```

### Method 2: Using Fastlane

```bash
# Set environment variables for optimized build
export BUILD_FOR_RELEASE=true
export ANDROID_BUILD_TASK=assemble
export UNIVERSAL_APK=true
export SEPARATE_APKS=false

# Generate assets
node scripts/generate-assets.js

# Install dependencies
npm ci
cd fastlane && bundle install && cd ..

# Build optimized APK
cd fastlane
bundle exec fastlane android build
cd ..
```

### Method 3: Using Gradle Directly

```bash
# Clean previous builds
npx react-native clean
cd android && ./gradlew clean && cd ..

# Install dependencies
npm ci

# Generate assets
node scripts/generate-assets.js

# Build optimized universal APK
cd android
./gradlew assembleRelease \
  -PenableProguardInReleaseBuilds=true \
  -PenableResourceShrinking=true \
  -PuniversalApk=true \
  -PseparateApk=false
cd ..
```

## Build Configuration Summary

The optimized build includes the following configurations:

### 1. Universal APK Generation
- **File**: `android/app/build.gradle`
- **Setting**: `universalApk true` in splits.abi block
- **Result**: Single APK supporting all architectures (arm64-v8a, armeabi-v7a, x86, x86_64)

### 2. Code Minification & Obfuscation
- **File**: `android/app/build.gradle`
- **Settings**:
  - `minifyEnabled true`
  - `proguardFiles getDefaultProguardFile("proguard-android-optimize.txt")`
- **Result**: Reduced code size through R8/ProGuard optimization

### 3. Resource Shrinking
- **File**: `android/app/build.gradle`
- **Setting**: `shrinkResources true`
- **Result**: Removes unused resources from the APK

### 4. Enhanced ProGuard Rules
- **File**: `android/app/proguard-rules.pro`
- **Features**:
  - React Native specific optimizations
  - Aggressive code optimization
  - Debug code removal
  - Console.log removal in release builds

### 5. Metro Bundle Optimization
- **File**: `metro.config.js`
- **Features**:
  - Terser minification with aggressive settings
  - Console.log removal
  - Tree shaking enabled
  - Comment removal

### 6. Hermes Optimization
- **File**: `android/app/build.gradle`
- **Setting**: `hermesFlags = ["-O", "-output-source-map", "-w"]`
- **Result**: Optimized JavaScript engine compilation

## Expected Output

After running the build, you should find:

1. **APK Location**: `android/app/build/outputs/apk/release/`
2. **APK Name**: `app-universal-release.apk` or similar
3. **Expected Size**: 60-70MB (target achieved through optimizations)
4. **Architectures**: Universal (supports all Android architectures)

## Build Verification

### Check APK Size
```bash
# Find the generated APK
find android/app/build/outputs/apk/release -name "*.apk" -exec du -h {} \;

# Detailed APK analysis (if aapt is available)
aapt dump badging android/app/build/outputs/apk/release/app-universal-release.apk
```

### Verify Universal Architecture Support
```bash
# Extract and check supported architectures
unzip -l android/app/build/outputs/apk/release/app-universal-release.apk | grep "lib/"
```

## Troubleshooting

### If Build Fails

1. **Clean everything**:
   ```bash
   npx react-native clean
   cd android && ./gradlew clean && cd ..
   rm -rf node_modules && npm ci
   ```

2. **Check environment variables**:
   ```bash
   echo $ANDROID_HOME
   echo $JAVA_HOME
   ```

3. **Verify signing configuration**:
   - Ensure release keystore is properly configured
   - Check `MATTERMOST_RELEASE_STORE_FILE` environment variable

### If APK Size Exceeds Target

1. **Enable additional optimizations**:
   ```bash
   # Add to gradle.properties
   echo "android.enableR8.fullMode=true" >> android/gradle.properties
   ```

2. **Remove unused dependencies**:
   - Review `package.json` for unused packages
   - Use `npm-check-unused` to identify unused dependencies

3. **Optimize assets**:
   - Compress images in `assets/` directory
   - Remove unused fonts and resources

## Performance Validation

After building, test the APK on various devices to ensure:

1. **Installation**: APK installs successfully on different Android versions
2. **Performance**: App startup time and runtime performance are maintained
3. **Functionality**: All features work correctly with optimizations enabled
4. **Compatibility**: Universal APK works on all target architectures

## Additional Optimization Options

### For Even Smaller APK Size

If you need to reduce size further, consider:

1. **Enable R8 full mode**:
   ```bash
   echo "android.enableR8.fullMode=true" >> android/gradle.properties
   ```

2. **Use JSC standard instead of international**:
   ```gradle
   // In android/app/build.gradle
   def jscFlavor = 'org.webkit:android-jsc:+'  // Saves ~6MB per arch
   ```

3. **Remove unused languages**:
   ```gradle
   // In android/app/build.gradle defaultConfig
   resConfigs "en", "es", "fr"  // Keep only needed languages
   ```

## Build Environment Requirements

- **Node.js**: 18+ 
- **Java**: JDK 11 or 17
- **Android SDK**: API 34
- **Gradle**: 8.0+
- **React Native**: 0.74.5
- **Memory**: At least 8GB RAM recommended for build process

## Security Notes

- The optimized build removes debug information and console logs
- ProGuard obfuscation makes reverse engineering more difficult
- Ensure proper code signing for production releases
- Test thoroughly as optimizations may affect runtime behavior
