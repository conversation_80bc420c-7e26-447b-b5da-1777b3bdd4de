#!/bin/bash

# Build Stable APK - Crash Fix Version
# This script builds an APK with crash prevention measures

set -e

echo "🔧 Building Stable APK with Crash Fixes..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Clean previous builds
print_status "Step 1: Cleaning previous builds..."
npx react-native clean
cd android && ./gradlew clean && cd ..
print_success "Clean completed"

# Step 2: Install dependencies
print_status "Step 2: Installing dependencies..."
npm ci
print_success "Dependencies installed"

# Step 3: Generate assets
print_status "Step 3: Generating assets..."
node scripts/generate-assets.js
print_success "Assets generated"

# Step 4: Build stable APK with crash prevention
print_status "Step 4: Building stable APK..."
cd android

# Build with safer settings
./gradlew assembleRelease \
  -PenableProguardInReleaseBuilds=true \
  -PenableResourceShrinking=false \
  -PuniversalApk=true \
  -PseparateApk=false \
  --stacktrace \
  --info

cd ..

# Step 5: Verify APK was created
APK_PATH="android/app/build/outputs/apk/release/app-release.apk"
if [ -f "$APK_PATH" ]; then
    print_success "APK built successfully!"
    print_status "APK Location: $APK_PATH"
    
    # Get APK size
    APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
    print_status "APK Size: $APK_SIZE"
    
    # Run basic validation
    print_status "Running basic APK validation..."
    if command -v aapt &> /dev/null; then
        aapt dump badging "$APK_PATH" | head -5
    else
        print_warning "aapt not available, skipping detailed validation"
    fi
    
    print_success "Build completed successfully!"
    echo ""
    echo "📱 Next Steps:"
    echo "1. Install the APK on your device: adb install $APK_PATH"
    echo "2. Test the app launch and basic functionality"
    echo "3. Check device logs if issues persist: adb logcat | grep -i mattermost"
    echo ""
    
else
    print_error "APK build failed - file not found at $APK_PATH"
    exit 1
fi
