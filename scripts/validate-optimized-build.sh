#!/usr/bin/env bash

# ============================================================================
# APK Validation Script for Optimized Mattermost Mobile Build
# ============================================================================
# This script validates that the optimized APK meets all requirements:
# - Size constraint (60-70MB)
# - Universal architecture support
# - Performance characteristics
# - Functionality preservation
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
TARGET_SIZE_MB=70
MIN_SIZE_MB=30
APK_DIR="android/app/build/outputs/apk/release"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to find the APK file
find_apk() {
    # Look for universal APK first
    APK_PATH=$(find "$APK_DIR" -name "*universal*.apk" 2>/dev/null | head -1)
    
    # Fallback to any release APK
    if [ -z "$APK_PATH" ]; then
        APK_PATH=$(find "$APK_DIR" -name "*.apk" 2>/dev/null | head -1)
    fi
    
    if [ -z "$APK_PATH" ] || [ ! -f "$APK_PATH" ]; then
        print_error "No APK found in $APK_DIR"
        print_status "Please run the build script first: ./scripts/build-optimized-apk.sh"
        exit 1
    fi
    
    echo "$APK_PATH"
}

# Function to validate APK size
validate_size() {
    local apk_path="$1"
    print_status "Validating APK size..."
    
    local size_bytes=$(stat -f%z "$apk_path" 2>/dev/null || stat -c%s "$apk_path" 2>/dev/null)
    local size_mb=$((size_bytes / 1024 / 1024))
    local size_human=$(du -h "$apk_path" | cut -f1)
    
    echo "APK Size: $size_human ($size_mb MB)"
    
    if [ "$size_mb" -le "$TARGET_SIZE_MB" ] && [ "$size_mb" -ge "$MIN_SIZE_MB" ]; then
        print_success "✅ APK size ($size_mb MB) is within target range ($MIN_SIZE_MB-$TARGET_SIZE_MB MB)"
        return 0
    elif [ "$size_mb" -gt "$TARGET_SIZE_MB" ]; then
        print_warning "⚠️  APK size ($size_mb MB) exceeds target ($TARGET_SIZE_MB MB)"
        return 1
    else
        print_warning "⚠️  APK size ($size_mb MB) seems unusually small (< $MIN_SIZE_MB MB)"
        return 1
    fi
}

# Function to validate universal architecture support
validate_architectures() {
    local apk_path="$1"
    print_status "Validating architecture support..."
    
    if ! command -v unzip &> /dev/null; then
        print_warning "unzip not available, skipping architecture validation"
        return 0
    fi
    
    local temp_dir=$(mktemp -d)
    unzip -q "$apk_path" -d "$temp_dir"
    
    local archs=$(find "$temp_dir/lib" -type d -name "*" 2>/dev/null | grep -E "(arm64-v8a|armeabi-v7a|x86|x86_64)" | wc -l)
    
    if [ "$archs" -ge 4 ]; then
        print_success "✅ Universal APK detected (supports multiple architectures)"
        find "$temp_dir/lib" -type d -name "*" 2>/dev/null | sed 's|.*/||' | sort
    elif [ "$archs" -ge 2 ]; then
        print_success "✅ Multi-architecture APK detected"
        find "$temp_dir/lib" -type d -name "*" 2>/dev/null | sed 's|.*/||' | sort
    else
        print_warning "⚠️  Limited architecture support detected"
    fi
    
    rm -rf "$temp_dir"
    return 0
}

# Function to validate APK structure and metadata
validate_apk_metadata() {
    local apk_path="$1"
    print_status "Validating APK metadata..."
    
    if command -v aapt &> /dev/null; then
        echo "Package Information:"
        aapt dump badging "$apk_path" | grep -E "(package|sdkVersion|targetSdkVersion|application-label)" | head -10
        
        echo -e "\nPermissions (first 10):"
        aapt dump permissions "$apk_path" | head -10
        
        echo -e "\nNative Libraries:"
        aapt list "$apk_path" | grep "lib/" | head -10
        
        print_success "✅ APK metadata validation completed"
    else
        print_warning "aapt not available, skipping detailed APK analysis"
    fi
}

# Function to check for optimization indicators
validate_optimizations() {
    local apk_path="$1"
    print_status "Checking optimization indicators..."
    
    local temp_dir=$(mktemp -d)
    unzip -q "$apk_path" -d "$temp_dir"
    
    # Check for Hermes bytecode
    if find "$temp_dir" -name "*.hbc" | grep -q .; then
        print_success "✅ Hermes bytecode detected (JavaScript optimization enabled)"
    else
        print_warning "⚠️  No Hermes bytecode found"
    fi
    
    # Check for ProGuard/R8 mapping
    if [ -f "android/app/build/outputs/mapping/release/mapping.txt" ]; then
        print_success "✅ ProGuard/R8 mapping file found (code minification enabled)"
    else
        print_warning "⚠️  No ProGuard/R8 mapping file found"
    fi
    
    # Check for resource optimization
    local resources_arsc_size=$(find "$temp_dir" -name "resources.arsc" -exec stat -f%z {} \; 2>/dev/null || find "$temp_dir" -name "resources.arsc" -exec stat -c%s {} \; 2>/dev/null)
    if [ -n "$resources_arsc_size" ] && [ "$resources_arsc_size" -lt 5000000 ]; then  # Less than 5MB
        print_success "✅ Resources appear to be optimized (resources.arsc < 5MB)"
    else
        print_warning "⚠️  Resources may not be fully optimized"
    fi
    
    rm -rf "$temp_dir"
}

# Function to generate build report
generate_report() {
    local apk_path="$1"
    local report_file="build-output/optimization-report.txt"
    
    print_status "Generating optimization report..."
    
    mkdir -p build-output
    
    {
        echo "Mattermost Mobile - Optimized APK Build Report"
        echo "=============================================="
        echo "Generated: $(date)"
        echo "APK Path: $apk_path"
        echo ""
        
        echo "Size Analysis:"
        echo "-------------"
        local size_bytes=$(stat -f%z "$apk_path" 2>/dev/null || stat -c%s "$apk_path" 2>/dev/null)
        local size_mb=$((size_bytes / 1024 / 1024))
        local size_human=$(du -h "$apk_path" | cut -f1)
        echo "APK Size: $size_human ($size_mb MB)"
        echo "Target Range: $MIN_SIZE_MB-$TARGET_SIZE_MB MB"
        echo "Status: $([ "$size_mb" -le "$TARGET_SIZE_MB" ] && echo "PASS" || echo "NEEDS OPTIMIZATION")"
        echo ""
        
        echo "Build Configuration:"
        echo "-------------------"
        echo "ProGuard/R8: $(grep -q "minifyEnabled true" android/app/build.gradle && echo "ENABLED" || echo "DISABLED")"
        echo "Resource Shrinking: $(grep -q "shrinkResources true" android/app/build.gradle && echo "ENABLED" || echo "DISABLED")"
        echo "Universal APK: $(grep -q "universalApk true" android/app/build.gradle && echo "ENABLED" || echo "DISABLED")"
        echo "Hermes: $(grep -q "hermesEnabled=true" android/gradle.properties && echo "ENABLED" || echo "DISABLED")"
        echo ""
        
        if command -v aapt &> /dev/null; then
            echo "APK Details:"
            echo "-----------"
            aapt dump badging "$apk_path" | grep -E "(package|sdkVersion|targetSdkVersion|application-label)"
        fi
        
    } > "$report_file"
    
    print_success "Report generated: $report_file"
}

# Function to provide optimization recommendations
provide_recommendations() {
    local apk_path="$1"
    local size_bytes=$(stat -f%z "$apk_path" 2>/dev/null || stat -c%s "$apk_path" 2>/dev/null)
    local size_mb=$((size_bytes / 1024 / 1024))
    
    print_status "Optimization Recommendations:"
    
    if [ "$size_mb" -gt "$TARGET_SIZE_MB" ]; then
        echo "📋 APK size exceeds target. Consider these optimizations:"
        echo "   1. Enable R8 full mode: android.enableR8.fullMode=true"
        echo "   2. Use standard JSC instead of international variant"
        echo "   3. Remove unused dependencies from package.json"
        echo "   4. Optimize image assets (compress/resize)"
        echo "   5. Remove unused language resources"
        echo "   6. Consider using App Bundle (.aab) instead of APK"
    else
        echo "✅ APK size is within target range!"
    fi
    
    echo ""
    echo "📋 Performance recommendations:"
    echo "   1. Test on low-end devices to ensure performance"
    echo "   2. Monitor app startup time"
    echo "   3. Verify all features work with optimizations"
    echo "   4. Test on different Android versions"
    
    echo ""
    echo "📋 Distribution recommendations:"
    echo "   1. Test installation on various devices"
    echo "   2. Verify app signing for production"
    echo "   3. Consider generating App Bundle for Play Store"
    echo "   4. Keep mapping.txt file for crash debugging"
}

# Main validation function
main() {
    print_status "Starting APK validation..."
    
    local apk_path=$(find_apk)
    print_status "Found APK: $apk_path"
    
    local validation_passed=true
    
    # Run all validations
    validate_size "$apk_path" || validation_passed=false
    validate_architectures "$apk_path"
    validate_apk_metadata "$apk_path"
    validate_optimizations "$apk_path"
    
    # Generate report
    generate_report "$apk_path"
    
    # Provide recommendations
    provide_recommendations "$apk_path"
    
    echo ""
    if [ "$validation_passed" = true ]; then
        print_success "🎉 APK validation completed successfully!"
        print_success "Your optimized universal APK is ready for distribution."
    else
        print_warning "⚠️  APK validation completed with warnings."
        print_status "Review the recommendations above for further optimization."
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "This script validates the optimized APK build and provides recommendations."
        echo ""
        echo "Options:"
        echo "  --help, -h       Show this help message"
        echo ""
        echo "The script checks:"
        echo "  - APK size (target: 60-70MB)"
        echo "  - Universal architecture support"
        echo "  - Optimization indicators"
        echo "  - APK metadata and structure"
        ;;
    *)
        main
        ;;
esac
