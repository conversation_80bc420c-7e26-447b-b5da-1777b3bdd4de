#!/usr/bin/env bash

# ============================================================================
# Optimized Android APK Build Script for Mattermost Mobile
# ============================================================================
# This script generates a universal, optimized Android APK with maximum
# size reduction and performance optimizations.
# 
# Target: 60-70MB universal APK
# Features: ProGuard/R8, resource shrinking, Hermes optimization
# ============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUILD_TYPE="Release"
ENABLE_OPTIMIZATIONS=true
CLEAN_BUILD=true
ANALYZE_SIZE=true

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "android" ]; then
        print_error "Please run this script from the root of the React Native project"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    # Check if Android SDK is available
    if [ -z "$ANDROID_HOME" ]; then
        print_error "ANDROID_HOME environment variable is not set"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to clean previous builds
clean_build() {
    if [ "$CLEAN_BUILD" = true ]; then
        print_status "Cleaning previous builds..."
        
        # Clean React Native
        npx react-native clean
        
        # Clean Android build
        cd android
        ./gradlew clean
        cd ..
        
        # Clean node_modules cache
        rm -rf node_modules/.cache
        
        print_success "Build cleaned"
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install npm dependencies
    npm ci
    
    # Install Fastlane gems
    cd fastlane && bundle install && cd ..
    
    print_success "Dependencies installed"
}

# Function to generate assets
generate_assets() {
    print_status "Generating app assets..."
    
    ASSETS=$(node scripts/generate-assets.js)
    if [ -z "$ASSETS" ]; then
        print_error "Failed to generate app assets"
        exit 1
    fi
    
    print_success "App assets generated"
}

# Function to build optimized APK
build_apk() {
    print_status "Building optimized universal APK..."
    
    # Set environment variables for optimized build
    export BUILD_FOR_RELEASE=true
    export ANDROID_BUILD_TASK=assemble
    export UNIVERSAL_APK=true
    export SEPARATE_APKS=false
    
    # Build using Fastlane
    cd fastlane
    bundle exec fastlane android build
    cd ..
    
    print_success "APK build completed"
}

# Function to build using direct Gradle (alternative method)
build_apk_gradle() {
    print_status "Building optimized universal APK using Gradle..."
    
    cd android
    
    # Build release APK with optimizations
    ./gradlew assembleRelease \
        -PenableProguardInReleaseBuilds=true \
        -PenableResourceShrinking=true \
        -PuniversalApk=true \
        -PseparateApk=false
    
    cd ..
    
    print_success "Gradle APK build completed"
}

# Function to analyze APK size
analyze_apk_size() {
    if [ "$ANALYZE_SIZE" = true ]; then
        print_status "Analyzing APK size..."
        
        # Find the generated APK
        APK_PATH=$(find android/app/build/outputs/apk/release -name "*.apk" | head -1)
        
        if [ -f "$APK_PATH" ]; then
            APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
            print_success "APK generated: $APK_PATH"
            print_success "APK size: $APK_SIZE"
            
            # Check if size is within target range (60-70MB)
            APK_SIZE_MB=$(du -m "$APK_PATH" | cut -f1)
            if [ "$APK_SIZE_MB" -le 70 ]; then
                print_success "✅ APK size ($APK_SIZE_MB MB) is within target range (≤70MB)"
            else
                print_warning "⚠️  APK size ($APK_SIZE_MB MB) exceeds target range (≤70MB)"
            fi
            
            # Optional: Analyze APK contents
            if command -v aapt &> /dev/null; then
                print_status "APK analysis:"
                aapt dump badging "$APK_PATH" | grep -E "(package|sdkVersion|targetSdkVersion|application-label)"
            fi
        else
            print_error "APK not found in expected location"
            exit 1
        fi
    fi
}

# Function to copy APK to output directory
copy_apk_output() {
    print_status "Copying APK to output directory..."
    
    # Create output directory
    mkdir -p build-output
    
    # Find and copy the universal APK
    APK_PATH=$(find android/app/build/outputs/apk/release -name "*universal*.apk" | head -1)
    
    if [ -z "$APK_PATH" ]; then
        # Fallback to any release APK
        APK_PATH=$(find android/app/build/outputs/apk/release -name "*.apk" | head -1)
    fi
    
    if [ -f "$APK_PATH" ]; then
        TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
        OUTPUT_NAME="mattermost-optimized-universal-${TIMESTAMP}.apk"
        cp "$APK_PATH" "build-output/$OUTPUT_NAME"
        print_success "APK copied to: build-output/$OUTPUT_NAME"
    else
        print_error "No APK found to copy"
        exit 1
    fi
}

# Main execution
main() {
    print_status "Starting optimized APK build process..."
    
    check_prerequisites
    clean_build
    install_dependencies
    generate_assets
    
    # Try Fastlane first, fallback to Gradle
    if build_apk; then
        print_success "Fastlane build successful"
    else
        print_warning "Fastlane build failed, trying Gradle..."
        build_apk_gradle
    fi
    
    analyze_apk_size
    copy_apk_output
    
    print_success "🎉 Optimized universal APK build completed successfully!"
    print_status "Check the build-output directory for your optimized APK"
}

# Handle script arguments
case "${1:-}" in
    --gradle-only)
        print_status "Using Gradle-only build method"
        check_prerequisites
        clean_build
        install_dependencies
        generate_assets
        build_apk_gradle
        analyze_apk_size
        copy_apk_output
        ;;
    --no-clean)
        CLEAN_BUILD=false
        main
        ;;
    --no-analysis)
        ANALYZE_SIZE=false
        main
        ;;
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --gradle-only    Use Gradle directly instead of Fastlane"
        echo "  --no-clean       Skip cleaning previous builds"
        echo "  --no-analysis    Skip APK size analysis"
        echo "  --help, -h       Show this help message"
        echo ""
        echo "This script builds an optimized universal Android APK with:"
        echo "  - ProGuard/R8 code minification"
        echo "  - Resource shrinking"
        echo "  - Hermes optimization"
        echo "  - Universal architecture support"
        echo "  - Target size: 60-70MB"
        ;;
    *)
        main
        ;;
esac
