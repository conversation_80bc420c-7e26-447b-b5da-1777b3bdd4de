#!/bin/bash

# APK Crash Debugging Script
# This script helps collect crash logs and debug information

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🐛 APK Crash Debugging Tool"
echo "============================"

# Check if adb is available
if ! command -v adb &> /dev/null; then
    print_error "adb is not available. Please install Android SDK platform-tools."
    exit 1
fi

# Check if device is connected
DEVICE_COUNT=$(adb devices | grep -v "List of devices" | grep -c "device$" || true)
if [ "$DEVICE_COUNT" -eq 0 ]; then
    print_error "No Android device connected. Please connect your device and enable USB debugging."
    exit 1
fi

print_success "Found $DEVICE_COUNT connected device(s)"

# Function to collect crash logs
collect_crash_logs() {
    print_status "Collecting crash logs..."
    
    # Create logs directory
    mkdir -p logs
    
    # Clear existing logs
    adb logcat -c
    
    print_status "Starting logcat monitoring..."
    print_warning "Please launch the app now and reproduce the crash"
    print_status "Press Ctrl+C when done collecting logs"
    
    # Collect logs with filters for common crash indicators
    adb logcat -v time \
        AndroidRuntime:E \
        System.err:E \
        ReactNativeJS:E \
        ReactNative:E \
        Mattermost:* \
        com.mattermost.rnbeta:* \
        *:F \
        | tee logs/crash_log_$(date +%Y%m%d_%H%M%S).txt
}

# Function to get app info
get_app_info() {
    print_status "Getting app information..."
    
    APP_PACKAGE="com.mattermost.rnbeta"
    
    echo "App Package: $APP_PACKAGE"
    
    # Check if app is installed
    if adb shell pm list packages | grep -q "$APP_PACKAGE"; then
        print_success "App is installed"
        
        # Get app version
        APP_VERSION=$(adb shell dumpsys package "$APP_PACKAGE" | grep "versionName" | head -1 | cut -d'=' -f2)
        echo "App Version: $APP_VERSION"
        
        # Get app permissions
        print_status "App Permissions:"
        adb shell dumpsys package "$APP_PACKAGE" | grep "permission\." | head -10
        
    else
        print_error "App is not installed"
    fi
}

# Function to install APK
install_apk() {
    APK_PATH="android/app/build/outputs/apk/release/app-release.apk"
    
    if [ -f "$APK_PATH" ]; then
        print_status "Installing APK: $APK_PATH"
        adb install -r "$APK_PATH"
        print_success "APK installed"
    else
        print_error "APK not found at $APK_PATH"
        print_status "Please build the APK first using: ./scripts/build-stable-apk.sh"
    fi
}

# Function to uninstall app
uninstall_app() {
    print_status "Uninstalling app..."
    adb uninstall com.mattermost.rnbeta || true
    print_success "App uninstalled"
}

# Function to get device info
get_device_info() {
    print_status "Device Information:"
    echo "Model: $(adb shell getprop ro.product.model)"
    echo "Android Version: $(adb shell getprop ro.build.version.release)"
    echo "API Level: $(adb shell getprop ro.build.version.sdk)"
    echo "Architecture: $(adb shell getprop ro.product.cpu.abi)"
    echo "RAM: $(adb shell cat /proc/meminfo | grep MemTotal)"
}

# Main menu
show_menu() {
    echo ""
    echo "Select an option:"
    echo "1) Install APK"
    echo "2) Get app info"
    echo "3) Collect crash logs"
    echo "4) Get device info"
    echo "5) Uninstall app"
    echo "6) Exit"
    echo ""
    read -p "Enter your choice (1-6): " choice
    
    case $choice in
        1) install_apk ;;
        2) get_app_info ;;
        3) collect_crash_logs ;;
        4) get_device_info ;;
        5) uninstall_app ;;
        6) exit 0 ;;
        *) print_error "Invalid option" ;;
    esac
    
    show_menu
}

# Start the menu
show_menu
