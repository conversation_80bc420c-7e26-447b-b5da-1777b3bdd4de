#!/bin/bash

# Quick APK Build with Crash Fixes
# This script builds an APK with the crash fixes without full clean

set -e

echo "🔧 Building APK with Crash Fixes (Quick Build)..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Clean only Android build cache
print_status "Step 1: Cleaning Android build cache..."
cd android && ./gradlew clean && cd ..
print_success "Android build cache cleaned"

# Step 2: Generate assets (if script exists)
if [ -f "scripts/generate-assets.js" ]; then
    print_status "Step 2: Generating assets..."
    node scripts/generate-assets.js
    print_success "Assets generated"
else
    print_warning "Asset generation script not found, skipping..."
fi

# Step 3: Build APK with crash fixes
print_status "Step 3: Building APK with crash fixes..."
cd android

# Build with safer settings to prevent crashes
print_status "Building with crash prevention settings..."
./gradlew assembleRelease \
  -PenableProguardInReleaseBuilds=true \
  -PenableResourceShrinking=false \
  -PuniversalApk=true \
  -PseparateApk=false \
  --stacktrace

cd ..

# Step 4: Verify APK was created
APK_PATH="android/app/build/outputs/apk/release/app-release.apk"
if [ -f "$APK_PATH" ]; then
    print_success "APK built successfully!"
    print_status "APK Location: $APK_PATH"
    
    # Get APK size
    APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
    print_status "APK Size: $APK_SIZE"
    
    print_success "Build completed successfully!"
    echo ""
    echo "📱 Next Steps:"
    echo "1. Install the APK: adb install -r $APK_PATH"
    echo "2. Test the app launch"
    echo "3. If crash persists, run: ./scripts/debug-apk-crash.sh"
    echo ""
    
    # Show what was fixed
    echo "🔧 Crash Fixes Applied:"
    echo "✅ Fixed package name mismatch (com.mattermost.rnbeta.fack → com.mattermost.rnbeta)"
    echo "✅ Added ProGuard rules to prevent over-optimization"
    echo "✅ Disabled resource shrinking to prevent missing resources"
    echo "✅ Used standard ProGuard instead of aggressive optimization"
    echo ""
    
else
    print_error "APK build failed - file not found at $APK_PATH"
    print_status "Check the build output above for errors"
    exit 1
fi
