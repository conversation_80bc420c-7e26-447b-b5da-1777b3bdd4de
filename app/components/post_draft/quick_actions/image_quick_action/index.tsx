// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback } from 'react';
import { useIntl } from 'react-intl';
import { Alert, StyleSheet } from 'react-native';

import CompassIcon from '@components/compass_icon';
import TouchableWithFeedback from '@components/touchable_with_feedback';
import { ICON_SIZE } from '@constants/post_draft';
import { useTheme } from '@context/theme';
import { fileMaxWarning } from '@utils/file';
import PickerUtil from '@utils/file/file_picker';
import { changeOpacity } from '@utils/theme';

import type { QuickActionAttachmentProps } from '@typings/components/post_draft_quick_action';
import { PhotoIcon } from 'react-native-heroicons/outline'
// import PhotoEditor from 'react-native-photo-editor';
// import ImageEditor from '@thienmd/react-native-image-editor'
// import ImageEditor from 'alhashedi-react-native-image-editor' // Temporarily disabled for build fix
// import ImageEditor from '@ezz/react-native-image-editor'

import FilePickerUtil from '@utils/file/file_picker';
import type { DocumentPickerResponse } from 'react-native-document-picker';
import RNFS from 'react-native-fs';
import deleteImageFile from '@app/utils/deleteImgCash';
import { useAlert } from '@app/context/alert';



const style = StyleSheet.create({
    icon: {
        alignItems: 'center',
        justifyContent: 'center',
        flexGrow: 1,
    },
});

export default function ImageQuickAction({
    disabled,
    fileCount = 0,
    onUploadFiles,
    maxFilesReached,
    maxFileCount,
    testID = '',
}: QuickActionAttachmentProps) {
    const intl = useIntl();
    const theme = useTheme();
    const { showAlert } = useAlert();

    const handleButtonPress = useCallback(() => {
        if (maxFilesReached) {
            showAlert(
                intl.formatMessage({
                    id: 'mobile.link.error.title',
                    defaultMessage: 'Error',
                }),
                fileMaxWarning(intl, maxFileCount),
            );

            return;
        }

        try {
            const picker = new PickerUtil(intl,
                (files: ExtractedFileInfo[]) => {
                    try {

                        if (files && files?.length > 0 && files[0].mime_type.split('/')[0] === 'image') {

                            console.log('this from edite useEffect');
                            const fileName = files![0].localPath
                            const file = new FilePickerUtil(intl, onUploadFiles);

                            // ImageEditor temporarily disabled for build fix
                            // TODO: Re-enable image editing functionality
                            /*
                            ImageEditor.Edit({
                                path: fileName?.substring(7, fileName.length),
                                hiddenControls: ['save'],
                                onDone: (imagePath: string) => {
                                    try {
                                        console.log(`\n\nthis the file path ${imagePath}\n\n`)


                                        RNFS.exists(imagePath).then((exists) => {
                                            console.log('image path', exists);

                                        })
                                        const fileNameToList = imagePath.split('/');
                                        console.log(fileNameToList);
                                        const filepath = `file://${imagePath}`
                                        const imagePicker = {

                                            uri: filepath,
                                            name: fileNameToList[fileNameToList.length - 1],
                                            copyError: null,
                                            fileCopyUri: null,
                                            type: fileNameToList[fileNameToList.length - 1].split('.')[1],
                                            size: undefined,
                                        } as unknown as DocumentPickerResponse;
                                        console.log('image path', imagePath);
                                        console.log('file name', fileNameToList[fileNameToList.length - 1]);


                                        file.prepareFileUploadAudio(imagePicker)
                                            // .then(() => {
                                            // deleteImageFile(filepath)
                                            //  })


                                            .catch(
                                                (err) => console.error('error is this ', err));

                                    } catch (error) {
                                        console.error('PhotoEditor Exception:', error);
                                    }


                                },
                                onCancel: () => {
                                    console.log('Editing cancelled by user.');
                                },
                                onError: (error: string) => {
                                    console.error('PhotoEditor Error:', error);
                                },
                            } as any);
                            */

                            // Temporary workaround: directly upload the image without editing
                            const imagePicker = {
                                uri: files![0].localPath,
                                name: files![0].name,
                                copyError: null,
                                fileCopyUri: null,
                                type: files![0].type,
                                size: files![0].size,
                            } as unknown as DocumentPickerResponse;

                            file.prepareFileUploadAudio(imagePicker)
                                .catch((err) => console.error('error is this ', err));
                            //   const file = new FilePickerUtil(intl, onUploadFiles);
                            //                             const filesPath: string[] = [];

                            //                             for (let index = 0; index < files.length; index++) {
                            //                                 const fileName = files![index].localPath;
                            //                                 if (fileName !== undefined)
                            //                                     filesPath.push(fileName.substring(7, fileName.length))
                            //                             }

                            //                                ImageEditor.Edit({
                            //                                   path: filesPath,
                            //                                   hiddenControls: ['save'],
                            //                                   onDone: (imagePath: string) => {
                            //                                       try {
                            //                                           RNFS.exists(imagePath).then((exists) => {
                            //                                               console.log('image path', exists);

                            //                                           })
                            //                                           const fileNameToList = imagePath.split('/');
                            //                                           console.log(fileNameToList);
                            //                                           const filepath = `file://${imagePath}`
                            //                                           const imagePicker = {

                            //                                               uri: filepath,
                            //                                               name: fileNameToList[fileNameToList.length - 1],
                            //                                               copyError: null,
                            //                                               fileCopyUri: null,
                            //                                               type: fileNameToList[fileNameToList.length - 1].split('.')[1],
                            //                                               size: undefined,
                            //                                           } as unknown as DocumentPickerResponse;
                            //                                           console.log('image path', imagePath);
                            //                                           console.log('file name', fileNameToList[fileNameToList.length - 1]);


                            //                                           file.prepareFileUploadAudio(imagePicker)
                            //                                              // .then(() => {
                            //                                                   // deleteImageFile(filepath) 
                            //                                                 //  })


                            //                                               .catch(
                            //                                                   (err) => console.error('error is this ', err));

                            //                                       } catch (error) {
                            //                                           console.error('PhotoEditor Exception:', error);
                            //                                       }


                            //                                   },
                            //                                   onCancel: () => {
                            //                                       console.log('Editing cancelled by user.');
                            //                                   },
                            //                                   onError: (error: string) => {
                            //                                       console.error('PhotoEditor Error:', error);
                            //                                   },
                            //                               } as any);

                        } else {
                            onUploadFiles(files)
                        }

                    } catch (error) {
                        console.error('PhotoEditor Exception:', error);
                    }
                });
            picker.attachImageFromPhotoGallery(maxFileCount - fileCount);

        } catch (err) {
            console.log(`\n\n\n\nthis error from attach file from photogallery${err}\n\n\n`)

        }
    }, [onUploadFiles, fileCount, maxFileCount]);

    const actionTestID = disabled ? `${testID}.disabled` : testID;
    const color = disabled ? changeOpacity(theme.centerChannelColor, 0.16) : changeOpacity(theme.centerChannelColor, 0.64);

    return (
        <TouchableWithFeedback
            testID={actionTestID}
            disabled={disabled}
            onPress={handleButtonPress}
            style={style.icon}
            type={'opacity'}
        >
            <PhotoIcon
                color={color}
                size={ICON_SIZE}
            />
        </TouchableWithFeedback>
    );
}

