// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback, useRef, useEffect} from 'react';
import {View, TouchableOpacity, Alert} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';


import {useTheme} from '@context/theme';
import {selectEmojiCategoryBarSection, useEmojiCategoryBar} from '@hooks/emoji_category_bar';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';

import EmojiCategoryBarIcon from './icon';

import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        justifyContent: 'space-between',
        backgroundColor: theme.centerChannelBg,
        height: 55,
        paddingHorizontal: 12,
        paddingTop: 11,
        borderTopColor: changeOpacity(theme.centerChannelColor, 0.08),
        borderTopWidth: 1,
        flexDirection: 'row',
        alignItems: 'center',
        minHeight: 55, // Ensure consistent height
    },
    categoryIconsContainer: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center',
    },
    backspaceButton: {
        width: 36,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 8,
        ri
        backgroundColor: changeOpacity(theme.errorTextColor, 0.15),
        borderRadius: 8,
        borderWidth: 1.5,
        borderColor: changeOpacity(theme.errorTextColor, 0.3),
    },
    backspaceIcon: {
        color: theme.errorTextColor,
    },

}));

type Props = {
    onSelect?: (index: number | undefined) => void;
    selectedEmojis?: SelectedEmoji[];
    cursorPosition?: number;
    onRemoveEmojiAtPosition?: (position: number) => void;
    onRemoveAllEmojis?: () => void;
}

const EmojiCategoryBar = ({onSelect, selectedEmojis = [], cursorPosition = 0, onRemoveEmojiAtPosition, onRemoveAllEmojis}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const {currentIndex, icons} = useEmojiCategoryBar();

    // Preserve icons to prevent them from disappearing
    const preservedIcons = useRef(icons);

    useEffect(() => {
        if (icons && icons.length > 0) {
            preservedIcons.current = icons;
        }
    }, [icons]);

    // Use preserved icons if current icons are undefined
    const displayIcons = icons || preservedIcons.current;

    // Log component render
    console.log('🔧 EmojiCategoryBar: always visible - emojis =', selectedEmojis.length, 'icons =', !!displayIcons);

    // Helper function to convert cursor position to emoji index
    const getEmojiIndexFromCursorPosition = useCallback((cursor: number, emojis: SelectedEmoji[]) => {
        if (emojis.length === 0) return -1;

        let charCount = 0;
        for (let i = 0; i < emojis.length; i++) {
            const emojiLength = emojis[i].character.length;

            // If cursor is within this emoji's character range
            if (cursor <= charCount + emojiLength) {
                return i;
            }
            charCount += emojiLength;
        }

        // If cursor is at the end or beyond, return the last emoji index
        return emojis.length - 1;
    }, []);

    const scrollToIndex = useCallback((index: number) => {
        if (onSelect) {
            onSelect(index);
            return;
        }

        selectEmojiCategoryBarSection(index);
    }, []);

    // Handle single emoji deletion at cursor position - always executes
    const handleBackspacePress = useCallback(() => {
        console.log('🚀 handleBackspacePress - emojis:', selectedEmojis.length);

        if (selectedEmojis.length > 0 && onRemoveEmojiAtPosition) {
            let emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition, selectedEmojis);

            // For backspace behavior, if cursor is at the beginning of an emoji, delete the previous one
            if (cursorPosition > 0) {
                emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition - 1, selectedEmojis);
            }

            if (emojiIndex >= 0 && emojiIndex < selectedEmojis.length) {
                console.log('🚀 Deleting emoji at index:', emojiIndex);
                onRemoveEmojiAtPosition(emojiIndex);
            }
        } else {
            console.log('🚀 No emojis to delete or callback not available');
        }
    }, [cursorPosition, selectedEmojis, onRemoveEmojiAtPosition, getEmojiIndexFromCursorPosition]);

    // Handle multiple emoji deletion (long press) - always executes
    const handleBackspaceLongPress = useCallback(() => {
        console.log('🔥 handleBackspaceLongPress - emojis:', selectedEmojis.length);

        if (selectedEmojis.length > 0 && onRemoveAllEmojis) {
            Alert.alert(
                'Clear All Emojis',
                `Are you sure you want to remove all ${selectedEmojis.length} emojis?`,
                [
                    {
                        text: 'Cancel',
                        style: 'cancel',
                    },
                    {
                        text: 'Clear All',
                        style: 'destructive',
                        onPress: () => {
                            console.log('🔥 Clearing all emojis');
                            onRemoveAllEmojis();
                        },
                    },
                ]
            );
        } else {
            console.log('🔥 No emojis to clear or callback not available');
        }
    }, [selectedEmojis, onRemoveAllEmojis]);

    // Category bar is ALWAYS rendered - no conditional logic

    return (
        <View
            style={styles.container}
            testID='emoji_picker.category_bar'
        >
            {/* Backspace button - always visible and interactive */}
            <TouchableOpacity
                style={styles.backspaceButton}
                hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                onPress={() => {
                    console.log('👆 Backspace button pressed - emojis:', selectedEmojis.length);
                    handleBackspacePress();
                }}
                onLongPress={() => {
                    console.log('👆 Backspace button long pressed - emojis:', selectedEmojis.length);
                    handleBackspaceLongPress();
                }}
                delayLongPress={500}
                testID="emoji_picker.category_bar.backspace_button"
                activeOpacity={0.6}
                accessibilityLabel="Delete emoji"
                accessibilityRole="button"
                accessibilityHint="Tap to delete emoji at cursor position, long press to delete all emojis"
            >
                {React.createElement(Ionicons as any, {
                    name: "backspace-outline",
                    size: 24,
                    color: "red"
                })}
            </TouchableOpacity>

            {/* Category icons container - always rendered */}
            <View style={styles.categoryIconsContainer}>
                {displayIcons?.map((icon, index) => (
                    <EmojiCategoryBarIcon
                        currentIndex={currentIndex}
                        key={icon.key}
                        icon={icon.icon}
                        index={index}
                        scrollToIndex={scrollToIndex}
                        theme={theme}
                    />
                ))}
            </View>
        </View>
    );
};

export default EmojiCategoryBar;
