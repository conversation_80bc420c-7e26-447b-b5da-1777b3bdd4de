// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback, useState} from 'react';
import {DeviceEventEmitter, StyleSheet} from 'react-native';

import {Events} from '@constants';
import {useIsTablet} from '@hooks/device';
import BottomSheet from '@screens/bottom_sheet';
import {getEmojiByName} from '@utils/emoji/helpers';

import Picker from './picker';
import PickerFooter from './picker/footer';

import type {AvailableScreens} from '@typings/screens/navigation';
import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

type Props = {
    componentId: AvailableScreens;
    onEmojiPress: (emoji: string) => void;
    imageUrl?: string;
    file?: ExtractedFileInfo;
    closeButtonId: string;
    isSelectingMultiple?: boolean | undefined;
    onClose?: (selectedEmojis: SelectedEmoji[]) => void;
};

const style = StyleSheet.create({
    contentStyle: {
        paddingTop: 14,
    },
});

const EmojiPickerScreen = ({closeButtonId, componentId, file, imageUrl, onEmojiPress, isSelectingMultiple = false, onClose}: Props) => {
    const isTablet = useIsTablet();
    const [selectedEmojis, setSelectedEmojis] = useState<SelectedEmoji[]>([]);
    const [cursorPosition, setCursorPosition] = useState(0);

    const handleEmojiPress = useCallback((emoji: string) => {
        console.log('😀 handleEmojiPress CALLED in EmojiPickerScreen!');
        console.log('😀 Emoji:', emoji, 'isSelectingMultiple:', isSelectingMultiple);

        if (isSelectingMultiple) {
            // For multiple selection, add to selected emojis instead of calling onEmojiPress immediately
            const emojiDataMew = getEmojiByName(emoji, []);
            let emojiCharacter = `:${emoji}:`;

            if (emojiDataMew?.image && emojiDataMew.category !== 'custom') {
                const codeArray: string[] = emojiDataMew.image.split('-');
                const code = codeArray.reduce((acc, c) => {
                    return acc + String.fromCodePoint(parseInt(c, 16));
                }, '');
                emojiCharacter = code;
            }

            const newEmoji: SelectedEmoji = {
                id: `${emoji}_${Date.now()}`,
                character: emojiCharacter,
                name: emoji,
            };

            console.log('😀 Adding new emoji:', newEmoji);
            setSelectedEmojis((prev) => {
                const newArray = [...prev, newEmoji];
                console.log('😀 New selectedEmojis array:', newArray.map(e => ({ id: e.id, character: e.character })));
                return newArray;
            });
        } else {
            // For single selection, use original behavior
            console.log('😀 Single selection mode, calling onEmojiPress and closing sheet');
            onEmojiPress(emoji);
            DeviceEventEmitter.emit(Events.CLOSE_BOTTOM_SHEET);
        }
    }, [isSelectingMultiple, onEmojiPress]);

    const handleRemoveEmoji = useCallback((id: string) => {
        setSelectedEmojis((prev) => prev.filter((emoji) => emoji.id !== id));
    }, []);

    const handleRemoveEmojiAtPosition = useCallback((position: number) => {
        console.log('🎯 Removing emoji at position:', position);

        setSelectedEmojis((prev) => {
            const newEmojis = [...prev];

            if (position >= 0 && position < newEmojis.length) {
                // Calculate new cursor position after deletion
                let newCursorPosition = 0;
                for (let i = 0; i < position; i++) {
                    newCursorPosition += newEmojis[i].character.length;
                }

                newEmojis.splice(position, 1);
                setCursorPosition(newCursorPosition);
            }
            return newEmojis;
        });
    }, []);

    const handleCursorPositionChange = useCallback((position: number) => {
        console.log('📍 handleCursorPositionChange CALLED in EmojiPickerScreen!');
        console.log('📍 New cursor position:', position);
        setCursorPosition(position);
    }, []);

    const handleRemoveAllEmojis = useCallback(() => {
        console.log('🧹 Clearing all emojis');
        setSelectedEmojis([]);
        setCursorPosition(0);
    }, []);

    const handleDone = useCallback(() => {
        onClose?.(selectedEmojis);
        DeviceEventEmitter.emit(Events.CLOSE_BOTTOM_SHEET);
    }, [selectedEmojis, onClose]);

    const renderContent = useCallback(() => {
        return (
            <Picker
                onEmojiPress={handleEmojiPress}
                imageUrl={imageUrl}
                file={file}
                testID='emoji_picker'
                selectedEmojis={selectedEmojis}
                onRemoveEmoji={handleRemoveEmoji}
                onRemoveEmojiAtPosition={handleRemoveEmojiAtPosition}
                cursorPosition={cursorPosition}
                onCursorPositionChange={handleCursorPositionChange}
                onDone={handleDone}
                showPreview={isSelectingMultiple}
            />
        );
    }, [handleEmojiPress, imageUrl, file, selectedEmojis, handleRemoveEmoji, handleRemoveEmojiAtPosition, cursorPosition, handleCursorPositionChange, handleDone, isSelectingMultiple]);

    const renderFooter = useCallback((props: any) => {
        console.log('🦶 renderFooter CALLED in EmojiPickerScreen!');
        console.log('🦶 Props being passed to PickerFooter:', {
            selectedEmojisCount: selectedEmojis.length,
            cursorPosition,
            hasHandleRemoveEmojiAtPosition: !!handleRemoveEmojiAtPosition,
            hasHandleRemoveAllEmojis: !!handleRemoveAllEmojis,
            isTablet
        });

        if (isTablet) {
            console.log('🦶 isTablet=true, returning undefined');
            return undefined;
        }

        console.log('🦶 Rendering PickerFooter with props');
        return (
            <PickerFooter
                selectedEmojis={selectedEmojis}
                cursorPosition={cursorPosition}
                onRemoveEmojiAtPosition={handleRemoveEmojiAtPosition}
                onRemoveAllEmojis={handleRemoveAllEmojis}
                {...props}
            />
        );
    }, [isTablet, selectedEmojis, cursorPosition, handleRemoveEmojiAtPosition, handleRemoveAllEmojis]);

    return (
        <BottomSheet
            renderContent={renderContent}
            closeButtonId={closeButtonId}
            componentId={componentId}
            contentStyle={style.contentStyle}
            initialSnapIndex={1}
            footerComponent={renderFooter}
            testID='post_options'
        />
    );
};

export default EmojiPickerScreen;
