import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { Dimensions, Image, Platform, Text, View } from "react-native";
import { popTopScreen, popToRoot } from "../navigation";
import useAndroidHardwareBackHandler from "@app/hooks/android_back_handler";
import { Pressable, TextInput, TouchableOpacity } from "react-native-gesture-handler";
import { useTheme } from "@app/context/theme";
import { opacity } from "react-native-reanimated/lib/typescript/Colors";
import { changeOpacity } from "@app/utils/theme";
import { typography } from "@app/utils/typography";
import { FAB, Input } from "@rneui/base";
import { useServerUrl } from "@app/context/server";
import { createStatusNew } from "@actions/remote/user";
import Loading from "@app/components/loading";
import { getServerCredentials } from "@init/credentials";
import axios from "axios";
import { PaperAirplaneIcon, PhotoIcon, XMarkIcon } from "react-native-heroicons/outline";
import { maxSatisfying } from "semver";
import { useIntl } from "react-intl";
import FilePickerUtil from "@app/utils/file/file_picker"; // Adjust the import path as necessary
import { fileMaxWarning } from "@app/utils/file";
import { useAlert } from "@app/context/alert";
// import VideoPlayer from 'react-native-video-player';
import * as RNLocalize from 'react-native-localize';
import { DEFAULT_LOCALE } from "@app/i18n";
import DocumentPicker, { type DocumentPickerResponse } from 'react-native-document-picker';
// import ImageEditor from '@thienmd/react-native-image-editor'
// import ImageEditor from 'alhashedi-react-native-image-editor' // Temporarily disabled for build fix

import DeviceInfo from "react-native-device-info";
import VideoFile from "@app/components/files/video_file";
import { Video } from "expo-av";
// import ImageEditor from '@ezz/react-native-image-editor'



type UserStatusCreationProps = {
    userID: string;

}
type ErrorHandlers = {
    [clientId: string]: (() => void) | null;
}

enum enType { text = "text", image = "image", vedio = "video" }
const UserStatusCreation = ({ userID }: UserStatusCreationProps) => {


    const onBackPress = useCallback(() => {
        popTopScreen("UserStatusCreation");
    }, ["UserStatusCreation"]);

    useAndroidHardwareBackHandler("UserStatusCreation", onBackPress);

    const windowWidth = Dimensions.get('window').width;
    const windowHeight = Dimensions.get('window').height;
    const [isLoading, setLoading] = useState(false);
    const theme = useTheme()
    const serverurl = useServerUrl();
    const [token, setToken] = useState("")
    const [isText, setIsText] = useState(true);
    const [fileType, setFileType] = useState<string | undefined>(undefined);
    const [text, setText] = useState('')
    const [file, setFile] = useState<ExtractedFileInfo | undefined>(undefined)
    const intl = useIntl();
    const { showAlert } = useAlert()
    const [localLang, setChangeLang] = useState<string>(`${DEFAULT_LOCALE}`);
    const [type, setType] = useState<enType>(enType.text)


    const getUserToken = useMemo(async () => {
        const credentials = await getServerCredentials(serverurl);
        if (credentials?.token) {
            console.log('htis from token data', credentials.token);
            setToken(credentials.token)
        }
    }, [])

    useEffect(() => {
        getUserToken
    }, [])

    useEffect(() => {
        console.log('this the file name and data', JSON.stringify(file), isText, fileType);
    }, [file])

    useEffect(() => {
        const locale = RNLocalize.getLocales()[0].languageCode;
        setChangeLang(locale);
        console.log('this the laugauage is', locale);
    }, []);



    const addFiles = async (newFiles: ExtractedFileInfo[]) => {
        try {


            if (!newFiles || newFiles.length === 0) {
                return;
            }

            const maxSizeInMB = 30;
            const maxSizeInBytes = maxSizeInMB * 1024 * 1024; // Convert MB to bytes


            for (const file of newFiles) {
                if (file.size && file.size > maxSizeInBytes) {
                    showAlert(
                        "خطء",
                        `الملف الذي تم رفعة اكبر من المتوقع يرجى تحميل ملف بحجم 
                            30MB`,
                        [{
                            text: intl.formatMessage({ id: 'mobile.post.cancel', defaultMessage: 'Cancel' }),
                            style: 'cancel',
                        }, {
                            text: intl.formatMessage({ id: 'channel_info.close', defaultMessage: 'Close' }),
                            style: 'destructive',
                            onPress: () => {

                            },
                        }], { cancelable: false },
                    );

                    return;
                }
            }

            // for (const file of newFiles) {
            //     if ((file.mime_type === "image/png" || file.mime_type === "image/jpeg" || file.mime_type === 'video/mp4') === false) {
            //         showAlert(
            //             "خطء",
            //             `صيغة الملف غير مدعومة يرجى تحميل ملف بامتداد 
            //             png او jpeg او mp4 
            //             `,
            //             [{
            //                 text: intl.formatMessage({ id: 'channel_info.close', defaultMessage: 'Close' }),
            //                 style: 'destructive',
            //                 onPress: () => {

            //                 },
            //             }], { cancelable: false },
            //         );

            //         return;
            //     }
            // }


            if (newFiles[0].mime_type.split('/')[0] === "image") {

                try {

                    const fileName = newFiles[0].localPath

                    // ImageEditor temporarily disabled for build fix
                    // TODO: Re-enable image editing functionality
                    /*
                    ImageEditor.Edit({
                        path: newFiles[0]?.localPath && fileName ? newFiles[0].localPath.substring(7, fileName.length) : '',
                        hiddenControls: ['save'],
                        onDone: (imagePath: string) => {
                            try {

                                newFiles[0].localPath = "file://" + imagePath;
                                setType(enType.image)


                            } catch (error) {
                                console.error('PhotoEditor Exception:', error);
                            }


                        },
                        onCancel: () => {
                            console.log('Editing cancelled by user.');
                        },
                        onError: (error: string) => {
                            console.error('PhotoEditor Error:', error);
                        },
                    } as any);
                    */

                    // Temporary workaround: directly set the image without editing
                    setType(enType.image);


                    // setFile

                    //picker.attachImageFromPhotoGallery();



                } catch (error) {
                    console.error('PhotoEditor Exception:', error);
                }


            } else {

                ///const fileUrl = await DeviceInfo.isEmulator ? file?.localPath : "file:///data/data" + file?.localPath?.substring("file:///data/user/0".length, file?.localPath?.length)
                // const fileUrl = await DeviceInfo.isEmulator ? file?.localPath : file?.localPath?.replace("file:///data/user/0", "file:///data/user/0")
                // newFiles[0].localPath = fileUrl;

                console.log(`\n\n\nthis  the file url  
                   
                     ${newFiles[0].localPath} \n\n\n`)

                setType(enType.vedio)
            }

            // setFileType(newFiles[0].mime_type)
            // setFile(newFiles[0])
            // setIsText(false)

            // if(await DeviceInfo.isEmulator() === false){
            //     newFiles[0].localPath= newFiles[0].localPath?.replace("file:///data/user/0","file:///data/data")
            // }

            console.log(`\n\n\nthis  the file url  
                   
                     ${newFiles[0].localPath} \n\n\n`)
            setFile(newFiles[0]);
            setFileType(newFiles[0].mime_type)

            setIsText(false)
            // sendingStatus()

        } catch (error) {
            console.log(`\n\n\nthis the error from adding file ${error} \n\n\n`)
        }
    }

    useEffect(() => {
        console.log(`\n\n\nthis the file data is ${JSON.stringify(file)}\n\n\n`)

    }, [file])

    const handleButtonPress = (isVedio: boolean = true) => {
        try {
            const picker = new FilePickerUtil(intl, addFiles);
            switch (isVedio) {
                case false: {
                    picker.attachFileFromCamera()
                    break;
                }
                default: {
                    picker.attachFileFromCamera({
                        quality: 0.8,
                        videoQuality: 'high',
                        mediaType: 'video',
                        saveToPhotos: true,
                    })

                    break;
                }
            }
            // picker.attachFileFromPhotoGallery(1);

        } catch (err) {
            console.log(`\n\n\n\nthis error from attach file from photogallery${err}\n\n\n`)
        }
    };

    const handleButtonPressGalary = (isVedio: boolean = true) => {
        try {
            const picker = new FilePickerUtil(intl,
                addFiles);

            picker.attachFileFromPhotoGallery(1);

        } catch (err) {
            console.log(`\n\n\n\nthis error from attach file from photogallery${err}\n\n\n`)
        }
    };



    const sendingStatus = async () => {

        console.log(`\n\nthis the text data ${(text === '' || file === undefined)}\n\n`)
        if (((isText && text === '') || (isText === false && file === undefined))) {
            showAlert(
                "خطء",
                `لابد من ادخال نص او اختيار ملف لعمل حالة`,
                [{
                    text: intl.formatMessage({ id: 'channel_info.close', defaultMessage: 'Close' }),
                    style: 'destructive',
                    onPress: () => {

                    },
                }], { cancelable: false },
            );
            return;
        }



        setLoading(true);
        const formData = new FormData();
        if (isText === false) {
            const typeHolder = file?.mime_type.split('/')[0];

            if (typeHolder === "image") {
                console.log(`\n\nthis the from type image \n\n`)
                setType(enType.image)
            } else {
                console.log(`\n\nthis the from type vedio \n\n`)

                setType(enType.vedio)
            }

            formData.append('type', type.toString())
        } else formData.append('type', enType.text);

        if (isText) {
            formData.append("text", text);


        } else if (file !== undefined) {
            console.log(`\n\nThis is the file being sent: ${JSON.stringify(file)}\n\n`);

            // Ensure the file is properly created
            try {
                // Handle Android and iOS file paths properly
                //const fileUri =  file.localPath.replace('file://', '')//file.localPath;
                const fileUri = file.localPath//file.localPath;
                const fileType = file.mime_type;

                // Create the file object
                const fileData = {
                    uri: fileUri,
                    type: fileType,
                    name: file.name,
                };

                // Append file to FormData
                formData.append("file", fileData);
            } catch (error) {
                console.error("Error processing the file:", error);
                setLoading(false);
                return; // Stop the process if file processing fails
            }
        }

        console.log(`\n\nThis is the file  type : ${JSON.stringify(formData)}\n\n`);


        try {
            console.log(`\n\nThis is the file being sent: ${JSON.stringify(formData)}\n\n`);

            // Make the API call
            await axios.post(
                `${serverurl}/plugins/com.example.status/api/v1/users/${userID}/status`,
                formData,
                {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "multipart/form-data", // Explicitly set content type
                        "Accept": "*/*", // Accept header, adjust as necessary
                    },
                }
            )
            popToRoot();

        } catch (error) {
            showAlert(
                "مشكلة",
                `هناك مشكلة في الاتصال او خلل في الخدمة`,
                [{
                    text: intl.formatMessage({ id: 'channel_info.close', defaultMessage: 'Close' }),
                    style: 'destructive',
                    onPress: () => {

                    },
                }], { cancelable: false },
            );
            console.log(`\n\nError from server: ${JSON.stringify(error)}\n\n`);
        } finally {
            setLoading(false); // Reset loading state
        }
    };



    const handleBody = () => {
        switch (isText) {
            case true: return (

                <TextInput
                    onChange={(event) => {
                        const { text } = event.nativeEvent;
                        setText(text)
                    }}
                    style={{
                        ...typography("Body", 1000, "Light"),
                        color: "white",
                        marginBottom: 50,
                        width: windowWidth,
                        marginTop: '35%'
                    }}
                    maxLength={100}
                    numberOfLines={5}
                    placeholderTextColor={changeOpacity(theme.centerChannelBg, 0.40)}
                    textAlign="center"
                    multiline={true}
                    value={text}
                    placeholder="اكتب حالة"
                    underlineColorAndroid={'transparent'} />

            )
            default: {
                switch (fileType) {
                    case "image/png":
                    case "image/jpeg": {
                        return (
                            <Image
                                style={{
                                    marginTop: "50%"
                                }}
                                resizeMode="cover"
                                height={windowHeight - 360}
                                width={windowWidth}
                                source={{ uri: file?.localPath }} />
                            // <View style={{ height: windowHeight,
                            //     width: windowWidth,
                            //      backgroundColor: 'black',

                            //      }} />
                        )
                    }
                    case 'video/mp4': {
                        console.log(`\n\nthis shown the image url is setting\n\n`)
                        //     return  <VideoFile
                        //     isFromPostList={ false}
                        //     file={{
                        //         name: file?.name ?? '',
                        //         extension: file?.extension ?? '',
                        //         has_preview_image: file?.has_preview_image ?? false,
                        //         localPath:file?.localPath??"",
                        //         height: file?.height??0,
                        //         mime_type: file?.mime_type??"",
                        //         size: file?.size??0,
                        //         width: file?.width??0,
                        //         user_id:userID
                        //     }}
                        //     forwardRef={undefined}
                        //     inViewPort={false}
                        //     isSingleImage={true}
                        //     contentFit={'cover'}
                        //     wrapperWidth={windowWidth}
                        //     updateFileForGallery={undefined}
                        //     index={0}
                        // />
                        // return <Image
                        //         style={{
                        //             marginTop: "50%"
                        //         }}
                        //         resizeMode="cover"
                        //         height={windowHeight - 360}
                        //         width={windowWidth}
                        //         source={{ uri: file?.localPath }} />
                        return <View style={{
                            marginTop: "50%",
                            height: windowHeight - 360,
                            width: windowWidth,
                            backgroundColor: 'black',
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                            <Video
                                style={{
                                    height: windowHeight - 360,
                                    width: windowWidth,
                                    backgroundColor: 'black'
                                }}
                                useNativeControls={true}
                                source={{
                                    uri: file?.localPath ?? "",
                                }}
                                onError={(e) => console.log(e)}
                                shouldPlay={true}
                            />
                        </View>

                    }
                    default: return null;

                }

            };
        }
    }




    return (
        <View>
            <View style={{
                height: windowHeight - 70,
                width: windowWidth,
                position: 'relative',
                flexDirection: 'row',
                backgroundColor: "#00987e"
            }}>
                <TouchableOpacity style={{

                    start: 15,
                    top: 5,
                    height: 40,
                    width: 40,
                    backgroundColor: "#017965",
                    borderRadius: 25,
                    alignItems: 'center',
                    justifyContent: 'center',
                    //paddingEnd: 5,
                    // marginEnd: 5,
                    position: 'absolute'

                }}
                    onPress={() => popToRoot()}>
                    {
                        <XMarkIcon
                            style={{
                                //start: localLang === 'en' ? 3 : 0,
                                //end: localLang === 'en' ? 3 : 0,
                                transform: [
                                    { rotate: localLang === 'en' ? '180deg' : '0deg' }, // Rotate the icon 45 degrees
                                ],
                            }}
                            //source={require("assets/base/images/send.png")}
                            color={"white"}
                            size={22}
                        />
                    }
                </TouchableOpacity>

                <TouchableOpacity style={{

                    start: windowWidth - 50,
                    bottom: 25,
                    height: 40,
                    width: 40,
                    backgroundColor: "#017965",
                    borderRadius: 5,
                    alignItems: 'center',
                    justifyContent: 'center',
                    //paddingEnd: 5,
                    // marginEnd: 5,
                    position: 'absolute'

                }}
                    onPress={() => {

                        setIsText(prev => prev = false)
                        handleButtonPressGalary()

                    }}>
                    {
                        <PhotoIcon
                            style={{
                                //  start: localLang === 'en' ? 3 : 0,
                                // end: localLang === 'en' ? 3 : 0,
                                transform: [
                                    { rotate: localLang === 'en' ? '180deg' : '0deg' }, // Rotate the icon 45 degrees
                                ],
                            }}
                            //source={require("assets/base/images/send.png")}
                            color={"white"}
                            size={28}
                        />
                    }
                </TouchableOpacity>

                <View style={{
                    // position:'absolute'
                }}>

                    {handleBody()}
                </View>
            </View>

            <View style={{
                height: 90,
                flexDirection: 'row',
                // marginTop: 8,
                justifyContent: 'space-between',
                backgroundColor: "#008971",
                paddingTop: 14,
                bottom: 19
            }}>

                <View style={{
                    flexDirection: 'row',
                    paddingStart: 15
                }}>

                    <TouchableOpacity
                        onPress={() => {
                            setIsText(prev => prev = true)
                            setType(prev => prev = enType.text)
                        }}
                        style={{
                            width: 70,
                            height: 35,
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: 20,
                            backgroundColor: type === enType.text ? "#299c88" : undefined,
                            paddingBottom: 5

                        }}>
                        <Text
                            style={{ ...typography("Body", 200, "Light"), color: "white" }}
                        >
                            {'نص'}
                        </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        onPress={() => {
                            // handleButtonPress()
                            setIsText(prev => prev = false)
                            handleButtonPress(false)
                        }}
                        style={{
                            marginStart: 5,
                            width: 90,
                            height: 40,
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: 20,
                            backgroundColor: type === enType.image ? "#299c88" : undefined,
                            paddingBottom: 5

                        }}>
                        <Text
                            style={{
                                ...typography("Body", 200, "Light"),
                                color: "white"
                            }}
                        >
                            {'صورة'}
                        </Text>
                    </TouchableOpacity>


                    <TouchableOpacity
                        onPress={() => {
                            setIsText(prev => prev = false)
                            handleButtonPress()
                        }}
                        style={{
                            marginStart: 5,
                            width: 90,
                            height: 40,
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: 20,
                            backgroundColor: type === enType.vedio ? "#299c88" : undefined,
                            paddingBottom: 5

                        }}>
                        <Text
                            style={{
                                ...typography("Body", 200, "Light"),
                                color: "white"
                            }}
                        >
                            {'فيديو'}
                        </Text>
                    </TouchableOpacity>

                </View>

                <TouchableOpacity style={{

                    bottom: 0,
                    height: 40,
                    width: 40,
                    backgroundColor: theme.buttonBg,
                    borderRadius: 25,
                    alignItems: 'center',
                    justifyContent: 'center',

                    marginEnd: 15,

                }}
                    onPress={sendingStatus}

                >
                    {isLoading ? <Loading color="white" size={35} /> : <PaperAirplaneIcon
                        style={{
                            start: localLang === 'en' ? 3 : 0,
                            end: localLang === 'en' ? 3 : 0,
                            transform: [
                                { rotate: '180deg' }, // Rotate the icon 45 degrees
                            ],
                        }}
                        //source={require("assets/base/images/send.png")}
                        color={"white"}
                        size={25}
                    />}
                </TouchableOpacity>
            </View>


        </View>
    )
}

export default UserStatusCreation;
